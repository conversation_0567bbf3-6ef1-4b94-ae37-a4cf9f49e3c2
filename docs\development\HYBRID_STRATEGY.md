# 基于老项目的精简重构策略

## 核心思路
保留老项目的核心价值（领域知识、数据处理经验），
去除复杂性（减少模块数量、简化架构），
利用新AI能力解决关键卡点。

## 保留的核心资产
1. **规则配置**：loudi.yaml的完整规则体系
2. **数据处理经验**：视频分段、SQLite数据湖等成熟方案
3. **领域知识**：区域分配的深度理解和历史教训
4. **测试数据**：已有的视频素材和标注数据

## 精简策略
1. **模块数量**：从100个减少到15-20个核心模块
2. **架构简化**：保持三层架构，但去除过度抽象
3. **依赖简化**：使用成熟开源库替代自建组件

## 关键突破点：区域状态分配
基于老项目的经验教训，设计新的解决方案：

### 老项目的问题
- 硬编码区域边界
- 缺乏动态适应能力
- 状态转换逻辑过于复杂

### 新解决方案
```python
class SmartRegionAssigner:
    def __init__(self):
        # 继承老项目的区域定义
        self.region_templates = self.load_from_old_project()
        # 新增：AI辅助的动态调整
        self.ai_adjuster = RegionAIAdjuster()
    
    def assign_region(self, detection, game_context):
        # 1. 使用老项目的模板匹配
        template_result = self.template_match(detection)
        
        # 2. AI验证和调整
        ai_result = self.ai_adjuster.verify_and_adjust(
            template_result, game_context
        )
        
        # 3. 规则约束验证
        final_result = self.rule_validate(ai_result)
        
        return final_result
```

## 实施计划
### 第一阶段：资产迁移（3天）
- 提取老项目的核心配置和数据处理逻辑
- 重构为简化的模块结构
- 建立新的测试框架

### 第二阶段：关键突破（5天）
- 重新设计区域分配算法
- 集成AI辅助决策
- 解决历史卡点问题

### 第三阶段：集成优化（4天）
- 端到端测试
- 性能优化
- 文档完善