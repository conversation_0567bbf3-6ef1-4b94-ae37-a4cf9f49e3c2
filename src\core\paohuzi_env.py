import numpy as np
from rlcard.envs import Env
from rlcard.games.base import Card

class PaohuziEnv(Env):
    """
    跑胡子游戏环境
    基于RLCard框架实现
    """
    
    def __init__(self, config=None):
        """
        初始化跑胡子环境
        """
        # 基本参数
        self.name = 'paohuzi'
        self.num_players = 2  # 默认2人对战
        self.num_cards = 80  # 跑胡子共80张牌
        self.num_actions = 5  # 基本动作：出牌、碰、吃、胡、过
        
        # 状态空间形状
        self.state_shape = [self.num_cards + 4 * self.num_cards]  # 基本状态 + 组合状态
        
        # 动作空间
        self.action_space = ['play', 'peng', 'chi', 'hu', 'pass']
        
        # 初始化游戏状态
        self.game = None
        self.game_config = config or {}
        
        # 初始化父类
        # 注意：RLCard的Env类需要特定格式的配置
        rlcard_config = {}
        rlcard_config['game_num_players'] = self.num_players
        rlcard_config['game'] = self.name
        rlcard_config['allow_step_back'] = self.game_config.get('allow_step_back', True)
        
        super().__init__(rlcard_config)
    
    def reset(self):
        """
        重置游戏环境
        
        Returns:
            dict: 初始状态
        """
        # 初始化游戏
        self.game = PaohuziGame(self.game_config)
        
        # 发牌
        self.game.init_game()
        
        # 获取初始状态
        state = self.game.get_state(self.game.current_player)
        
        return state
    
    def step(self, action):
        """
        执行动作
        
        Args:
            action (int): 动作ID
            
        Returns:
            tuple: (下一状态, 奖励, 是否结束, 信息)
        """
        if self.game is None:
            return {}, 0, True, {}
            
        # 执行动作
        next_state, reward, done = self.game.step(action)
        
        # 返回结果
        return next_state, reward, done, {}
    
    def get_legal_actions(self):
        """
        获取合法动作
        
        Returns:
            list: 合法动作列表
        """
        return self.game.get_legal_actions() if self.game else []
    
    def get_state(self, player_id):
        """
        获取玩家状态
        
        Args:
            player_id (int): 玩家ID
            
        Returns:
            dict: 玩家状态
        """
        return self.game.get_state(player_id) if self.game else {}


class PaohuziGame:
    """
    跑胡子游戏逻辑
    """
    
    def __init__(self, config=None):
        """
        初始化游戏
        """
        self.num_players = 2
        self.num_cards = 80
        
        # 牌组构成: 一到十，壹到拾，每种4张
        self.cards = []
        for card_type in range(1, 11):  # 1-10
            for _ in range(4):
                self.cards.append(Card(card_type, 0))  # 小写
                self.cards.append(Card(card_type, 1))  # 大写
        
        # 游戏状态
        self.current_player = 0
        self.players = [PaohuziPlayer(player_id=i) for i in range(self.num_players)]
        self.dealer_id = 0  # 庄家ID
        self.round = 0
        self.is_over = False
        self.winner = None
        
        # 底牌
        self.remaining_cards = []
    
    def init_game(self):
        """
        初始化游戏，发牌
        
        Returns:
            dict: 初始状态
        """
        # 洗牌
        np.random.shuffle(self.cards)
        
        # 随机去除20张牌（2人对战规则）
        removed_cards = self.cards[:20]
        self.cards = self.cards[20:]
        
        # 发牌：每人20张，庄家多1张
        for i in range(self.num_players):
            if i == self.dealer_id:
                self.players[i].hand = self.cards[:21]
                self.cards = self.cards[21:]
            else:
                self.players[i].hand = self.cards[:20]
                self.cards = self.cards[20:]
        
        # 剩余牌作为底牌
        self.remaining_cards = self.cards
        
        # 获取初始状态
        return self.get_state(self.current_player)
    
    def step(self, action):
        """
        执行动作
        
        Args:
            action (int): 动作ID
            
        Returns:
            tuple: (下一状态, 奖励, 是否结束)
        """
        # TODO: 实现具体动作执行逻辑
        
        # 临时实现：随机结果
        done = np.random.random() < 0.05  # 5%概率结束
        reward = 0
        
        if done:
            self.is_over = True
            self.winner = self.current_player
            reward = 1
        else:
            # 切换玩家
            self.current_player = (self.current_player + 1) % self.num_players
        
        return self.get_state(self.current_player), reward, done
    
    def get_state(self, player_id):
        """
        获取玩家状态
        
        Args:
            player_id (int): 玩家ID
            
        Returns:
            dict: 玩家状态
        """
        state = {
            'hand': self.players[player_id].hand.copy(),
            'current_player': self.current_player,
            'dealer': self.dealer_id,
            'played_cards': [p.played_cards.copy() for p in self.players],
            'peng_cards': [p.peng_cards.copy() for p in self.players],
            'chi_cards': [p.chi_cards.copy() for p in self.players],
            'remaining_cards': len(self.remaining_cards),
            'legal_actions': self.get_legal_actions()
        }
        
        return state
    
    def get_legal_actions(self):
        """
        获取合法动作
        
        Returns:
            list: 合法动作列表
        """
        # TODO: 实现具体合法动作判断逻辑
        
        # 临时实现：所有动作都合法
        return list(range(5))  # 出牌、碰、吃、胡、过


class PaohuziPlayer:
    """
    跑胡子玩家
    """
    
    def __init__(self, player_id):
        """
        初始化玩家
        
        Args:
            player_id (int): 玩家ID
        """
        self.player_id = player_id
        self.hand = []  # 手牌
        self.played_cards = []  # 打出的牌
        self.peng_cards = []  # 碰的牌
        self.chi_cards = []  # 吃的牌
        self.hu = False  # 是否胡牌 