#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
深度标注差异分析器

详细分析生成的JSON与原始JSON的差异，
找出标签错误、区域状态错误、数字孪生系统问题的具体原因。
"""

import os
import json
import cv2
import numpy as np
from typing import Dict, List, Any, Tuple
from collections import defaultdict, Counter
import matplotlib.pyplot as plt
import seaborn as sns


class DeepAnnotationAnalyzer:
    """深度标注差异分析器"""
    
    def __init__(self, 
                 original_path: str = "legacy_assets/ceshi/calibration_gt/labels",
                 enhanced_path: str = "legacy_assets/ceshi/calibration_gt_enhanced/labels",
                 images_path: str = "legacy_assets/ceshi/calibration_gt/images"):
        """
        初始化分析器
        
        Args:
            original_path: 原始标注路径
            enhanced_path: 增强标注路径
            images_path: 图像路径
        """
        self.original_path = original_path
        self.enhanced_path = enhanced_path
        self.images_path = images_path
        
        self.analysis_results = {
            'label_mapping_errors': [],
            'region_assignment_errors': [],
            'owner_assignment_errors': [],
            'position_errors': [],
            'missing_detections': [],
            'false_detections': [],
            'digital_twin_issues': []
        }
        
        print(f"🔍 深度标注差异分析器初始化")
        print(f"   - 原始标注: {original_path}")
        print(f"   - 增强标注: {enhanced_path}")
    
    def analyze_comprehensive_differences(self, sample_size: int = 50) -> Dict[str, Any]:
        """全面分析差异"""
        print(f"🚀 开始深度差异分析 (样本: {sample_size})...")
        
        # 获取共同文件
        original_files = set(f for f in os.listdir(self.original_path) if f.endswith('.json'))
        enhanced_files = set(f for f in os.listdir(self.enhanced_path) if f.endswith('.json'))
        common_files = list(original_files & enhanced_files)[:sample_size]
        
        print(f"📋 分析 {len(common_files)} 个文件...")
        
        # 分析每个文件
        for i, filename in enumerate(common_files):
            try:
                self._analyze_single_file(filename)
                
                if (i + 1) % 10 == 0:
                    progress = (i + 1) / len(common_files) * 100
                    print(f"   进度: {i+1}/{len(common_files)} ({progress:.1f}%)")
                    
            except Exception as e:
                print(f"❌ 分析失败: {filename} - {e}")
        
        # 生成分析报告
        report = self._generate_analysis_report()
        
        print(f"✅ 深度分析完成")
        return report
    
    def _analyze_single_file(self, filename: str) -> None:
        """分析单个文件"""
        # 读取原始和增强标注
        original_data = self._load_json(os.path.join(self.original_path, filename))
        enhanced_data = self._load_json(os.path.join(self.enhanced_path, filename))
        
        # 提取检测结果
        original_detections = self._extract_detections_detailed(original_data)
        enhanced_detections = self._extract_detections_detailed(enhanced_data)
        
        # 匹配检测结果
        matches, unmatched_original, unmatched_enhanced = self._match_detections_detailed(
            original_detections, enhanced_detections
        )
        
        # 分析各类错误
        self._analyze_label_mapping_errors(matches, filename)
        self._analyze_region_assignment_errors(matches, filename)
        self._analyze_owner_assignment_errors(matches, filename)
        self._analyze_position_errors(matches, filename)
        self._analyze_missing_detections(unmatched_original, filename)
        self._analyze_false_detections(unmatched_enhanced, filename)
        self._analyze_digital_twin_issues(enhanced_data, filename)
    
    def _load_json(self, filepath: str) -> Dict[str, Any]:
        """加载JSON文件"""
        with open(filepath, 'r', encoding='utf-8') as f:
            return json.load(f)
    
    def _extract_detections_detailed(self, data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """提取详细检测信息"""
        detections = []
        
        for i, shape in enumerate(data.get('shapes', [])):
            points = shape.get('points', [])
            if len(points) == 4:
                # 转换为[x, y, w, h]格式
                xs = [p[0] for p in points]
                ys = [p[1] for p in points]
                x, y = min(xs), min(ys)
                w, h = max(xs) - x, max(ys) - y
                
                detection = {
                    'index': i,
                    'label': shape.get('label', ''),
                    'bbox': [x, y, w, h],
                    'confidence': shape.get('score', 1.0),
                    'region_name': shape.get('region_name', ''),
                    'owner': shape.get('owner', ''),
                    'attributes': shape.get('attributes', {}),
                    'group_id': shape.get('group_id', i+1),
                    'center': [x + w/2, y + h/2],
                    'area': w * h
                }
                detections.append(detection)
        
        return detections
    
    def _match_detections_detailed(self, original: List[Dict], enhanced: List[Dict]) -> Tuple[List[Dict], List[Dict], List[Dict]]:
        """详细匹配检测结果"""
        matches = []
        used_enhanced = set()
        
        for orig in original:
            best_match = None
            best_iou = 0
            best_idx = -1
            
            for i, enh in enumerate(enhanced):
                if i in used_enhanced:
                    continue
                
                iou = self._calculate_iou(orig['bbox'], enh['bbox'])
                if iou > best_iou and iou > 0.3:  # 降低IoU阈值以捕获更多匹配
                    best_iou = iou
                    best_match = enh
                    best_idx = i
            
            if best_match:
                used_enhanced.add(best_idx)
                matches.append({
                    'original': orig,
                    'enhanced': best_match,
                    'iou': best_iou
                })
        
        # 未匹配的检测
        unmatched_original = [det for det in original if not any(m['original'] == det for m in matches)]
        unmatched_enhanced = [enhanced[i] for i in range(len(enhanced)) if i not in used_enhanced]
        
        return matches, unmatched_original, unmatched_enhanced
    
    def _calculate_iou(self, bbox1: List[float], bbox2: List[float]) -> float:
        """计算IoU"""
        try:
            x1, y1, w1, h1 = bbox1
            x2, y2, w2, h2 = bbox2
            
            # 转换为 [x1, y1, x2, y2] 格式
            box1 = [x1, y1, x1 + w1, y1 + h1]
            box2 = [x2, y2, x2 + w2, y2 + h2]
            
            # 计算交集
            x_left = max(box1[0], box2[0])
            y_top = max(box1[1], box2[1])
            x_right = min(box1[2], box2[2])
            y_bottom = min(box1[3], box2[3])
            
            if x_right < x_left or y_bottom < y_top:
                return 0.0
            
            intersection = (x_right - x_left) * (y_bottom - y_top)
            
            # 计算并集
            area1 = w1 * h1
            area2 = w2 * h2
            union = area1 + area2 - intersection
            
            return intersection / union if union > 0 else 0.0
        
        except (ValueError, IndexError, ZeroDivisionError):
            return 0.0
    
    def _analyze_label_mapping_errors(self, matches: List[Dict], filename: str) -> None:
        """分析标签映射错误"""
        for match in matches:
            orig_label = match['original']['label']
            enh_label = match['enhanced']['label']
            
            if orig_label != enh_label:
                self.analysis_results['label_mapping_errors'].append({
                    'file': filename,
                    'original_label': orig_label,
                    'enhanced_label': enh_label,
                    'iou': match['iou'],
                    'position': match['original']['center'],
                    'confidence': match['enhanced']['confidence']
                })
    
    def _analyze_region_assignment_errors(self, matches: List[Dict], filename: str) -> None:
        """分析区域分配错误"""
        for match in matches:
            orig_region = match['original']['region_name']
            enh_region = match['enhanced']['region_name']
            
            if orig_region != enh_region:
                self.analysis_results['region_assignment_errors'].append({
                    'file': filename,
                    'original_region': orig_region,
                    'enhanced_region': enh_region,
                    'position': match['original']['center'],
                    'label': match['original']['label']
                })
    
    def _analyze_owner_assignment_errors(self, matches: List[Dict], filename: str) -> None:
        """分析所有者分配错误"""
        for match in matches:
            orig_owner = match['original']['owner']
            enh_owner = match['enhanced']['owner']
            
            if orig_owner != enh_owner:
                self.analysis_results['owner_assignment_errors'].append({
                    'file': filename,
                    'original_owner': orig_owner,
                    'enhanced_owner': enh_owner,
                    'region': match['original']['region_name'],
                    'position': match['original']['center']
                })
    
    def _analyze_position_errors(self, matches: List[Dict], filename: str) -> None:
        """分析位置错误"""
        for match in matches:
            if match['iou'] < 0.7:  # 位置误差阈值
                self.analysis_results['position_errors'].append({
                    'file': filename,
                    'iou': match['iou'],
                    'original_bbox': match['original']['bbox'],
                    'enhanced_bbox': match['enhanced']['bbox'],
                    'label': match['original']['label']
                })
    
    def _analyze_missing_detections(self, unmatched_original: List[Dict], filename: str) -> None:
        """分析漏检"""
        for detection in unmatched_original:
            self.analysis_results['missing_detections'].append({
                'file': filename,
                'label': detection['label'],
                'region': detection['region_name'],
                'position': detection['center'],
                'area': detection['area']
            })
    
    def _analyze_false_detections(self, unmatched_enhanced: List[Dict], filename: str) -> None:
        """分析误检"""
        for detection in unmatched_enhanced:
            self.analysis_results['false_detections'].append({
                'file': filename,
                'label': detection['label'],
                'region': detection['region_name'],
                'position': detection['center'],
                'confidence': detection['confidence'],
                'recovered': detection['attributes'].get('recovered', False)
            })
    
    def _analyze_digital_twin_issues(self, enhanced_data: Dict[str, Any], filename: str) -> None:
        """分析数字孪生问题"""
        metadata = enhanced_data.get('enhanced_metadata', {})
        
        # 检查数字孪生数量
        digital_twins_count = metadata.get('digital_twins_count', 0)
        shapes_count = len(enhanced_data.get('shapes', []))
        
        if digital_twins_count == 0 and shapes_count > 0:
            self.analysis_results['digital_twin_issues'].append({
                'file': filename,
                'issue': 'no_digital_twins',
                'shapes_count': shapes_count,
                'metadata': metadata
            })
    
    def _generate_analysis_report(self) -> Dict[str, Any]:
        """生成分析报告"""
        # 统计各类错误
        label_error_stats = self._analyze_label_error_patterns()
        region_error_stats = self._analyze_region_error_patterns()
        owner_error_stats = self._analyze_owner_error_patterns()
        
        report = {
            'error_summary': {
                'label_mapping_errors': len(self.analysis_results['label_mapping_errors']),
                'region_assignment_errors': len(self.analysis_results['region_assignment_errors']),
                'owner_assignment_errors': len(self.analysis_results['owner_assignment_errors']),
                'position_errors': len(self.analysis_results['position_errors']),
                'missing_detections': len(self.analysis_results['missing_detections']),
                'false_detections': len(self.analysis_results['false_detections']),
                'digital_twin_issues': len(self.analysis_results['digital_twin_issues'])
            },
            'error_patterns': {
                'label_mapping': label_error_stats,
                'region_assignment': region_error_stats,
                'owner_assignment': owner_error_stats
            },
            'detailed_analysis': self.analysis_results,
            'root_causes': self._identify_root_causes(),
            'fix_recommendations': self._generate_fix_recommendations()
        }
        
        # 保存报告
        report_path = "output/deep_annotation_analysis_report.json"
        os.makedirs(os.path.dirname(report_path), exist_ok=True)
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        return report
    
    def _analyze_label_error_patterns(self) -> Dict[str, Any]:
        """分析标签错误模式"""
        errors = self.analysis_results['label_mapping_errors']
        
        # 统计错误映射
        error_mappings = Counter()
        for error in errors:
            mapping = f"{error['original_label']} -> {error['enhanced_label']}"
            error_mappings[mapping] += 1
        
        # 分析最常见的错误
        common_errors = error_mappings.most_common(10)
        
        return {
            'total_errors': len(errors),
            'unique_mappings': len(error_mappings),
            'most_common_errors': common_errors,
            'error_rate_by_confidence': self._analyze_error_by_confidence(errors)
        }
    
    def _analyze_region_error_patterns(self) -> Dict[str, Any]:
        """分析区域错误模式"""
        errors = self.analysis_results['region_assignment_errors']
        
        # 统计区域映射错误
        region_mappings = Counter()
        for error in errors:
            mapping = f"{error['original_region']} -> {error['enhanced_region']}"
            region_mappings[mapping] += 1
        
        return {
            'total_errors': len(errors),
            'unique_mappings': len(region_mappings),
            'most_common_errors': region_mappings.most_common(10)
        }
    
    def _analyze_owner_error_patterns(self) -> Dict[str, Any]:
        """分析所有者错误模式"""
        errors = self.analysis_results['owner_assignment_errors']
        
        # 统计所有者映射错误
        owner_mappings = Counter()
        for error in errors:
            mapping = f"{error['original_owner']} -> {error['enhanced_owner']}"
            owner_mappings[mapping] += 1
        
        return {
            'total_errors': len(errors),
            'unique_mappings': len(owner_mappings),
            'most_common_errors': owner_mappings.most_common(10)
        }
    
    def _analyze_error_by_confidence(self, errors: List[Dict]) -> Dict[str, float]:
        """按置信度分析错误"""
        if not errors:
            return {}
        
        high_conf_errors = sum(1 for e in errors if e['confidence'] > 0.8)
        med_conf_errors = sum(1 for e in errors if 0.5 <= e['confidence'] <= 0.8)
        low_conf_errors = sum(1 for e in errors if e['confidence'] < 0.5)
        
        total = len(errors)
        
        return {
            'high_confidence_error_rate': high_conf_errors / total if total > 0 else 0,
            'medium_confidence_error_rate': med_conf_errors / total if total > 0 else 0,
            'low_confidence_error_rate': low_conf_errors / total if total > 0 else 0
        }
    
    def _identify_root_causes(self) -> List[str]:
        """识别根本原因"""
        causes = []
        
        # 标签映射问题
        if len(self.analysis_results['label_mapping_errors']) > 50:
            causes.append("标签映射存在系统性问题，可能是模型训练数据与实际标签不匹配")
        
        # 区域分配问题
        if len(self.analysis_results['region_assignment_errors']) > 30:
            causes.append("区域分配逻辑存在问题，可能是坐标判断逻辑与原始标注不一致")
        
        # 数字孪生问题
        if len(self.analysis_results['digital_twin_issues']) > 10:
            causes.append("数字孪生系统未正确初始化或状态管理存在问题")
        
        # 检测问题
        missing_rate = len(self.analysis_results['missing_detections'])
        false_rate = len(self.analysis_results['false_detections'])
        
        if missing_rate > 20:
            causes.append("检测模型存在漏检问题，可能需要调整检测阈值")
        
        if false_rate > 20:
            causes.append("检测模型存在误检问题，可能是置信度阈值设置过低")
        
        return causes
    
    def _generate_fix_recommendations(self) -> List[Dict[str, str]]:
        """生成修复建议"""
        recommendations = []
        
        # 标签映射修复
        if len(self.analysis_results['label_mapping_errors']) > 0:
            recommendations.append({
                'category': 'label_mapping',
                'priority': 'high',
                'recommendation': '检查YOLO模型的类别映射，确保与原始标注的标签一致'
            })
        
        # 区域分配修复
        if len(self.analysis_results['region_assignment_errors']) > 0:
            recommendations.append({
                'category': 'region_assignment',
                'priority': 'high',
                'recommendation': '重新实现区域分配逻辑，参考原始标注的区域判断方法'
            })
        
        # 数字孪生修复
        if len(self.analysis_results['digital_twin_issues']) > 0:
            recommendations.append({
                'category': 'digital_twin',
                'priority': 'medium',
                'recommendation': '修复数字孪生系统的初始化和状态管理逻辑'
            })
        
        return recommendations


def main():
    """主函数"""
    print("🔍 深度标注差异分析器")
    print("=" * 50)
    
    # 创建分析器
    analyzer = DeepAnnotationAnalyzer()
    
    # 进行深度分析
    report = analyzer.analyze_comprehensive_differences(sample_size=50)
    
    # 打印结果
    print("\n📊 错误统计:")
    for error_type, count in report['error_summary'].items():
        print(f"   {error_type}: {count}")
    
    print("\n🔍 根本原因:")
    for cause in report['root_causes']:
        print(f"   • {cause}")
    
    print("\n💡 修复建议:")
    for rec in report['fix_recommendations']:
        print(f"   [{rec['priority'].upper()}] {rec['recommendation']}")
    
    print(f"\n📁 详细报告已保存: output/deep_annotation_analysis_report.json")


if __name__ == "__main__":
    main()
