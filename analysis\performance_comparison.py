"""
性能对比分析脚本

对比分析不同版本区域分类器的性能，总结改进效果和剩余问题。

对比版本：
1. 原始版本（72.0%区域准确率）
2. V2.0版本（91.4%区域准确率）
3. 多算法融合版本（90.0%区域准确率）
"""

import json
import numpy as np
from pathlib import Path
from typing import Dict, List, Any, Tuple
from collections import Counter
import matplotlib.pyplot as plt

class PerformanceComparator:
    """性能对比分析器"""
    
    def __init__(self):
        self.validation_reports = {}
        self.load_validation_reports()
    
    def load_validation_reports(self):
        """加载所有验证报告"""
        reports_dir = Path("tests")
        
        # 查找所有验证报告
        report_files = {
            "original": "region_validation_report_20250717_073607.json",
            "v2": "enhanced_system_validation_20250717_075841.json", 
            "multi_algorithm": "multi_algorithm_validation_20250717_082150.json"
        }
        
        for version, filename in report_files.items():
            report_path = reports_dir / filename
            if report_path.exists():
                try:
                    with open(report_path, 'r', encoding='utf-8') as f:
                        self.validation_reports[version] = json.load(f)
                    print(f"✅ 加载{version}版本报告: {filename}")
                except Exception as e:
                    print(f"❌ 加载{version}版本报告失败: {e}")
            else:
                print(f"⚠️ 未找到{version}版本报告: {filename}")
    
    def extract_performance_metrics(self) -> Dict[str, Dict[str, float]]:
        """提取性能指标"""
        metrics = {}
        
        for version, report in self.validation_reports.items():
            if version == "original":
                # 原始版本的数据结构
                stats = report.get("overall_statistics", {})
                metrics[version] = {
                    "region_accuracy": stats.get("overall_accuracy", 0),
                    "id_accuracy": 0.491,  # 从之前的分析得出
                    "total_cards": stats.get("total_cards_validated", 0),
                    "sequences": stats.get("sequences_validated", 0)
                }
            else:
                # V2.0和多算法版本的数据结构
                stats = report.get("overall_statistics", {})
                metrics[version] = {
                    "region_accuracy": stats.get("overall_region_accuracy", 0),
                    "id_accuracy": stats.get("overall_id_accuracy", 0),
                    "total_cards": stats.get("total_cards_validated", 0),
                    "sequences": stats.get("sequences_validated", 0)
                }
        
        return metrics
    
    def analyze_improvement_trends(self, metrics: Dict[str, Dict[str, float]]) -> Dict[str, Any]:
        """分析改进趋势"""
        print("\n📈 性能改进趋势分析")
        print("=" * 40)
        
        versions = ["original", "v2", "multi_algorithm"]
        available_versions = [v for v in versions if v in metrics]
        
        trends = {
            "region_accuracy_trend": [],
            "id_accuracy_trend": [],
            "improvement_analysis": {}
        }
        
        print("📊 区域分配准确率变化:")
        for i, version in enumerate(available_versions):
            region_acc = metrics[version]["region_accuracy"]
            trends["region_accuracy_trend"].append(region_acc)
            
            if i == 0:
                print(f"  {version}: {region_acc:.1%} (基线)")
            else:
                prev_acc = metrics[available_versions[i-1]]["region_accuracy"]
                improvement = region_acc - prev_acc
                print(f"  {version}: {region_acc:.1%} ({improvement:+.1%})")
        
        print("\n📊 ID分配准确率变化:")
        for i, version in enumerate(available_versions):
            id_acc = metrics[version]["id_accuracy"]
            trends["id_accuracy_trend"].append(id_acc)
            
            if i == 0:
                print(f"  {version}: {id_acc:.1%} (基线)")
            else:
                prev_acc = metrics[available_versions[i-1]]["id_accuracy"]
                improvement = id_acc - prev_acc
                print(f"  {version}: {id_acc:.1%} ({improvement:+.1%})")
        
        # 计算总体改进
        if len(available_versions) >= 2:
            first_version = available_versions[0]
            last_version = available_versions[-1]
            
            region_total_improvement = (
                metrics[last_version]["region_accuracy"] - 
                metrics[first_version]["region_accuracy"]
            )
            id_total_improvement = (
                metrics[last_version]["id_accuracy"] - 
                metrics[first_version]["id_accuracy"]
            )
            
            trends["improvement_analysis"] = {
                "region_total_improvement": region_total_improvement,
                "id_total_improvement": id_total_improvement,
                "best_region_version": max(available_versions, key=lambda v: metrics[v]["region_accuracy"]),
                "best_id_version": max(available_versions, key=lambda v: metrics[v]["id_accuracy"])
            }
        
        return trends
    
    def analyze_error_patterns(self) -> Dict[str, Any]:
        """分析错误模式变化"""
        print("\n🔍 错误模式变化分析")
        print("=" * 40)
        
        error_analysis = {}
        
        for version, report in self.validation_reports.items():
            if version == "original":
                continue  # 原始版本数据结构不同
            
            # 统计错误模式
            all_errors = []
            for seq_result in report.get("sequence_results", []):
                all_errors.extend(seq_result.get("region_errors", []))
            
            if all_errors:
                error_patterns = Counter()
                for error in all_errors:
                    pattern = f"{error['expected_region']} -> {error['actual_region']}"
                    error_patterns[pattern] += 1
                
                error_analysis[version] = {
                    "total_errors": len(all_errors),
                    "top_error_patterns": dict(error_patterns.most_common(5)),
                    "error_rate": len(all_errors) / report["overall_statistics"]["total_cards_validated"]
                }
                
                print(f"\n{version}版本错误分析:")
                print(f"  总错误数: {len(all_errors)}")
                print(f"  错误率: {error_analysis[version]['error_rate']:.1%}")
                print("  主要错误模式:")
                for pattern, count in error_patterns.most_common(3):
                    print(f"    {pattern}: {count}次")
        
        return error_analysis
    
    def identify_remaining_challenges(self, metrics: Dict, error_analysis: Dict) -> List[str]:
        """识别剩余挑战"""
        print("\n🎯 剩余挑战识别")
        print("=" * 40)
        
        challenges = []
        
        # 1. 区域分配挑战
        latest_version = "multi_algorithm" if "multi_algorithm" in metrics else "v2"
        if latest_version in metrics:
            region_acc = metrics[latest_version]["region_accuracy"]
            if region_acc < 0.95:
                gap = 0.95 - region_acc
                challenges.append(f"区域分配准确率距离95%目标还差{gap:.1%}")
        
        # 2. ID分配挑战
        if latest_version in metrics:
            id_acc = metrics[latest_version]["id_accuracy"]
            if id_acc < 0.8:
                gap = 0.8 - id_acc
                challenges.append(f"ID分配准确率距离80%目标还差{gap:.1%}")
        
        # 3. 错误模式挑战
        if latest_version in error_analysis:
            top_errors = error_analysis[latest_version]["top_error_patterns"]
            if top_errors:
                most_common_error = list(top_errors.items())[0]
                pattern, count = most_common_error
                challenges.append(f"最常见错误模式'{pattern}'仍有{count}次出现")
        
        # 4. 算法性能挑战
        if "v2" in metrics and "multi_algorithm" in metrics:
            v2_acc = metrics["v2"]["region_accuracy"]
            multi_acc = metrics["multi_algorithm"]["region_accuracy"]
            if multi_acc < v2_acc:
                decline = v2_acc - multi_acc
                challenges.append(f"多算法融合版本性能下降{decline:.1%}，需要优化融合策略")
        
        print("识别的主要挑战:")
        for i, challenge in enumerate(challenges, 1):
            print(f"{i}. {challenge}")
        
        return challenges
    
    def generate_improvement_recommendations(self, challenges: List[str]) -> List[str]:
        """生成改进建议"""
        print("\n💡 改进建议")
        print("=" * 40)
        
        recommendations = []
        
        for challenge in challenges:
            if "区域分配准确率" in challenge:
                recommendations.append("引入更先进的机器学习算法（如深度学习）进行区域分类")
                recommendations.append("收集更多高质量的标注数据进行训练")
                recommendations.append("优化区域边界定义和特征工程")
            
            elif "ID分配准确率" in challenge:
                recommendations.append("改进空间排序算法，考虑更多上下文信息")
                recommendations.append("优化数字孪生ID分配策略")
                recommendations.append("引入时序信息进行ID追踪")
            
            elif "错误模式" in challenge:
                recommendations.append("针对特定错误模式设计专门的处理逻辑")
                recommendations.append("使用对抗训练提高模型鲁棒性")
            
            elif "性能下降" in challenge:
                recommendations.append("优化多算法融合的权重分配策略")
                recommendations.append("使用更复杂的集成学习方法")
                recommendations.append("引入自适应融合机制")
        
        # 去重
        recommendations = list(set(recommendations))
        
        print("建议的改进方向:")
        for i, rec in enumerate(recommendations, 1):
            print(f"{i}. {rec}")
        
        return recommendations
    
    def run_comprehensive_comparison(self) -> Dict[str, Any]:
        """运行全面对比分析"""
        print("🚀 区域分类器性能全面对比分析")
        print("=" * 60)
        
        # 1. 提取性能指标
        metrics = self.extract_performance_metrics()
        
        # 2. 分析改进趋势
        trends = self.analyze_improvement_trends(metrics)
        
        # 3. 分析错误模式
        error_analysis = self.analyze_error_patterns()
        
        # 4. 识别剩余挑战
        challenges = self.identify_remaining_challenges(metrics, error_analysis)
        
        # 5. 生成改进建议
        recommendations = self.generate_improvement_recommendations(challenges)
        
        # 保存分析结果
        analysis_result = {
            "analysis_timestamp": "2025-01-17 08:30:00",
            "performance_metrics": metrics,
            "improvement_trends": trends,
            "error_analysis": error_analysis,
            "remaining_challenges": challenges,
            "improvement_recommendations": recommendations
        }
        
        output_path = "analysis/performance_comparison_results.json"
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(analysis_result, f, ensure_ascii=False, indent=2)
        
        print(f"\n💾 对比分析结果已保存: {output_path}")
        
        return analysis_result

def main():
    """主分析函数"""
    comparator = PerformanceComparator()
    results = comparator.run_comprehensive_comparison()
    
    print("\n🎉 性能对比分析完成！")
    return True

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
