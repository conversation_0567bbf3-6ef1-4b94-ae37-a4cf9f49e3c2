"""
测试数字孪生系统V3.0
验证是否严格按照GAME_RULES设计文档实现

测试内容：
1. 有效卡牌类别过滤（21个类别）
2. 空间排序规则（从下到上，从左到右）
3. ID绝对稳定性原则
4. 帧间继承优先原则
5. 区域2互斥处理
6. 虚拟区域处理（区域10、11、12）
7. 物理约束验证
"""

import sys
import logging
sys.path.insert(0, '.')

from src.core.digital_twin_v3 import create_digital_twin_system

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')

def test_valid_card_filtering():
    """测试1: 有效卡牌类别过滤"""
    print("🧪 测试1: 有效卡牌类别过滤（21个类别）")
    print("-" * 50)
    
    dt = create_digital_twin_system()
    
    # 模拟检测结果：包含有效和无效类别
    detections = [
        {'label': '二', 'bbox': [100, 100, 150, 150], 'confidence': 0.9, 'group_id': 1},  # 有效
        {'label': '三', 'bbox': [200, 100, 250, 150], 'confidence': 0.8, 'group_id': 1},  # 有效
        {'label': '暗', 'bbox': [300, 100, 350, 150], 'confidence': 0.7, 'group_id': 6},  # 有效
        {'label': '碰', 'bbox': [400, 100, 450, 150], 'confidence': 0.9, 'group_id': 10}, # 无效（UI元素）
        {'label': '胡', 'bbox': [500, 100, 550, 150], 'confidence': 0.8, 'group_id': 11}, # 无效（UI元素）
    ]
    
    result = dt.process_frame(detections, frame_id=1)
    
    print(f"输入检测: {len(detections)}个对象")
    print(f"有效卡牌: {result['statistics']['total_cards']}张")
    print(f"物理卡牌: {result['statistics']['physical_cards']}张")
    print(f"虚拟卡牌: {result['statistics']['virtual_cards']}张")
    
    # 验证：应该只有3张有效卡牌（二、三、暗）
    expected_valid = 3
    actual_valid = result['statistics']['total_cards']
    success = actual_valid == expected_valid
    
    print(f"✅ 有效类别过滤{'成功' if success else '失败'}: {actual_valid}/{expected_valid}")
    return success

def test_spatial_sorting():
    """测试2: 空间排序规则"""
    print("\n🧪 测试2: 空间排序规则（从下到上，从左到右）")
    print("-" * 50)
    
    dt = create_digital_twin_system()
    
    # 模拟手牌区3张相同卡牌，不同Y坐标
    detections = [
        {'label': '二', 'bbox': [100, 100, 150, 200], 'confidence': 0.9, 'group_id': 1},  # 底部200
        {'label': '二', 'bbox': [100, 50, 150, 150], 'confidence': 0.8, 'group_id': 1},   # 底部150  
        {'label': '二', 'bbox': [100, 150, 150, 250], 'confidence': 0.9, 'group_id': 1},  # 底部250
    ]
    
    result = dt.process_frame(detections, frame_id=1)
    cards = result['digital_twin_cards']
    
    print("空间排序结果:")
    for card in sorted(cards, key=lambda c: -c.bbox[3]):  # 按底部坐标排序显示
        print(f"  {card.twin_id} - 底部坐标: {card.bbox[3]}")
    
    # 验证：底部坐标最大的应该分配1号ID（从下到上）
    bottom_card = max(cards, key=lambda c: c.bbox[3])
    expected_id = "1_二"
    success = bottom_card.twin_id == expected_id
    
    print(f"✅ 空间排序{'成功' if success else '失败'}: 最下方卡牌ID为{bottom_card.twin_id}")
    return success

def test_id_stability():
    """测试3: ID绝对稳定性原则"""
    print("\n🧪 测试3: ID绝对稳定性原则")
    print("-" * 50)
    
    dt = create_digital_twin_system()
    
    # 第一帧
    frame1 = [
        {'label': '二', 'bbox': [100, 100, 150, 200], 'confidence': 0.9, 'group_id': 1},
        {'label': '三', 'bbox': [200, 100, 250, 200], 'confidence': 0.8, 'group_id': 1},
    ]
    
    result1 = dt.process_frame(frame1, frame_id=1)
    cards1 = result1['digital_twin_cards']
    
    print("第一帧ID分配:")
    for card in cards1:
        print(f"  {card.twin_id}")
    
    # 第二帧（位置略有变化）
    frame2 = [
        {'label': '二', 'bbox': [105, 105, 155, 205], 'confidence': 0.9, 'group_id': 1},  # 略微移动
        {'label': '三', 'bbox': [205, 105, 255, 205], 'confidence': 0.8, 'group_id': 1},  # 略微移动
    ]
    
    result2 = dt.process_frame(frame2, frame_id=2)
    cards2 = result2['digital_twin_cards']
    
    print("第二帧ID分配:")
    for card in cards2:
        print(f"  {card.twin_id}")
    
    # 验证ID是否保持一致
    ids1 = sorted([c.twin_id for c in cards1])
    ids2 = sorted([c.twin_id for c in cards2])
    success = ids1 == ids2
    
    print(f"继承率: {result2['statistics']['inheritance_rate']:.1f}%")
    print(f"✅ ID稳定性{'成功' if success else '失败'}: {ids1} -> {ids2}")
    return success

def test_region2_exclusive():
    """测试4: 区域2互斥处理"""
    print("\n🧪 测试4: 区域2互斥处理")
    print("-" * 50)
    
    dt = create_digital_twin_system()
    
    # 第一帧：区域1有多张相同卡牌
    frame1 = [
        {'label': '二', 'bbox': [100, 100, 150, 200], 'confidence': 0.9, 'group_id': 1},
        {'label': '二', 'bbox': [100, 50, 150, 150], 'confidence': 0.8, 'group_id': 1},
        {'label': '二', 'bbox': [100, 150, 150, 250], 'confidence': 0.9, 'group_id': 1},
    ]
    
    result1 = dt.process_frame(frame1, frame_id=1)
    
    print("第一帧（区域1）:")
    for card in result1['digital_twin_cards']:
        print(f"  {card.twin_id} (区域{card.group_id})")
    
    # 第二帧：区域2出现，应该继承最大ID
    frame2 = [
        {'label': '二', 'bbox': [100, 100, 150, 200], 'confidence': 0.9, 'group_id': 1},
        {'label': '二', 'bbox': [100, 50, 150, 150], 'confidence': 0.8, 'group_id': 1},
        {'label': '二', 'bbox': [300, 100, 350, 200], 'confidence': 0.9, 'group_id': 2},  # 区域2
    ]
    
    result2 = dt.process_frame(frame2, frame_id=2)
    cards2 = result2['digital_twin_cards']
    
    print("第二帧（区域2出现）:")
    region1_cards = [c for c in cards2 if c.group_id == 1]
    region2_cards = [c for c in cards2 if c.group_id == 2]
    
    print("  区域1:")
    for card in region1_cards:
        print(f"    {card.twin_id}")
    print("  区域2:")
    for card in region2_cards:
        print(f"    {card.twin_id}")
    
    # 验证：区域2应该继承最大ID，区域1应该少一张卡
    region2_id = region2_cards[0].twin_id if region2_cards else ""
    expected_id = "3_二"  # 应该继承最大的ID
    success = region2_id == expected_id and len(region1_cards) == 2
    
    print(f"✅ 区域2互斥{'成功' if success else '失败'}: 区域2继承了{region2_id}")
    return success

def test_virtual_regions():
    """测试5: 虚拟区域处理"""
    print("\n🧪 测试5: 虚拟区域处理（区域10、11、12）")
    print("-" * 50)
    
    dt = create_digital_twin_system()
    
    # 包含虚拟区域的检测
    detections = [
        {'label': '二', 'bbox': [100, 100, 150, 200], 'confidence': 0.9, 'group_id': 1},   # 正常区域
        {'label': '三', 'bbox': [200, 100, 250, 200], 'confidence': 0.8, 'group_id': 10},  # 虚拟区域
        {'label': '四', 'bbox': [300, 100, 350, 200], 'confidence': 0.9, 'group_id': 11},  # 虚拟区域
        {'label': '五', 'bbox': [400, 100, 450, 200], 'confidence': 0.7, 'group_id': 12},  # 虚拟区域
    ]
    
    result = dt.process_frame(detections, frame_id=1)
    cards = result['digital_twin_cards']
    
    print("处理结果:")
    for card in cards:
        virtual_mark = " (虚拟)" if card.is_virtual else ""
        print(f"  {card.twin_id} (区域{card.group_id}){virtual_mark}")
    
    # 验证：虚拟区域的卡牌应该标记为虚拟
    virtual_cards = [c for c in cards if c.is_virtual]
    normal_cards = [c for c in cards if not c.is_virtual]
    
    success = len(virtual_cards) == 3 and len(normal_cards) == 1
    print(f"✅ 虚拟区域处理{'成功' if success else '失败'}: {len(virtual_cards)}张虚拟卡牌")
    return success

def test_physical_constraints():
    """测试6: 物理约束验证"""
    print("\n🧪 测试6: 物理约束验证")
    print("-" * 50)
    
    dt = create_digital_twin_system()
    
    # 模拟超过4张相同卡牌的情况
    detections = [
        {'label': '二', 'bbox': [100, 100, 150, 200], 'confidence': 0.9, 'group_id': 1},
        {'label': '二', 'bbox': [150, 100, 200, 200], 'confidence': 0.9, 'group_id': 1},
        {'label': '二', 'bbox': [200, 100, 250, 200], 'confidence': 0.9, 'group_id': 1},
        {'label': '二', 'bbox': [250, 100, 300, 200], 'confidence': 0.9, 'group_id': 1},
        {'label': '二', 'bbox': [300, 100, 350, 200], 'confidence': 0.9, 'group_id': 1},  # 第5张
    ]
    
    result = dt.process_frame(detections, frame_id=1)
    cards = result['digital_twin_cards']
    
    print("处理结果:")
    for card in cards:
        virtual_mark = " (虚拟)" if card.is_virtual else ""
        print(f"  {card.twin_id}{virtual_mark}")
    
    # 验证物理约束
    violations = dt.validate_physical_constraints()
    
    # 验证：第5张应该被标记为虚拟
    virtual_cards = [c for c in cards if c.is_virtual and c.label == '二']
    success = len(virtual_cards) == 1 and len(violations) == 0
    
    print(f"约束违反: {violations}")
    print(f"✅ 物理约束{'成功' if success else '失败'}: {len(virtual_cards)}张虚拟卡牌")
    return success

def main():
    """运行所有测试"""
    print("🚀 数字孪生系统V3.0测试 - 基于GAME_RULES设计文档")
    print("=" * 60)
    
    tests = [
        test_valid_card_filtering,
        test_spatial_sorting,
        test_id_stability,
        test_region2_exclusive,
        test_virtual_regions,
        test_physical_constraints
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"❌ 测试失败: {e}")
            results.append(False)
    
    print("\n" + "=" * 60)
    print("📊 测试总结:")
    success_count = sum(results)
    total_count = len(results)
    
    test_names = [
        "有效类别过滤", "空间排序规则", "ID稳定性原则", 
        "区域2互斥处理", "虚拟区域处理", "物理约束验证"
    ]
    
    for i, (name, result) in enumerate(zip(test_names, results)):
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  测试{i+1} ({name}): {status}")
    
    print(f"\n🎯 总体结果: {success_count}/{total_count} 测试通过")
    
    if success_count == total_count:
        print("🎉 所有测试通过！系统符合GAME_RULES设计文档要求。")
    else:
        print("⚠️ 部分测试失败，需要进一步调试。")

if __name__ == "__main__":
    main()
