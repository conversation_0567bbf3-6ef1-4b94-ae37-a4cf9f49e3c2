#!/usr/bin/env python3
"""
测试所有区域的暗牌修复逻辑
验证设计文档中的暗牌分配规则
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'src', 'core'))

from digital_twin_v3 import create_digital_twin_system

def test_all_regions_dark_card_fix():
    """测试所有区域的暗牌修复"""
    print("🔍 测试所有区域的暗牌修复逻辑")
    
    # 创建数字孪生系统
    system = create_digital_twin_system()
    
    # 测试不同区域的暗牌修复
    test_regions = [6, 14, 15, 16]  # 常见的有暗牌的区域
    
    for region_id in test_regions:
        print(f"\n📍 测试区域 {region_id}")
        
        # 第一帧：建立暗牌和明牌
        frame1_detections = [
            {
                'label': '暗',
                'bbox': [100, 200, 150, 250],  # 暗牌
                'confidence': 0.8,
                'group_id': region_id
            },
            {
                'label': '二',
                'bbox': [100, 100, 150, 150],  # 明牌
                'confidence': 0.9,
                'group_id': region_id
            }
        ]
        
        result1 = system.process_frame(frame1_detections, 1)
        print(f"第一帧结果: {len(result1['digital_twin_cards'])}张卡牌")
        for card in result1['digital_twin_cards']:
            print(f"  - {card.twin_id} (标签: {card.label}, 暗牌: {card.is_dark}, 区域: {card.group_id})")
        
        # 第二帧：只有明牌，暗牌被遮挡（触发补偿）
        frame2_detections = [
            {
                'label': '二',
                'bbox': [100, 100, 150, 150],  # 明牌继续存在
                'confidence': 0.9,
                'group_id': region_id
            }
        ]
        
        print(f"第二帧：模拟区域{region_id}暗牌被遮挡，触发补偿")
        result2 = system.process_frame(frame2_detections, 2)
        print(f"第二帧结果: {len(result2['digital_twin_cards'])}张卡牌")
        
        # 分析结果
        success = True
        for card in result2['digital_twin_cards']:
            print(f"  - {card.twin_id} (标签: {card.label}, 暗牌: {card.is_dark}, 置信度: {card.confidence}, 区域: {card.group_id})")
            
            # 检查补偿的暗牌是否正确关联
            if card.is_dark:
                if card.twin_id.endswith('_暗'):
                    print(f"    ❌ 错误: 暗牌 {card.twin_id} 仍然使用旧格式（带下划线）")
                    success = False
                elif card.twin_id.endswith('二暗'):
                    print(f"    ✅ 正确: 暗牌 {card.twin_id} 正确关联到'二'（新格式）")
                else:
                    print(f"    ⚠️  未知: 暗牌 {card.twin_id} 格式未知")
                    success = False
        
        if success:
            print(f"✅ 区域{region_id}暗牌修复成功!")
        else:
            print(f"❌ 区域{region_id}暗牌修复失败!")
        
        # 重置系统为下一个测试
        system = create_digital_twin_system()
    
    return True

def test_wei_pai_scenario():
    """测试偎牌场景（1明2暗）"""
    print(f"\n🎯 测试偎牌场景（1明2暗）")
    
    system = create_digital_twin_system()
    
    # 模拟偎牌：1明2暗
    frame_detections = [
        {
            'label': '暗',
            'bbox': [100, 250, 150, 300],  # 第一张暗牌（最下面）
            'confidence': 0.8,
            'group_id': 6
        },
        {
            'label': '暗',
            'bbox': [100, 200, 150, 250],  # 第二张暗牌（中间）
            'confidence': 0.8,
            'group_id': 6
        },
        {
            'label': '二',
            'bbox': [100, 150, 150, 200],  # 明牌（最上面）
            'confidence': 0.9,
            'group_id': 6
        }
    ]
    
    result = system.process_frame(frame_detections, 1)
    print(f"偎牌结果: {len(result['digital_twin_cards'])}张卡牌")
    
    # 按照设计文档，应该是：1二暗, 2二暗, 3二
    expected_ids = ['1二暗', '2二暗', '3二']
    actual_ids = [card.twin_id for card in result['digital_twin_cards']]
    
    print(f"期望ID: {expected_ids}")
    print(f"实际ID: {actual_ids}")
    
    # 检查是否符合设计文档
    success = True
    for card in result['digital_twin_cards']:
        print(f"  - {card.twin_id} (标签: {card.label}, 暗牌: {card.is_dark})")
        if card.is_dark and '_' in card.twin_id:
            print(f"    ❌ 错误: 暗牌 {card.twin_id} 使用了旧格式（带下划线）")
            success = False
        elif card.is_dark and card.twin_id.endswith('二暗'):
            print(f"    ✅ 正确: 暗牌 {card.twin_id} 使用新格式（无下划线）")
        elif not card.is_dark and card.twin_id.endswith('二'):
            print(f"    ✅ 正确: 明牌 {card.twin_id} 格式正确")
    
    return success

if __name__ == "__main__":
    print("🔧 测试所有区域的暗牌修复逻辑")
    print("=" * 60)
    
    # 测试所有区域的暗牌修复
    success1 = test_all_regions_dark_card_fix()
    
    # 测试偎牌场景
    success2 = test_wei_pai_scenario()
    
    print("\n" + "=" * 60)
    if success1 and success2:
        print("🎉 所有测试通过! 暗牌修复逻辑符合设计文档!")
    else:
        print("❌ 部分测试失败，需要进一步修复")
