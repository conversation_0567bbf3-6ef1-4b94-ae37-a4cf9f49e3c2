# 跑胡子AI配置文件

# 模型配置
model_path: "best.pt"
confidence_threshold: 0.25
iou_threshold: 0.45
device: "cuda:0"  # 使用GPU，如果没有GPU可以设置为"cpu"

# AnyLabeling兼容配置 (2025-07-16新增)
anylabeling_compatible:
  model_path: "data/processed/train9.0/weights/best.onnx"
  confidence_threshold: 0.01
  iou_threshold: 0.1
  enable_validation: false
  description: "AnyLabeling兼容配置，97.4%召回率，2.6%漏检率"

# 屏幕捕获配置
monitor:
  top: 0
  left: 0
  width: 1280
  height: 720

# 决策配置
decision:
  agent_type: "random"  # 可选: random, rule_based, dqn
  seed: 42
  confidence_threshold: 0.7
  win_rate_threshold: 0.6

# RLCard配置
rlcard:
  num_players: 2
  num_actions: 5
  state_shape: [80, 320]  # 80张牌，320维状态向量

# 输出配置
output:
  save_detections: false
  save_frames: false
  output_dir: "output"
  visualize: true 