#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
YOLO类别映射调试器

调试YOLO模型输出的类别索引与实际标签的对应关系，
找出正确的映射方式。
"""

import os
import sys
import cv2
import json
from pathlib import Path
from typing import Dict, List, Any
import numpy as np

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)

from ultralytics import YOLO


class YOLOClassMappingDebugger:
    """YOLO类别映射调试器"""
    
    def __init__(self):
        """初始化调试器"""
        # 使用train9.0模型
        self.model_path = r"D:\phz-ai-simple\data\processed\train9.0\weights\best.pt"
        self.yaml_path = r"D:\phz-ai-simple\data\processed\train9.0\weights\yolov8x9.0.yaml"
        
        # 加载YOLO模型
        self.model = YOLO(self.model_path)
        
        # 从YAML加载类别名称
        self.yaml_classes = self._load_yaml_classes()
        
        # 项目标准映射
        self.project_mapping = {
            "一": 1, "二": 2, "三": 3, "四": 4, "五": 5, "六": 6, "七": 7, "八": 8, "九": 9, "十": 10,
            "壹": 11, "贰": 12, "叁": 13, "肆": 14, "伍": 15, "陆": 16, "柒": 17, "捌": 18, "玖": 19, "拾": 20,
            "暗": 21, "吃": 22, "碰": 23, "胡": 24, "过": 25, "打鸟选择": 26, "已准备": 27,
            "你赢了": 28, "你输了": 29, "荒庄": 30, "牌局结束": 31
        }
        
        print(f"🔍 YOLO类别映射调试器初始化")
        print(f"   - 模型: {self.model_path}")
        print(f"   - YAML类别数: {len(self.yaml_classes)}")
        print(f"   - 项目映射数: {len(self.project_mapping)}")
    
    def _load_yaml_classes(self) -> List[str]:
        """从YAML文件加载类别名称"""
        classes = []
        
        try:
            with open(self.yaml_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            in_classes_section = False
            for line in lines:
                line = line.strip()
                if line == 'classes:':
                    in_classes_section = True
                    continue
                
                if in_classes_section:
                    if line.startswith('- '):
                        # 提取类别名称
                        class_name = line[2:].split('#')[0].strip()
                        classes.append(class_name)
                    elif line and not line.startswith(' ') and not line.startswith('-'):
                        break
                        
        except Exception as e:
            print(f"❌ 加载YAML失败: {e}")
        
        return classes
    
    def debug_class_mapping(self, test_images_dir: str = "legacy_assets/ceshi/calibration_gt/images") -> Dict[str, Any]:
        """调试类别映射"""
        print(f"🚀 开始调试类别映射...")
        
        # 收集测试图像
        test_images = []
        for filename in sorted(os.listdir(test_images_dir))[:10]:  # 只测试前10张
            if filename.lower().endswith(('.jpg', '.jpeg', '.png')):
                test_images.append(os.path.join(test_images_dir, filename))
        
        print(f"📊 测试 {len(test_images)} 张图像")
        
        # 调试结果
        debug_results = {
            'yolo_raw_outputs': [],
            'yaml_mapping': {},
            'project_mapping': self.project_mapping,
            'mapping_analysis': {}
        }
        
        # 处理每张图像
        for i, image_path in enumerate(test_images):
            print(f"   处理图像 {i+1}: {os.path.basename(image_path)}")
            
            # 使用YOLO直接检测
            results = self.model(image_path, verbose=False)
            
            for result in results:
                if result.boxes is not None:
                    for j, box in enumerate(result.boxes):
                        cls_id = int(box.cls[0])
                        conf = float(box.conf[0])
                        
                        # YOLO原始输出
                        yolo_output = {
                            'image': os.path.basename(image_path),
                            'detection_id': j,
                            'yolo_class_id': cls_id,
                            'confidence': conf,
                            'yaml_class_name': self.yaml_classes[cls_id] if cls_id < len(self.yaml_classes) else 'unknown',
                            'project_mapping_plus1': self._get_project_label(cls_id + 1),
                            'project_mapping_direct': self._get_project_label(cls_id),
                            'bbox': box.xyxy[0].tolist()
                        }
                        
                        debug_results['yolo_raw_outputs'].append(yolo_output)
        
        # 创建映射分析
        debug_results['yaml_mapping'] = {i: name for i, name in enumerate(self.yaml_classes)}
        debug_results['mapping_analysis'] = self._analyze_mappings(debug_results['yolo_raw_outputs'])
        
        # 保存调试结果
        debug_path = "output/yolo_class_mapping_debug.json"
        os.makedirs(os.path.dirname(debug_path), exist_ok=True)
        with open(debug_path, 'w', encoding='utf-8') as f:
            json.dump(debug_results, f, ensure_ascii=False, indent=2)
        
        # 生成调试报告
        report = self._generate_debug_report(debug_results)
        
        print(f"✅ 类别映射调试完成")
        return report
    
    def _get_project_label(self, label_id: int) -> str:
        """根据ID获取项目标签"""
        id_to_label = {v: k for k, v in self.project_mapping.items()}
        return id_to_label.get(label_id, 'unknown')
    
    def _analyze_mappings(self, yolo_outputs: List[Dict]) -> Dict[str, Any]:
        """分析映射关系"""
        analysis = {
            'yaml_vs_project_plus1': {},
            'yaml_vs_project_direct': {},
            'mapping_consistency': {}
        }
        
        for output in yolo_outputs:
            yaml_name = output['yaml_class_name']
            project_plus1 = output['project_mapping_plus1']
            project_direct = output['project_mapping_direct']
            
            # 分析YAML vs 项目映射(+1)
            key = f"{yaml_name} vs {project_plus1}"
            if key not in analysis['yaml_vs_project_plus1']:
                analysis['yaml_vs_project_plus1'][key] = 0
            analysis['yaml_vs_project_plus1'][key] += 1
            
            # 分析YAML vs 项目映射(直接)
            key = f"{yaml_name} vs {project_direct}"
            if key not in analysis['yaml_vs_project_direct']:
                analysis['yaml_vs_project_direct'][key] = 0
            analysis['yaml_vs_project_direct'][key] += 1
        
        return analysis
    
    def _generate_debug_report(self, debug_results: Dict[str, Any]) -> Dict[str, Any]:
        """生成调试报告"""
        
        # 分析YAML和项目映射的一致性
        yaml_classes = debug_results['yaml_mapping']
        project_mapping = debug_results['project_mapping']
        
        consistency_analysis = {}
        
        # 检查YAML类别与项目映射的对应关系
        for yolo_id, yaml_name in yaml_classes.items():
            project_id_plus1 = yolo_id + 1
            project_id_direct = yolo_id
            
            project_label_plus1 = self._get_project_label(project_id_plus1)
            project_label_direct = self._get_project_label(project_id_direct)
            
            consistency_analysis[f"yolo_id_{yolo_id}"] = {
                'yaml_name': yaml_name,
                'project_plus1': project_label_plus1,
                'project_direct': project_label_direct,
                'yaml_matches_plus1': yaml_name == project_label_plus1,
                'yaml_matches_direct': yaml_name == project_label_direct
            }
        
        report = {
            'summary': {
                'total_detections': len(debug_results['yolo_raw_outputs']),
                'yaml_classes_count': len(yaml_classes),
                'project_mapping_count': len(project_mapping)
            },
            'consistency_analysis': consistency_analysis,
            'mapping_recommendations': self._generate_mapping_recommendations(consistency_analysis),
            'sample_detections': debug_results['yolo_raw_outputs'][:20]  # 前20个检测
        }
        
        # 保存报告
        report_path = "output/yolo_mapping_debug_report.json"
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        return report
    
    def _generate_mapping_recommendations(self, consistency_analysis: Dict[str, Any]) -> List[str]:
        """生成映射建议"""
        recommendations = []
        
        plus1_matches = sum(1 for analysis in consistency_analysis.values() 
                           if analysis['yaml_matches_plus1'])
        direct_matches = sum(1 for analysis in consistency_analysis.values() 
                            if analysis['yaml_matches_direct'])
        
        total_classes = len(consistency_analysis)
        
        plus1_rate = plus1_matches / total_classes if total_classes > 0 else 0
        direct_rate = direct_matches / total_classes if total_classes > 0 else 0
        
        if plus1_rate > direct_rate:
            recommendations.append(f"建议使用+1映射: YAML匹配率{plus1_rate:.1%} vs 直接映射{direct_rate:.1%}")
            recommendations.append("修改CardDetector: cls_name = ID_TO_LABEL.get(cls_id+1, 'unknown')")
        elif direct_rate > plus1_rate:
            recommendations.append(f"建议使用直接映射: 直接映射匹配率{direct_rate:.1%} vs +1映射{plus1_rate:.1%}")
            recommendations.append("修改CardDetector: cls_name = ID_TO_LABEL.get(cls_id, 'unknown')")
        else:
            recommendations.append("YAML与项目映射不一致，需要手动检查映射关系")
        
        return recommendations


def main():
    """主函数"""
    print("🔍 YOLO类别映射调试器")
    print("=" * 50)
    
    # 创建调试器
    debugger = YOLOClassMappingDebugger()
    
    # 调试类别映射
    report = debugger.debug_class_mapping()
    
    # 打印结果
    print("\n📊 调试结果:")
    print(f"   总检测数: {report['summary']['total_detections']}")
    print(f"   YAML类别数: {report['summary']['yaml_classes_count']}")
    print(f"   项目映射数: {report['summary']['project_mapping_count']}")
    
    print("\n💡 映射建议:")
    for rec in report['mapping_recommendations']:
        print(f"   • {rec}")
    
    print("\n🔍 一致性分析样例:")
    for i, (key, analysis) in enumerate(list(report['consistency_analysis'].items())[:5]):
        print(f"   {key}:")
        print(f"     YAML: {analysis['yaml_name']}")
        print(f"     项目+1: {analysis['project_plus1']} ({'✅' if analysis['yaml_matches_plus1'] else '❌'})")
        print(f"     项目直接: {analysis['project_direct']} ({'✅' if analysis['yaml_matches_direct'] else '❌'})")
    
    print(f"\n📁 详细调试数据: output/yolo_class_mapping_debug.json")
    print(f"📁 调试报告: output/yolo_mapping_debug_report.json")


if __name__ == "__main__":
    main()
