import sys
sys.path.append('src')

print("🧪 快速测试继承机制")

try:
    from modules.simple_inheritor import SimpleInheritor
    
    inheritor = SimpleInheritor(iou_threshold=0.3)
    
    # 第1帧
    frame1 = [{'label': '二', 'bbox': [100, 100, 150, 150], 'group_id': 1, 'twin_id': '1二'}]
    result1 = inheritor.process_inheritance(frame1)
    print(f"第1帧: 继承{len(result1.inherited_cards)}, 新增{len(result1.new_cards)}")
    
    # 第2帧
    frame2 = [{'label': '二', 'bbox': [105, 105, 155, 155], 'group_id': 1}]
    result2 = inheritor.process_inheritance(frame2)
    print(f"第2帧: 继承{len(result2.inherited_cards)}, 新增{len(result2.new_cards)}")
    
    if result2.inherited_cards:
        print(f"✅ 继承成功: {result2.inherited_cards[0]['twin_id']}")
    else:
        print("❌ 继承失败")
        
except Exception as e:
    print(f"❌ 错误: {e}")
    import traceback
    traceback.print_exc()
