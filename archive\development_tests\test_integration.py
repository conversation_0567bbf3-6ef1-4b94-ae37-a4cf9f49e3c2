"""
测试新的数字孪生系统V3.0集成
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.core.digital_twin_v3 import DigitalTwinSystemV3

def test_integration():
    """测试集成"""
    print("🧪 测试新数字孪生系统V3.0集成")
    
    system = DigitalTwinSystemV3()
    
    # 模拟检测结果
    detections = [
        {"label": "二", "bbox": [100, 100, 150, 150], "confidence": 0.9, "group_id": 6, "region_name": "吃碰区_观战方", "owner": "观战方"},
        {"label": "暗", "bbox": [160, 100, 210, 150], "confidence": 0.8, "group_id": 6, "region_name": "吃碰区_观战方", "owner": "观战方"},
        {"label": "暗", "bbox": [220, 100, 270, 150], "confidence": 0.8, "group_id": 6, "region_name": "吃碰区_观战方", "owner": "观战方"},
    ]
    
    result = system.process_frame(detections, frame_id=0)
    
    print(f"✅ 处理成功: {result['success']}")
    print(f"📊 统计信息: {result['statistics']}")
    
    cards = result["digital_twin_cards"]
    print(f"🎯 分配的ID:")
    for card in cards:
        print(f"  {card.twin_id} (标签: {card.label}, 暗牌: {card.is_dark})")
    
    # 验证暗牌关联
    dark_cards = [c for c in cards if c.is_dark]
    expected_dark_ids = ["2二暗", "3二暗"]
    actual_dark_ids = [c.twin_id for c in dark_cards]
    
    if all(id in actual_dark_ids for id in expected_dark_ids):
        print("✅ 暗牌关联测试通过")
    else:
        print("❌ 暗牌关联测试失败")
        print(f"期望: {expected_dark_ids}")
        print(f"实际: {actual_dark_ids}")

if __name__ == "__main__":
    test_integration()
