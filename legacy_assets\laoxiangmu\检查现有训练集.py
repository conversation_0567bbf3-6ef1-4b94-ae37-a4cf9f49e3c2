#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
跑胡子卡牌标注验证与优化脚本 - 修正完整版
功能：
1. 与训练脚本完全兼容，确保模型在AnyLabeling中正常使用
2. 多种方法验证标注错误（位置、重复、类别等）
3. 生成详细的错误报告TXT文件
4. 可调节置信度阈值
5. 支持GPU加速和内存优化
6. 增强的日志系统和性能监控
"""

import os
import json
import cv2
import numpy as np
from pathlib import Path
from loguru import logger
import onnxruntime
from collections import defaultdict
from tqdm import tqdm
import argparse
import psutil
import GPUtil
import time
from datetime import datetime

# 默认配置参数
DEFAULT_CONFIG = {
    "model_path": r"D:\project_root\data\processed\xunlianshuchu\train5.0\weights\16best.onnx",
    "image_dir": r"D:\project_root\data\biaozhuwenjian\images\train",
    "json_dir": r"D:\project_root\data\biaozhuwenjian\labels\train",
    "output_report": r"D:\project_root\data\biaozhuwenjian\annotation_errors.txt",
    "visual_debug_dir": r"D:\project_root\data\biaozhuwenjian\visual_debug",
    "folders_to_check": [str(i) for i in range(25, 41)],  # 25-40文件夹
    "confidence_threshold": 0.5,  # 可调节的置信度阈值
    "iou_threshold": 0.5,  # 用于检测重复标注
    "position_error_threshold": 0.2,  # 位置误差阈值
    "class_mismatch_threshold": 0.7,  # 类别不匹配阈值
    "size_error_min_area": 100,  # 最小允许面积(px)
    "size_error_max_ratio": 0.25,  # 最大允许面积占图像比例
    "enable_visual_debug": True,  # 是否生成可视化调试图像
    "use_gpu": True,  # 是否使用GPU加速
    "enable_memory_monitor": True,  # 是否启用内存监控
    "batch_processing": False,  # 是否启用批处理模式
    "batch_conf_thresholds": [0.3, 0.5, 0.7],  # 批处理的置信度阈值列表
    "img_size": (640, 320)  # 图像尺寸 (H, W)
}

# 类别映射 (与训练脚本完全一致)
LABEL_TO_ID = {
    "一": 1, "二": 2, "三": 3, "四": 4, "五": 5, "六": 6, "七": 7, "八": 8, "九": 9, "十": 10,
    "壹": 11, "贰": 12, "叁": 13, "肆": 14, "伍": 15, "陆": 16, "柒": 17, "捌": 18, "玖": 19, "拾": 20,
    "暗": 21, "吃": 22, "碰": 23, "胡": 24, "过": 25, "打鸟选择": 26, "已准备": 27,
    "你赢了": 28, "你输了": 29, "荒庄": 30, "牌局结束": 31
}

ID_TO_LABEL = {v: k for k, v in LABEL_TO_ID.items()}


class SystemMonitor:
    """系统资源监控器"""

    @staticmethod
    def get_memory_usage():
        """获取内存使用情况(MB)"""
        process = psutil.Process(os.getpid())
        return process.memory_info().rss / 1024 / 1024

    @staticmethod
    def get_gpu_usage():
        """获取GPU使用情况"""
        try:
            gpus = GPUtil.getGPUs()
            if gpus:
                return {
                    "gpu_load": gpus[0].load * 100,
                    "gpu_mem_used": gpus[0].memoryUsed,
                    "gpu_mem_total": gpus[0].memoryTotal
                }
        except:
            pass
        return None

    @staticmethod
    def print_system_status():
        """打印系统状态"""
        mem = SystemMonitor.get_memory_usage()
        gpu = SystemMonitor.get_gpu_usage()

        status = f"内存使用: {mem:.2f} MB"
        if gpu:
            status += f" | GPU负载: {gpu['gpu_load']:.1f}% | GPU显存: {gpu['gpu_mem_used']}/{gpu['gpu_mem_total']}MB"
        logger.debug(status)


class AnnotationValidator:
    def __init__(self, config):
        self.config = config
        self.start_time = time.time()

        # 定义输入形状 (NCHW)
        self.image_size = config.get("img_size", (640, 320))  # H, W
        self.input_shape = (1, 3, self.image_size[0], self.image_size[1])  # NCHW 格式

        # 初始化日志系统
        self._setup_logging()

        # 初始化模型
        self.session = self._load_onnx_model()
        # Remove these lines as they're now handled in _load_onnx_model
        # self.input_name = self.session.get_inputs()[0].name
        # self.output_name = self.session.get_outputs()[0].name

        # 错误统计
        self.report = []
        self.error_stats = defaultdict(int)
        self.total_images = 0
        self.missing_annotations = 0
        self.false_positives = 0
        self.missed_objects = 0
        self.repeated_detections = 0
        self.low_confidence_count = 0

        # 创建输出目录
        os.makedirs(os.path.dirname(config["output_report"]), exist_ok=True)
        if config["enable_visual_debug"]:
            os.makedirs(config["visual_debug_dir"], exist_ok=True)

    def _setup_logging(self):
        """配置日志系统"""
        logger.add(
            os.path.join(os.path.dirname(self.config["output_report"]), "annotation_validator.log"),
            rotation="10 MB",
            retention="7 days",
            level="DEBUG",
            encoding="utf-8"
        )
        logger.info(f"初始化验证器，配置: {self.config}")

    def _load_onnx_model(self):
        """加载ONNX模型并预热"""
        try:
            # 优先使用GPU
            providers = (['CUDAExecutionProvider', 'CPUExecutionProvider']
                         if self.config["use_gpu"] else ['CPUExecutionProvider'])

            # 配置会话选项
            session_options = onnxruntime.SessionOptions()
            session_options.graph_optimization_level = (
                onnxruntime.GraphOptimizationLevel.ORT_ENABLE_ALL)
            session_options.intra_op_num_threads = min(14, os.cpu_count())  # 适配您的14线程CPU

            # 加载模型
            session = onnxruntime.InferenceSession(
                self.config["model_path"],
                providers=providers,
                sess_options=session_options
            )

            # Get input name after session is created
            self.input_name = session.get_inputs()[0].name
            self.output_name = session.get_outputs()[0].name

            # 模型预热
            self._warmup_model(session)

            logger.info(f"成功加载模型: {self.config['model_path']}")
            logger.info(f"使用执行提供者: {session.get_providers()}")
            logger.info(f"输入形状: {self.input_shape}")
            return session
        except Exception as e:
            logger.error(f"加载模型失败: {e}")
            raise
    def _warmup_model(self, session):
        """模型预热"""
        dummy_input = np.random.randn(*self.input_shape).astype(np.float16)
        inputs = {self.input_name: dummy_input}
        session.run(None, inputs)
        logger.info("模型预热完成")

    def _preprocess_image(self, image_path):
        """预处理图像"""
        img = cv2.imread(image_path)
        if img is None:
            logger.warning(f"无法读取图像: {image_path}")
            return None, None, None

        # 保持宽高比调整大小
        h, w = img.shape[:2]
        new_w, new_h = self.input_shape[3], self.input_shape[2]

        # 计算缩放比例并填充
        scale = min(new_w / w, new_h / h)
        resized = cv2.resize(img, (int(w * scale), int(h * scale)))
        padded = np.zeros((new_h, new_w, 3), dtype=np.uint8)
        padded[:resized.shape[0], :resized.shape[1]] = resized

        # 归一化并转置为CHW格式
        blob = padded.astype(np.float16) / 255.0
        blob = blob.transpose(2, 0, 1)[np.newaxis, ...]
        return blob, (w, h), scale

    def _extract_standard_label(self, label):
        """从标注中提取标准类别名 (与训练脚本完全一致)"""
        if not label:
            return None

        # 直接匹配
        if label in LABEL_TO_ID:
            return label

        # 提取中文字符
        chinese_chars = ''.join([c for c in label if '\u4e00' <= c <= '\u9fff'])

        # 尝试最长匹配
        if chinese_chars:
            for candidate in sorted(LABEL_TO_ID.keys(), key=len, reverse=True):
                if candidate in chinese_chars:
                    return candidate

        # 关键字匹配
        for keyword in ["吃", "碰", "胡", "过"]:
            if keyword in label:
                return keyword

        return None

    def _parse_json_annotations(self, json_path):
        """解析JSON标注文件"""
        try:
            with open(json_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
        except json.JSONDecodeError:
            self._add_error_report("无效JSON文件", json_path, "JSON解析错误")
            return []
        except Exception as e:
            self._add_error_report("读取JSON失败", json_path, str(e))
            return []

        image_width = data.get("imageWidth")
        image_height = data.get("imageHeight")
        if not image_width or not image_height:
            self._add_error_report("JSON缺少尺寸", json_path, "缺少imageWidth或imageHeight")
            return []

        annotations = []
        for shape in data.get("shapes", []):
            label = shape.get("label", "")
            points = shape.get("points", [])

            if not label or not points:
                continue

            # 提取标准类别名
            card_type = self._extract_standard_label(label)
            if not card_type:
                self._add_error_report("无法解析标签", json_path, f"原始标签: {label}")
                continue

            class_id = LABEL_TO_ID.get(card_type, 0)
            if class_id == 0:
                continue

            # 计算边界框
            x_coords = [p[0] for p in points]
            y_coords = [p[1] for p in points]
            xmin, xmax = min(x_coords), max(x_coords)
            ymin, ymax = min(y_coords), max(y_coords)

            # 确保坐标在图像范围内
            xmin = max(0, min(xmin, image_width - 1))
            xmax = max(0, min(xmax, image_width - 1))
            ymin = max(0, min(ymin, image_height - 1))
            ymax = max(0, min(ymax, image_height - 1))

            annotations.append({
                "class_id": class_id,
                "class_name": card_type,
                "bbox": [xmin, ymin, xmax, ymax],
                "width": image_width,
                "height": image_height,
                "original_label": label,
                "json_path": json_path
            })

        return annotations

    def _calculate_iou(self, box1, box2):
        """计算两个框的IoU"""
        x1_min, y1_min, x1_max, y1_max = box1
        x2_min, y2_min, x2_max, y2_max = box2

        # 计算交集区域
        inter_x_min = max(x1_min, x2_min)
        inter_y_min = max(y1_min, y2_min)
        inter_x_max = min(x1_max, x2_max)
        inter_y_max = min(y1_max, y2_max)

        if inter_x_max < inter_x_min or inter_y_max < inter_y_min:
            return 0.0

        inter_area = (inter_x_max - inter_x_min) * (inter_y_max - inter_y_min)
        area1 = (x1_max - x1_min) * (y1_max - y1_min)
        area2 = (x2_max - x2_min) * (y2_max - y2_min)

        return inter_area / (area1 + area2 - inter_area + 1e-6)  # 避免除以零

    def _postprocess_output(self, output, original_size, scale):
        """后处理模型输出"""
        predictions = np.squeeze(output[0])
        boxes = []
        scores = []
        class_ids = []

        # 原始图像尺寸
        orig_w, orig_h = original_size

        # 检查预测结果维度
        if len(predictions.shape) == 1:
            predictions = np.expand_dims(predictions, 0)

        for pred in predictions:
            # 过滤低置信度预测
            if pred[4] < self.config["confidence_threshold"]:
                continue

            # 提取类别信息
            scores_array = pred[5:]
            class_id = np.argmax(scores_array)
            score = pred[4] * scores_array[class_id]

            if score < self.config["confidence_threshold"]:
                continue

            # 转换坐标到原始图像尺寸
            x_center = pred[0] / scale
            y_center = pred[1] / scale
            width = pred[2] / scale
            height = pred[3] / scale

            x_min = max(0, int(x_center - width / 2))
            y_min = max(0, int(y_center - height / 2))
            x_max = min(orig_w, int(x_center + width / 2))
            y_max = min(orig_h, int(y_center + height / 2))

            # 确保有效的边界框
            if x_max <= x_min or y_max <= y_min:
                continue

            boxes.append([x_min, y_min, x_max, y_max])
            scores.append(score)
            class_ids.append(class_id)

        return boxes, scores, class_ids

    def _add_error_report(self, error_type, file_path, details, **kwargs):
        """添加错误报告"""
        rel_path = os.path.relpath(file_path, self.config["image_dir"])
        error_entry = f"{error_type}: {rel_path} | {details}"

        # 添加额外信息
        for k, v in kwargs.items():
            error_entry += f" | {k}: {v}"

        self.report.append(error_entry)
        self.error_stats[error_type] += 1

    def detect_annotation_errors(self):
        """检测标注错误"""
        logger.info("开始标注验证...")
        logger.info(f"检查文件夹: {self.config['folders_to_check']}")
        logger.info(f"置信度阈值: {self.config['confidence_threshold']}")

        # 遍历指定文件夹
        for folder in self.config["folders_to_check"]:
            image_folder = os.path.join(self.config["image_dir"], folder)
            json_folder = os.path.join(self.config["json_dir"], folder)

            if not os.path.exists(image_folder):
                logger.warning(f"图像文件夹不存在: {image_folder}")
                continue
            if not os.path.exists(json_folder):
                logger.warning(f"标注文件夹不存在: {json_folder}")
                continue

            # 获取图像和JSON文件列表
            image_files = [f for f in os.listdir(image_folder)
                           if f.lower().endswith(('.jpg', '.jpeg', '.png'))]

            if not image_files:
                logger.warning(f"文件夹 {folder} 中没有图像文件")
                continue

            for img_file in tqdm(image_files, desc=f"处理文件夹 {folder}"):
                # 构建对应JSON路径
                json_file = os.path.splitext(img_file)[0] + '.json'
                json_path = os.path.join(json_folder, json_file)
                image_path = os.path.join(image_folder, img_file)

                # 检查标注文件是否存在
                if not os.path.exists(json_path):
                    self._add_error_report("缺失标注", image_path, f"缺少对应的JSON文件: {json_file}")
                    continue

                # 监控系统资源
                if self.config["enable_memory_monitor"]:
                    SystemMonitor.print_system_status()

                # 预处理图像
                blob, original_size, scale = self._preprocess_image(image_path)
                if blob is None:
                    continue

                # 模型推理
                try:
                    outputs = self.session.run([self.output_name], {self.input_name: blob})
                    pred_boxes, pred_scores, pred_classes = self._postprocess_output(
                        outputs, original_size, scale)
                except Exception as e:
                    logger.error(f"模型推理失败 {image_path}: {e}")
                    continue

                # 解析JSON标注
                gt_annotations = self._parse_json_annotations(json_path)
                gt_boxes = [ann["bbox"] for ann in gt_annotations]
                gt_classes = [ann["class_id"] for ann in gt_annotations]

                # 检测各种错误类型
                self._check_missing_annotations(image_path, pred_boxes, pred_scores, pred_classes, gt_boxes)
                self._check_extra_annotations(image_path, pred_boxes, pred_scores, pred_classes, gt_boxes)
                self._check_class_mismatches(image_path, pred_boxes, pred_scores, pred_classes, gt_annotations)
                self._check_position_errors(image_path, pred_boxes, pred_scores, pred_classes, gt_annotations)
                self._check_duplicate_annotations(image_path, gt_annotations)
                self._check_size_errors(image_path, gt_annotations)

                # 生成可视化调试图像
                if self.config["enable_visual_debug"]:
                    self.generate_visual_debug(image_path, json_path, self.config["visual_debug_dir"])

        # 生成最终报告
        self._generate_report()
        elapsed_time = time.time() - self.start_time
        logger.info(f"检查完成! 共发现 {len(self.report)} 个潜在问题，耗时 {elapsed_time:.2f} 秒")
        logger.info(f"错误统计: {dict(self.error_stats)}")

    def _check_missing_annotations(self, image_path, pred_boxes, pred_scores, pred_classes, gt_boxes):
        """检测缺失标注 (模型检测到但标注中没有)"""
        for pred_box, score, class_id in zip(pred_boxes, pred_scores, pred_classes):
            matched = False
            for gt_box in gt_boxes:
                if self._calculate_iou(pred_box, gt_box) > self.config["iou_threshold"]:
                    matched = True
                    break

            if not matched and score > self.config["confidence_threshold"] * 1.2:  # 更高置信度要求
                self._add_error_report(
                    "可能缺失标注",
                    image_path,
                    f"类别: {ID_TO_LABEL.get(class_id, '未知')}",
                    position=pred_box,
                    confidence=f"{score:.2f}"
                )

    def _check_extra_annotations(self, image_path, pred_boxes, pred_scores, pred_classes, gt_boxes):
        """检测多余标注 (标注中有但模型没检测到)"""
        for gt_box in gt_boxes:
            matched = False
            max_iou = 0.0
            best_score = 0.0

            for pred_box, score in zip(pred_boxes, pred_scores):
                iou = self._calculate_iou(pred_box, gt_box)
                if iou > max_iou:
                    max_iou = iou
                    best_score = score

                if iou > self.config["iou_threshold"] and score > self.config["confidence_threshold"]:
                    matched = True
                    break

            if not matched:
                details = f"位置: {gt_box}"
                if max_iou > 0:
                    details += f" | 最大IoU: {max_iou:.2f} (置信度: {best_score:.2f})"
                self._add_error_report("可能多余标注", image_path, details)

    def _check_class_mismatches(self, image_path, pred_boxes, pred_scores, pred_classes, gt_annotations):
        """检测类别不匹配"""
        for gt_ann in gt_annotations:
            gt_box = gt_ann["bbox"]
            gt_class = gt_ann["class_id"]
            gt_label = gt_ann["original_label"]

            for pred_box, pred_score, pred_class in zip(pred_boxes, pred_scores, pred_classes):
                iou = self._calculate_iou(pred_box, gt_box)
                if iou > self.config["iou_threshold"]:
                    if pred_class != gt_class and pred_score > self.config["class_mismatch_threshold"]:
                        self._add_error_report(
                            "类别不匹配",
                            image_path,
                            f"标注类别: {ID_TO_LABEL.get(gt_class, '未知')}({gt_label}) | "
                            f"预测类别: {ID_TO_LABEL.get(pred_class, '未知')}",
                            position=gt_box,
                            IoU=f"{iou:.2f}",
                            confidence=f"{pred_score:.2f}",
                            json_path=gt_ann["json_path"]
                        )

    def _check_position_errors(self, image_path, pred_boxes, pred_scores, pred_classes, gt_annotations):
        """检测位置误差"""
        for gt_ann in gt_annotations:
            gt_box = gt_ann["bbox"]
            gt_class = gt_ann["class_id"]

            for pred_box, pred_score, pred_class in zip(pred_boxes, pred_scores, pred_classes):
                if pred_class == gt_class:
                    iou = self._calculate_iou(pred_box, gt_box)
                    if iou < self.config["position_error_threshold"] and pred_score > self.config[
                        "confidence_threshold"]:
                        self._add_error_report(
                            "位置误差",
                            image_path,
                            f"类别: {ID_TO_LABEL.get(gt_class, '未知')} | "
                            f"标注位置: {gt_box} | 预测位置: {pred_box}",
                            IoU=f"{iou:.2f}",
                            confidence=f"{pred_score:.2f}",
                            json_path=gt_ann["json_path"]
                        )

    def _check_duplicate_annotations(self, image_path, gt_annotations):
        """检测重复标注"""
        for i, ann1 in enumerate(gt_annotations):
            for j, ann2 in enumerate(gt_annotations[i + 1:], i + 1):
                iou = self._calculate_iou(ann1["bbox"], ann2["bbox"])
                if iou > self.config["iou_threshold"]:
                    self._add_error_report(
                        "重复标注",
                        image_path,
                        f"类别1: {ann1['class_name']} | 位置1: {ann1['bbox']} | "
                        f"类别2: {ann2['class_name']} | 位置2: {ann2['bbox']}",
                        IoU=f"{iou:.2f}",
                        json_path=ann1["json_path"]
                    )

    def _check_size_errors(self, image_path, gt_annotations):
        """检测异常大小的标注框"""
        for ann in gt_annotations:
            w = ann["bbox"][2] - ann["bbox"][0]
            h = ann["bbox"][3] - ann["bbox"][1]
            area = w * h

            # 检查最小面积
            if area < self.config["size_error_min_area"]:
                self._add_error_report(
                    "异常小标注",
                    image_path,
                    f"类别: {ann['class_name']} | 位置: {ann['bbox']}",
                    area=f"{area}px (小于 {self.config['size_error_min_area']}px)",
                    json_path=ann["json_path"]
                )

            # 检查最大面积比例
            max_allowed = ann["width"] * ann["height"] * self.config["size_error_max_ratio"]
            if area > max_allowed:
                self._add_error_report(
                    "异常大标注",
                    image_path,
                    f"类别: {ann['class_name']} | 位置: {ann['bbox']}",
                    area=f"{area}px (大于图像面积的 {self.config['size_error_max_ratio'] * 100:.1f}%)",
                    json_path=ann["json_path"]
                )

    def generate_visual_debug(self, image_path, json_path, output_dir):
        """生成可视化对比图"""
        img = cv2.imread(image_path)
        if img is None:
            return

        gt_annotations = self._parse_json_annotations(json_path)

        # 绘制标注框(绿色)
        for ann in gt_annotations:
            x1, y1, x2, y2 = map(int, ann["bbox"])
            cv2.rectangle(img, (x1, y1), (x2, y2), (0, 255, 0), 2)
            cv2.putText(img, ann["class_name"], (x1, y1 - 5),
                        cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 1)

        # 绘制预测框(红色)
        blob, original_size, scale = self._preprocess_image(image_path)
        if blob is not None:
            try:
                outputs = self.session.run([self.output_name], {self.input_name: blob})
                boxes, scores, classes = self._postprocess_output(outputs, original_size, scale)

                for box, score, cls in zip(boxes, scores, classes):
                    x1, y1, x2, y2 = map(int, box)
                    cv2.rectangle(img, (x1, y1), (x2, y2), (0, 0, 255), 2)
                    cv2.putText(img, f"{ID_TO_LABEL[cls]}:{score:.2f}", (x1, y1 - 25),
                                cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 255), 1)
            except Exception as e:
                logger.warning(f"生成可视化时推理失败: {e}")

        # 保存对比图
        out_path = os.path.join(output_dir, os.path.basename(image_path))
        cv2.imwrite(out_path, img)

    def _generate_report(self):
        """生成错误报告文件"""
        if not self.report:
            logger.info("未发现标注错误")
            with open(self.config["output_report"], 'w', encoding='utf-8') as f:
                f.write("跑胡子卡牌标注验证报告\n")
                f.write("=" * 50 + "\n")
                f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write("未发现任何标注错误\n")
            return

        # 按错误类型分类
        error_types = defaultdict(list)
        for line in self.report:
            error_type = line.split(":")[0].strip()
            error_types[error_type].append(line)

        # 写入报告文件
        with open(self.config["output_report"], 'w', encoding='utf-8') as f:
            f.write("跑胡子卡牌标注错误报告\n")
            f.write("=" * 50 + "\n")
            f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"模型路径: {self.config['model_path']}\n")
            f.write(f"检查文件夹: {self.config['folders_to_check']}\n")
            f.write(f"置信度阈值: {self.config['confidence_threshold']}\n")
            f.write(f"共发现 {len(self.report)} 个潜在问题\n\n")

            # 错误统计
            f.write("错误统计:\n")
            for err_type, count in sorted(self.error_stats.items(), key=lambda x: x[1], reverse=True):
                f.write(f"- {err_type}: {count} 个\n")
            f.write("\n")

            # 详细错误
            for error_type, items in sorted(error_types.items(), key=lambda x: len(x[1]), reverse=True):
                f.write(f"\n{error_type} ({len(items)} 个):\n")
                f.write("-" * 50 + "\n")
                f.write("\n".join(items) + "\n")

        logger.info(f"错误报告已生成: {self.config['output_report']}")


def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description="跑胡子卡牌标注验证脚本")
    parser.add_argument("--model-path", type=str, default=DEFAULT_CONFIG["model_path"],
                        help="ONNX模型路径")
    parser.add_argument("--image-dir", type=str, default=DEFAULT_CONFIG["image_dir"],
                        help="图像目录路径")
    parser.add_argument("--json-dir", type=str, default=DEFAULT_CONFIG["json_dir"],
                        help="JSON标注目录路径")
    parser.add_argument("--output-report", type=str, default=DEFAULT_CONFIG["output_report"],
                        help="输出报告文件路径")
    parser.add_argument("--folders", type=str, default=",".join(DEFAULT_CONFIG["folders_to_check"]),
                        help="要检查的文件夹列表，用逗号分隔")
    parser.add_argument("--confidence", type=float, default=DEFAULT_CONFIG["confidence_threshold"],
                        help="置信度阈值(0-1)")
    parser.add_argument("--visual-debug", action="store_true",
                        help="生成可视化调试图像")
    parser.add_argument("--no-gpu", action="store_true",
                        help="禁用GPU加速")
    parser.add_argument("--batch", action="store_true",
                        help="启用批处理模式测试不同置信度阈值")
    return parser.parse_args()


def run_batch_validation():
    """运行批处理验证"""
    base_config = DEFAULT_CONFIG.copy()
    for conf_thresh in base_config["batch_conf_thresholds"]:
        config = {
            **base_config,
            "confidence_threshold": conf_thresh,
            "output_report": f"report_conf_{conf_thresh}.txt",
            "enable_visual_debug": False  # 批处理时关闭可视化以提升速度
        }
        logger.info(f"\n{'=' * 50}\n开始批处理验证，置信度阈值: {conf_thresh}\n{'=' * 50}")
        validator = AnnotationValidator(config)
        validator.detect_annotation_errors()


if __name__ == "__main__":
    args = parse_args()

    # 更新配置
    config = DEFAULT_CONFIG.copy()
    config.update({
        "model_path": args.model_path,
        "image_dir": args.image_dir,
        "json_dir": args.json_dir,
        "output_report": args.output_report,
        "folders_to_check": [f.strip() for f in args.folders.split(",") if f.strip()],
        "confidence_threshold": args.confidence,
        "enable_visual_debug": args.visual_debug,
        "use_gpu": not args.no_gpu
    })

    if args.batch:
        run_batch_validation()
    else:
        validator = AnnotationValidator(config)
        validator.detect_annotation_errors()