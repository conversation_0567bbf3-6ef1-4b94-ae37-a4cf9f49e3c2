#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
数据验证和清洗层测试脚本
使用真实的YOLO检测结果测试验证和清洗功能
"""

import sys
import os
import cv2
import json
import numpy as np
from pathlib import Path

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from src.core.data_validator import DataValidationPipeline, ValidationResult
from src.core.detect import CardDetector

def test_with_real_images():
    """使用真实图片测试数据验证功能"""
    print("🔍 开始数据验证和清洗层测试...")
    
    # 配置参数
    model_path = "best.pt"
    image_dir = "legacy_assets/ceshi/calibration_gt/images"
    test_images = 5  # 测试前5张图片
    
    # 检查文件是否存在
    if not os.path.exists(model_path):
        print(f"❌ 模型文件不存在: {model_path}")
        return False
    
    if not os.path.exists(image_dir):
        print(f"❌ 图片目录不存在: {image_dir}")
        return False
    
    # 初始化检测器
    print("📦 初始化YOLO检测器...")
    detector = CardDetector(model_path)
    
    # 初始化数据验证管道
    print("🔧 初始化数据验证管道...")
    validation_config = {
        'validator': {
            'min_confidence': 0.3,
            'min_bbox_area': 100,
            'max_bbox_area': 50000,
        },
        'cleaner': {
            'duplicate_threshold': 0.8,
            'min_quality_score': 0.3,
        },
        'temporal': {
            'history_size': 3,
            'position_threshold': 50.0,
            'max_new_detections': 5,
        }
    }
    
    pipeline = DataValidationPipeline(validation_config)
    
    # 获取测试图片
    image_files = sorted([f for f in os.listdir(image_dir) if f.endswith('.jpg')])[:test_images]
    
    print(f"📊 测试{len(image_files)}张图片...")
    
    all_reports = []
    
    for i, img_file in enumerate(image_files):
        print(f"\n🖼️  处理图片 {i+1}/{len(image_files)}: {img_file}")
        
        # 读取图片
        img_path = os.path.join(image_dir, img_file)
        image = cv2.imread(img_path)
        
        if image is None:
            print(f"❌ 无法读取图片: {img_path}")
            continue
        
        # YOLO检测
        print("   🔍 YOLO检测中...")
        detections = detector.detect_image(image)
        
        # 格式化检测结果
        formatted_detections = []
        for j, det in enumerate(detections):
            formatted_det = {
                'id': f"{j}_{det.get('label', 'unknown')}",
                'conf': det.get('confidence', 0.0),
                'pos': det.get('bbox', [0, 0, 0, 0]),
                'group_id': det.get('group_id', 1)  # 默认设为1
            }
            formatted_detections.append(formatted_det)
        
        print(f"   📋 原始检测数量: {len(formatted_detections)}")
        
        # 数据验证和清洗
        print("   🧹 数据验证和清洗中...")
        validated_detections, report = pipeline.process(formatted_detections)
        
        print(f"   ✅ 验证后数量: {len(validated_detections)}")
        print(f"   ⏱️  处理时间: {report['processing_time']:.3f}秒")
        print(f"   ⚠️  警告数量: {report['warning_count']}")
        print(f"   ❌ 拒绝数量: {report['rejected_count']}")
        print(f"   🧽 清洗数量: {report['cleaned_count']}")
        
        # 显示质量分数统计
        if report['quality_scores']:
            avg_quality = np.mean(report['quality_scores'])
            min_quality = np.min(report['quality_scores'])
            max_quality = np.max(report['quality_scores'])
            print(f"   📊 质量分数: 平均={avg_quality:.3f}, 最小={min_quality:.3f}, 最大={max_quality:.3f}")
        
        # 显示处理日志（前3条）
        if report['log']:
            print("   📝 处理日志:")
            for log_entry in report['log'][:3]:
                print(f"      - {log_entry}")
            if len(report['log']) > 3:
                print(f"      ... 还有{len(report['log']) - 3}条日志")
        
        all_reports.append(report)
    
    # 显示总体统计
    print("\n" + "="*60)
    print("📊 总体统计信息:")
    
    stats = pipeline.get_statistics()
    print(f"   总处理数量: {stats['total_processed']}")
    print(f"   总拒绝数量: {stats['total_rejected']}")
    print(f"   总警告数量: {stats['total_warnings']}")
    print(f"   总清洗数量: {stats['total_cleaned']}")
    print(f"   平均处理时间: {stats['average_processing_time']:.3f}秒")
    print(f"   拒绝率: {stats['rejection_rate']:.1%}")
    print(f"   警告率: {stats['warning_rate']:.1%}")
    
    # 分析处理效果
    print("\n📈 处理效果分析:")
    
    total_input = sum(r['input_count'] for r in all_reports)
    total_output = sum(r['output_count'] for r in all_reports)
    total_rejected = sum(r['rejected_count'] for r in all_reports)
    total_cleaned = sum(r['cleaned_count'] for r in all_reports)
    
    if total_input > 0:
        retention_rate = total_output / total_input
        print(f"   数据保留率: {retention_rate:.1%}")
        print(f"   数据拒绝率: {total_rejected/total_input:.1%}")
        print(f"   数据清洗率: {total_cleaned/total_input:.1%}")
    
    # 评估验证效果
    print("\n🎯 验证效果评估:")
    
    if stats['rejection_rate'] > 0.3:
        print("   ⚠️  拒绝率较高，可能需要调整验证阈值")
    elif stats['rejection_rate'] < 0.05:
        print("   ⚠️  拒绝率较低，验证可能过于宽松")
    else:
        print("   ✅ 拒绝率适中，验证效果良好")
    
    if stats['warning_rate'] > 0.2:
        print("   ⚠️  警告率较高，数据质量可能存在问题")
    else:
        print("   ✅ 警告率正常")
    
    if stats['average_processing_time'] > 0.1:
        print("   ⚠️  处理时间较长，可能需要性能优化")
    else:
        print("   ✅ 处理速度良好")
    
    return True

def test_synthetic_data():
    """使用合成数据测试边界情况"""
    print("\n🧪 合成数据边界测试...")
    
    pipeline = DataValidationPipeline()
    
    # 测试用例1：正常数据
    normal_detections = [
        {'id': '1_二', 'conf': 0.85, 'pos': [100, 100, 50, 70], 'group_id': 1},
        {'id': '2_七', 'conf': 0.92, 'pos': [200, 100, 50, 70], 'group_id': 1},
        {'id': '3_十', 'conf': 0.78, 'pos': [300, 100, 50, 70], 'group_id': 1},
    ]
    
    validated, report = pipeline.process(normal_detections)
    print(f"✅ 正常数据测试: 输入{len(normal_detections)}, 输出{len(validated)}, 拒绝{report['rejected_count']}")
    
    # 测试用例2：异常数据
    abnormal_detections = [
        {'id': '1_二', 'conf': 0.1, 'pos': [100, 100, 50, 70], 'group_id': 1},  # 低置信度
        {'id': '2_invalid', 'conf': 0.9, 'pos': [200, 100, 50, 70], 'group_id': 1},  # 无效标签
        {'id': '3_七', 'conf': 1.5, 'pos': [-10, 100, 50, 70], 'group_id': 1},  # 异常置信度和位置
        {'id': '4_十', 'conf': 0.8, 'pos': [300, 100, 1, 1], 'group_id': 1},  # 过小尺寸
    ]
    
    validated, report = pipeline.process(abnormal_detections)
    print(f"🔧 异常数据测试: 输入{len(abnormal_detections)}, 输出{len(validated)}, 拒绝{report['rejected_count']}")
    
    # 测试用例3：重复数据
    duplicate_detections = [
        {'id': '1_二', 'conf': 0.85, 'pos': [100, 100, 50, 70], 'group_id': 1},
        {'id': '2_二', 'conf': 0.80, 'pos': [105, 105, 50, 70], 'group_id': 1},  # 重复检测
        {'id': '3_二', 'conf': 0.90, 'pos': [102, 102, 50, 70], 'group_id': 1},  # 重复检测
    ]
    
    validated, report = pipeline.process(duplicate_detections)
    print(f"🔄 重复数据测试: 输入{len(duplicate_detections)}, 输出{len(validated)}, 清洗{report['cleaned_count']}")
    
    # 测试用例4：空数据
    empty_detections = []
    validated, report = pipeline.process(empty_detections)
    print(f"🗂️  空数据测试: 输入{len(empty_detections)}, 输出{len(validated)}")
    
    print("✅ 合成数据测试完成")

def main():
    """主函数"""
    print("🚀 数据验证和清洗层测试开始...\n")
    
    try:
        # 测试真实数据
        success = test_with_real_images()
        
        if success:
            # 测试合成数据
            test_synthetic_data()
            
            print("\n" + "="*60)
            print("🎉 数据验证和清洗层测试完成！")
            print("\n📋 测试总结:")
            print("✅ 数据验证器 - 成功验证检测结果合理性")
            print("✅ 数据清洗器 - 成功去除重复和异常数据")
            print("✅ 时间一致性验证器 - 成功检测帧间异常")
            print("✅ 验证管道 - 成功整合所有功能")
            
            print("\n🎯 下一步建议:")
            print("1. 根据测试结果调整验证阈值")
            print("2. 集成到主检测流程中")
            print("3. 进行更大规模的测试")
            
        else:
            print("❌ 测试失败，请检查配置和文件路径")
            
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
