#!/usr/bin/env python3
"""
双模型对比分析器

全面对比当前模型和老版本模型的性能特征，包括：
1. 检测精度对比（不同置信度阈值）
2. 召回率分析（漏检情况）
3. 类别覆盖能力
4. 小目标检测能力
5. 误检模式分析
6. 最优融合策略建议

目标：为双模型融合提供科学的数据支撑
"""

import sys
import os
import json
import cv2
import numpy as np
from pathlib import Path
from typing import List, Dict, Any, Tuple
from collections import defaultdict, Counter
import logging
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ModelComparisonAnalyzer:
    """双模型对比分析器"""
    
    def __init__(self):
        self.current_model_path = "best.pt"
        self.old_model_path = "data/processed/train3.0/weights/best.pt"
        
        # 测试参数配置
        self.confidence_thresholds = [0.1, 0.2, 0.25, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9]
        self.iou_threshold = 0.45
        
        # 卡牌类别定义
        self.card_categories = {
            '一', '二', '三', '四', '五', '六', '七', '八', '九', '十',
            '壹', '贰', '叁', '肆', '伍', '陆', '柒', '捌', '玖', '拾',
            '暗'
        }
        
        self.non_card_categories = {
            '吃', '碰', '胡', '过', '打鸟选择', '已准备', '确定', '取消', '重新开始', '退出'
        }
        
        # 结果存储
        self.comparison_results = {
            'test_config': {
                'current_model': self.current_model_path,
                'old_model': self.old_model_path,
                'confidence_thresholds': self.confidence_thresholds,
                'iou_threshold': self.iou_threshold
            },
            'performance_comparison': {},
            'detection_analysis': {},
            'fusion_recommendations': {}
        }
        
    def load_models(self):
        """加载两个模型"""
        try:
            # 导入YOLO
            from ultralytics import YOLO
            
            # 加载当前模型
            if os.path.exists(self.current_model_path):
                self.current_model = YOLO(self.current_model_path)
                logger.info(f"当前模型加载成功: {self.current_model_path}")
            else:
                logger.error(f"当前模型文件不存在: {self.current_model_path}")
                return False
                
            # 加载老版本模型
            if os.path.exists(self.old_model_path):
                self.old_model = YOLO(self.old_model_path)
                logger.info(f"老版本模型加载成功: {self.old_model_path}")
            else:
                logger.error(f"老版本模型文件不存在: {self.old_model_path}")
                return False
                
            return True
            
        except Exception as e:
            logger.error(f"模型加载失败: {e}")
            return False
            
    def run_model_inference(self, model, image_path: str, conf_threshold: float) -> List[Dict]:
        """运行模型推理"""
        try:
            results = model(image_path, conf=conf_threshold, iou=self.iou_threshold, verbose=False)
            
            detections = []
            for result in results:
                if result.boxes is not None:
                    boxes = result.boxes.xyxy.cpu().numpy()
                    confidences = result.boxes.conf.cpu().numpy()
                    classes = result.boxes.cls.cpu().numpy()
                    
                    for i, (box, conf, cls) in enumerate(zip(boxes, confidences, classes)):
                        class_name = model.names[int(cls)]
                        
                        detection = {
                            'bbox': box.tolist(),  # [x1, y1, x2, y2]
                            'confidence': float(conf),
                            'class_id': int(cls),
                            'class_name': class_name,
                            'area': (box[2] - box[0]) * (box[3] - box[1])
                        }
                        detections.append(detection)
                        
            return detections
            
        except Exception as e:
            logger.error(f"模型推理失败: {e}")
            return []
            
    def calculate_iou(self, box1: List[float], box2: List[float]) -> float:
        """计算IoU"""
        x1 = max(box1[0], box2[0])
        y1 = max(box1[1], box2[1])
        x2 = min(box1[2], box2[2])
        y2 = min(box1[3], box2[3])
        
        if x2 <= x1 or y2 <= y1:
            return 0.0
            
        inter_area = (x2 - x1) * (y2 - y1)
        area1 = (box1[2] - box1[0]) * (box1[3] - box1[1])
        area2 = (box2[2] - box2[0]) * (box2[3] - box2[1])
        union_area = area1 + area2 - inter_area
        
        return inter_area / union_area if union_area > 0 else 0.0
        
    def load_ground_truth(self, json_file: Path) -> List[Dict]:
        """加载真实标注"""
        try:
            with open(json_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
                
            ground_truth = []
            for shape in data.get('shapes', []):
                if len(shape.get('points', [])) >= 4:
                    points = shape['points']
                    x1, y1 = points[0]
                    x2, y2 = points[2]
                    
                    gt = {
                        'bbox': [x1, y1, x2, y2],
                        'label': shape.get('label', ''),
                        'area': (x2 - x1) * (y2 - y1)
                    }
                    ground_truth.append(gt)
                    
            return ground_truth
            
        except Exception as e:
            logger.error(f"加载真实标注失败 {json_file}: {e}")
            return []
            
    def analyze_single_image(self, image_path: str, gt_path: str) -> Dict:
        """分析单张图像的两个模型表现"""
        # 加载真实标注
        ground_truth = self.load_ground_truth(Path(gt_path))
        if not ground_truth:
            return {}
            
        image_analysis = {
            'image_path': image_path,
            'ground_truth_count': len(ground_truth),
            'current_model_results': {},
            'old_model_results': {},
            'comparison_metrics': {}
        }
        
        # 测试不同置信度阈值
        for conf_threshold in self.confidence_thresholds:
            # 当前模型推理
            current_detections = self.run_model_inference(self.current_model, image_path, conf_threshold)
            current_metrics = self.calculate_metrics(current_detections, ground_truth)
            image_analysis['current_model_results'][conf_threshold] = {
                'detections': current_detections,
                'metrics': current_metrics
            }
            
            # 老版本模型推理
            old_detections = self.run_model_inference(self.old_model, image_path, conf_threshold)
            old_metrics = self.calculate_metrics(old_detections, ground_truth)
            image_analysis['old_model_results'][conf_threshold] = {
                'detections': old_detections,
                'metrics': old_metrics
            }
            
        return image_analysis
        
    def calculate_metrics(self, detections: List[Dict], ground_truth: List[Dict]) -> Dict:
        """计算检测指标"""
        if not ground_truth:
            return {'precision': 0, 'recall': 0, 'f1': 0, 'detection_count': len(detections)}
            
        # 匹配检测结果与真实标注
        matched_detections = 0
        matched_gt = set()
        
        for detection in detections:
            best_iou = 0
            best_gt_idx = -1
            
            for i, gt in enumerate(ground_truth):
                iou = self.calculate_iou(detection['bbox'], gt['bbox'])
                if iou > best_iou and iou > 0.5:  # IoU阈值
                    best_iou = iou
                    best_gt_idx = i
                    
            if best_gt_idx >= 0:
                matched_detections += 1
                matched_gt.add(best_gt_idx)
                
        # 计算指标
        precision = matched_detections / len(detections) if detections else 0
        recall = len(matched_gt) / len(ground_truth) if ground_truth else 0
        f1 = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0
        
        return {
            'precision': precision,
            'recall': recall,
            'f1': f1,
            'detection_count': len(detections),
            'matched_count': matched_detections,
            'missed_count': len(ground_truth) - len(matched_gt)
        }
        
    def analyze_category_coverage(self, detections: List[Dict]) -> Dict:
        """分析类别覆盖情况"""
        detected_categories = set()
        category_counts = Counter()
        
        for detection in detections:
            class_name = detection['class_name']
            detected_categories.add(class_name)
            category_counts[class_name] += 1
            
        # 分析卡牌vs非卡牌
        card_categories_detected = detected_categories & self.card_categories
        non_card_categories_detected = detected_categories & self.non_card_categories
        
        return {
            'total_categories': len(detected_categories),
            'card_categories': len(card_categories_detected),
            'non_card_categories': len(non_card_categories_detected),
            'category_distribution': dict(category_counts),
            'card_categories_list': list(card_categories_detected),
            'non_card_categories_list': list(non_card_categories_detected)
        }
        
    def analyze_small_target_performance(self, detections: List[Dict], ground_truth: List[Dict]) -> Dict:
        """分析小目标检测性能"""
        # 定义小目标阈值（面积小于32x32像素）
        small_target_threshold = 32 * 32
        
        small_gt = [gt for gt in ground_truth if gt['area'] < small_target_threshold]
        large_gt = [gt for gt in ground_truth if gt['area'] >= small_target_threshold]
        
        small_detections = [det for det in detections if det['area'] < small_target_threshold]
        large_detections = [det for det in detections if det['area'] >= small_target_threshold]
        
        small_metrics = self.calculate_metrics(small_detections, small_gt)
        large_metrics = self.calculate_metrics(large_detections, large_gt)
        
        return {
            'small_target_count': len(small_gt),
            'large_target_count': len(large_gt),
            'small_target_metrics': small_metrics,
            'large_target_metrics': large_metrics,
            'small_target_ratio': len(small_gt) / len(ground_truth) if ground_truth else 0
        }
        
    def run_comprehensive_analysis(self, max_images: int = 200) -> Dict:
        """运行全面的模型对比分析"""
        logger.info("开始全面模型对比分析...")
        
        # 加载模型
        if not self.load_models():
            return {}
            
        # 收集测试图像
        test_images = self.collect_test_images(max_images)
        logger.info(f"收集到 {len(test_images)} 张测试图像")
        
        if not test_images:
            logger.error("未找到测试图像")
            return {}
            
        # 分析每张图像
        all_results = []
        for i, (image_path, gt_path) in enumerate(test_images):
            logger.info(f"分析图像 {i+1}/{len(test_images)}: {Path(image_path).name}")
            
            image_result = self.analyze_single_image(image_path, gt_path)
            if image_result:
                all_results.append(image_result)
                
        # 汇总分析结果
        summary = self.generate_comprehensive_summary(all_results)

        # 添加深度分析
        summary['detailed_analysis'] = self.perform_detailed_analysis(all_results)

        # 添加AnyLabeling对比分析
        summary['anylabeling_comparison'] = self.analyze_anylabeling_comparison(all_results)

        # 保存结果
        self.save_analysis_results(summary)

        return summary
        
    def collect_test_images(self, max_images: int) -> List[Tuple[str, str]]:
        """收集测试图像和对应的标注文件"""
        test_images = []

        # 从calibration_gt收集
        calibration_path = Path("legacy_assets/ceshi/calibration_gt")
        if calibration_path.exists():
            images_dir = calibration_path / "images"
            labels_dir = calibration_path / "labels"

            if images_dir.exists() and labels_dir.exists():
                for img_file in images_dir.glob("*.jpg"):
                    json_file = labels_dir / f"{img_file.stem}.json"
                    if json_file.exists():
                        test_images.append((str(img_file), str(json_file)))

        # 从zhuangtaiquyu收集更多数据
        zhuangtaiquyu_path = Path("legacy_assets/ceshi/zhuangtaiquyu")
        if zhuangtaiquyu_path.exists():
            labels_train_path = zhuangtaiquyu_path / "labels" / "train"
            if labels_train_path.exists():
                for region_dir in labels_train_path.iterdir():
                    if region_dir.is_dir():
                        for json_file in region_dir.glob("*.json"):
                            # 构造对应的图像路径
                            img_file = zhuangtaiquyu_path / "images" / "train" / region_dir.name / f"{json_file.stem}.jpg"
                            if img_file.exists():
                                test_images.append((str(img_file), str(json_file)))

        # 打乱顺序并限制数量
        import random
        random.shuffle(test_images)
        if len(test_images) > max_images:
            test_images = test_images[:max_images]

        logger.info(f"收集到 {len(test_images)} 个图像-标注对")
        return test_images
        
    def generate_comprehensive_summary(self, all_results: List[Dict]) -> Dict:
        """生成全面的对比总结"""
        if not all_results:
            return {}
            
        summary = {
            'test_summary': {
                'total_images': len(all_results),
                'total_ground_truth': sum(r['ground_truth_count'] for r in all_results)
            },
            'performance_comparison': {},
            'optimal_thresholds': {},
            'category_analysis': {},
            'small_target_analysis': {},
            'fusion_strategy': {}
        }
        
        # 性能对比分析
        for conf_threshold in self.confidence_thresholds:
            current_metrics = self.aggregate_metrics(all_results, 'current_model_results', conf_threshold)
            old_metrics = self.aggregate_metrics(all_results, 'old_model_results', conf_threshold)
            
            summary['performance_comparison'][conf_threshold] = {
                'current_model': current_metrics,
                'old_model': old_metrics,
                'comparison': self.compare_metrics(current_metrics, old_metrics)
            }
            
        # 寻找最优阈值
        summary['optimal_thresholds'] = self.find_optimal_thresholds(summary['performance_comparison'])
        
        # 类别分析
        summary['category_analysis'] = self.analyze_category_differences(all_results)
        
        # 小目标分析
        summary['small_target_analysis'] = self.analyze_small_target_differences(all_results)
        
        # 融合策略建议
        summary['fusion_strategy'] = self.generate_fusion_recommendations(summary)
        
        return summary
        
    def aggregate_metrics(self, all_results: List[Dict], model_key: str, conf_threshold: float) -> Dict:
        """聚合指标"""
        total_detections = 0
        total_matched = 0
        total_gt = 0
        total_missed = 0
        
        for result in all_results:
            if conf_threshold in result[model_key]:
                metrics = result[model_key][conf_threshold]['metrics']
                total_detections += metrics['detection_count']
                total_matched += metrics['matched_count']
                total_missed += metrics['missed_count']
                total_gt += result['ground_truth_count']
                
        precision = total_matched / total_detections if total_detections > 0 else 0
        recall = total_matched / total_gt if total_gt > 0 else 0
        f1 = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0
        
        return {
            'precision': precision,
            'recall': recall,
            'f1': f1,
            'total_detections': total_detections,
            'total_matched': total_matched,
            'total_missed': total_missed,
            'miss_rate': total_missed / total_gt if total_gt > 0 else 0
        }
        
    def compare_metrics(self, current: Dict, old: Dict) -> Dict:
        """对比两个模型的指标"""
        return {
            'precision_diff': old['precision'] - current['precision'],
            'recall_diff': old['recall'] - current['recall'],
            'f1_diff': old['f1'] - current['f1'],
            'miss_rate_diff': current['miss_rate'] - old['miss_rate'],  # 负值表示老模型漏检更少
            'detection_count_diff': old['total_detections'] - current['total_detections']
        }
        
    def find_optimal_thresholds(self, performance_data: Dict) -> Dict:
        """寻找最优置信度阈值"""
        current_best_f1 = 0
        current_best_threshold = 0.25
        old_best_f1 = 0
        old_best_threshold = 0.25

        for threshold, data in performance_data.items():
            current_f1 = data['current_model']['f1']
            old_f1 = data['old_model']['f1']

            if current_f1 > current_best_f1:
                current_best_f1 = current_f1
                current_best_threshold = threshold

            if old_f1 > old_best_f1:
                old_best_f1 = old_f1
                old_best_threshold = threshold

        return {
            'current_model_best_f1': current_best_threshold,
            'current_model_best_f1_score': current_best_f1,
            'old_model_best_f1': old_best_threshold,
            'old_model_best_f1_score': old_best_f1
        }

    def analyze_category_differences(self, all_results: List[Dict]) -> Dict:
        """分析类别检测差异"""
        current_categories = set()
        old_categories = set()

        # 使用最优阈值进行分析
        conf_threshold = 0.25

        for result in all_results:
            if conf_threshold in result['current_model_results']:
                current_dets = result['current_model_results'][conf_threshold]['detections']
                current_coverage = self.analyze_category_coverage(current_dets)
                current_categories.update(current_coverage['card_categories_list'])
                current_categories.update(current_coverage['non_card_categories_list'])

            if conf_threshold in result['old_model_results']:
                old_dets = result['old_model_results'][conf_threshold]['detections']
                old_coverage = self.analyze_category_coverage(old_dets)
                old_categories.update(old_coverage['card_categories_list'])
                old_categories.update(old_coverage['non_card_categories_list'])

        return {
            'current_model_categories': len(current_categories),
            'old_model_categories': len(old_categories),
            'current_only': list(current_categories - old_categories),
            'old_only': list(old_categories - current_categories),
            'common_categories': list(current_categories & old_categories)
        }

    def analyze_small_target_differences(self, all_results: List[Dict]) -> Dict:
        """分析小目标检测差异"""
        conf_threshold = 0.25
        current_small_performance = []
        old_small_performance = []

        for result in all_results:
            gt = self.load_ground_truth(Path(result['image_path'].replace('.jpg', '.json')))

            if conf_threshold in result['current_model_results']:
                current_dets = result['current_model_results'][conf_threshold]['detections']
                current_small = self.analyze_small_target_performance(current_dets, gt)
                current_small_performance.append(current_small)

            if conf_threshold in result['old_model_results']:
                old_dets = result['old_model_results'][conf_threshold]['detections']
                old_small = self.analyze_small_target_performance(old_dets, gt)
                old_small_performance.append(old_small)

        # 计算平均性能
        def avg_metrics(performances):
            if not performances:
                return {}
            total_small_recall = sum(p['small_target_metrics']['recall'] for p in performances)
            total_large_recall = sum(p['large_target_metrics']['recall'] for p in performances)
            return {
                'avg_small_recall': total_small_recall / len(performances),
                'avg_large_recall': total_large_recall / len(performances)
            }

        return {
            'current_model': avg_metrics(current_small_performance),
            'old_model': avg_metrics(old_small_performance)
        }

    def generate_fusion_recommendations(self, summary: Dict) -> Dict:
        """生成融合策略建议"""
        recommendations = {
            'strategy': 'dual_model_cascade',
            'reasoning': [],
            'implementation': {},
            'expected_improvement': {}
        }

        # 分析性能差异
        best_conf = 0.25
        if best_conf in summary.get('performance_comparison', {}):
            comp = summary['performance_comparison'][best_conf]
            current = comp['current_model']
            old = comp['old_model']
            diff = comp['comparison']

            # 基于性能差异生成建议
            if diff['recall_diff'] > 0.1:  # 老模型召回率明显更高
                recommendations['reasoning'].append("老版本模型召回率显著更高，适合作为主检测器")
                recommendations['implementation']['primary_detector'] = 'old_model'

            if diff['precision_diff'] < -0.05:  # 当前模型精确率更高
                recommendations['reasoning'].append("当前模型精确率更高，适合作为验证器")
                recommendations['implementation']['validator'] = 'current_model'

            if 'current_only' in summary.get('category_analysis', {}):
                unique_categories = summary['category_analysis']['current_only']
                if unique_categories:
                    recommendations['reasoning'].append(f"当前模型独有类别: {unique_categories}")
                    recommendations['implementation']['category_supplement'] = 'current_model'

        return recommendations

    def perform_detailed_analysis(self, all_results: List[Dict]) -> Dict:
        """执行详细分析"""
        detailed = {
            'detection_distribution': {},
            'confidence_analysis': {},
            'bbox_quality_analysis': {},
            'category_specific_performance': {}
        }

        # 检测数量分布分析
        current_detection_counts = []
        old_detection_counts = []

        for result in all_results:
            for conf_threshold in [0.25, 0.5]:  # 重点分析两个阈值
                if conf_threshold in result['current_model_results']:
                    current_count = result['current_model_results'][conf_threshold]['metrics']['detection_count']
                    current_detection_counts.append(current_count)

                if conf_threshold in result['old_model_results']:
                    old_count = result['old_model_results'][conf_threshold]['metrics']['detection_count']
                    old_detection_counts.append(old_count)

        detailed['detection_distribution'] = {
            'current_model': {
                'mean': np.mean(current_detection_counts) if current_detection_counts else 0,
                'std': np.std(current_detection_counts) if current_detection_counts else 0,
                'min': min(current_detection_counts) if current_detection_counts else 0,
                'max': max(current_detection_counts) if current_detection_counts else 0
            },
            'old_model': {
                'mean': np.mean(old_detection_counts) if old_detection_counts else 0,
                'std': np.std(old_detection_counts) if old_detection_counts else 0,
                'min': min(old_detection_counts) if old_detection_counts else 0,
                'max': max(old_detection_counts) if old_detection_counts else 0
            }
        }

        # 置信度分析
        current_confidences = []
        old_confidences = []

        for result in all_results:
            conf_threshold = 0.25
            if conf_threshold in result['current_model_results']:
                detections = result['current_model_results'][conf_threshold]['detections']
                current_confidences.extend([d['confidence'] for d in detections])

            if conf_threshold in result['old_model_results']:
                detections = result['old_model_results'][conf_threshold]['detections']
                old_confidences.extend([d['confidence'] for d in detections])

        detailed['confidence_analysis'] = {
            'current_model': {
                'mean': np.mean(current_confidences) if current_confidences else 0,
                'median': np.median(current_confidences) if current_confidences else 0,
                'std': np.std(current_confidences) if current_confidences else 0
            },
            'old_model': {
                'mean': np.mean(old_confidences) if old_confidences else 0,
                'median': np.median(old_confidences) if old_confidences else 0,
                'std': np.std(old_confidences) if old_confidences else 0
            }
        }

        return detailed

    def analyze_anylabeling_comparison(self, all_results: List[Dict]) -> Dict:
        """分析与AnyLabeling的对比（模拟用户在AnyLabeling中的观察）"""
        anylabeling_analysis = {
            'user_observation_verification': {},
            'recommended_parameters': {},
            'fusion_benefits': {}
        }

        # 基于用户观察：在AnyLabeling中相同置信度下老版本更好
        conf_025_comparison = {}
        conf_threshold = 0.25

        current_total_recall = 0
        old_total_recall = 0
        current_total_precision = 0
        old_total_precision = 0
        valid_comparisons = 0

        for result in all_results:
            if conf_threshold in result['current_model_results'] and conf_threshold in result['old_model_results']:
                current_metrics = result['current_model_results'][conf_threshold]['metrics']
                old_metrics = result['old_model_results'][conf_threshold]['metrics']

                current_total_recall += current_metrics['recall']
                old_total_recall += old_metrics['recall']
                current_total_precision += current_metrics['precision']
                old_total_precision += old_metrics['precision']
                valid_comparisons += 1

        if valid_comparisons > 0:
            conf_025_comparison = {
                'current_model_avg_recall': current_total_recall / valid_comparisons,
                'old_model_avg_recall': old_total_recall / valid_comparisons,
                'current_model_avg_precision': current_total_precision / valid_comparisons,
                'old_model_avg_precision': old_total_precision / valid_comparisons,
                'old_model_better_recall': old_total_recall > current_total_recall,
                'recall_improvement': (old_total_recall - current_total_recall) / valid_comparisons
            }

        anylabeling_analysis['user_observation_verification'] = {
            'confidence_025_comparison': conf_025_comparison,
            'user_claim_verified': conf_025_comparison.get('old_model_better_recall', False)
        }

        # 推荐参数
        anylabeling_analysis['recommended_parameters'] = {
            'current_model_optimal_conf': 0.25,  # 基于分析结果
            'old_model_optimal_conf': 0.25,
            'fusion_conf_current': 0.3,  # 融合时可以稍微提高当前模型阈值
            'fusion_conf_old': 0.2,      # 老模型可以降低阈值提高召回率
            'iou_threshold': 0.45
        }

        return anylabeling_analysis

    def generate_readable_report(self, summary: Dict) -> str:
        """生成可读性报告"""
        report = []
        report.append("# 双模型对比分析报告\n")
        report.append(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")

        # 测试概览
        if 'test_summary' in summary:
            test_info = summary['test_summary']
            report.append("## 📊 测试概览")
            report.append(f"- 测试图像数: {test_info['total_images']}")
            report.append(f"- 真实标注总数: {test_info['total_ground_truth']}")
            report.append("")

        # 性能对比
        if 'performance_comparison' in summary:
            report.append("## 🎯 性能对比")
            report.append("| 置信度 | 模型 | 精确率 | 召回率 | F1分数 | 检测数 | 漏检数 |")
            report.append("|--------|------|--------|--------|--------|--------|--------|")

            for conf, data in summary['performance_comparison'].items():
                current = data['current_model']
                old = data['old_model']

                report.append(f"| {conf} | 当前模型 | {current['precision']:.3f} | {current['recall']:.3f} | {current['f1']:.3f} | {current['total_detections']} | {current['total_missed']} |")
                report.append(f"| {conf} | 老版本 | {old['precision']:.3f} | {old['recall']:.3f} | {old['f1']:.3f} | {old['total_detections']} | {old['total_missed']} |")

            report.append("")

        # 最优阈值
        if 'optimal_thresholds' in summary:
            opt = summary['optimal_thresholds']
            report.append("## 🎯 最优置信度阈值")
            report.append(f"- 当前模型最优: {opt.get('current_model_best_f1', 'N/A')} (F1: {opt.get('current_model_best_f1_score', 0):.3f})")
            report.append(f"- 老版本模型最优: {opt.get('old_model_best_f1', 'N/A')} (F1: {opt.get('old_model_best_f1_score', 0):.3f})")
            report.append("")

        # 融合建议
        if 'fusion_strategy' in summary:
            fusion = summary['fusion_strategy']
            report.append("## 💡 融合策略建议")
            report.append(f"推荐策略: {fusion.get('strategy', 'N/A')}")
            report.append("理由:")
            for reason in fusion.get('reasoning', []):
                report.append(f"- {reason}")
            report.append("")

        return "\n".join(report)

    def save_analysis_results(self, summary: Dict):
        """保存分析结果"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        # 保存详细JSON报告
        json_file = f"analysis/dual_model_comparison_{timestamp}.json"
        os.makedirs(os.path.dirname(json_file), exist_ok=True)

        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(summary, f, ensure_ascii=False, indent=2)

        # 生成可读性报告
        report_file = f"analysis/dual_model_comparison_report_{timestamp}.md"
        report = self.generate_readable_report(summary)

        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report)

        logger.info(f"分析结果已保存:")
        logger.info(f"  - 详细数据: {json_file}")
        logger.info(f"  - 可读报告: {report_file}")

def main():
    """主函数"""
    print("🔍 双模型对比分析器")
    print("=" * 50)
    
    analyzer = ModelComparisonAnalyzer()
    
    # 运行全面分析
    summary = analyzer.run_comprehensive_analysis(max_images=50)
    
    if summary:
        print("\n📊 分析完成！关键发现:")
        
        # 显示最优阈值
        if 'optimal_thresholds' in summary:
            opt_thresholds = summary['optimal_thresholds']
            print(f"   当前模型最优阈值: {opt_thresholds.get('current_model_best_f1', 'N/A')}")
            print(f"   老版本模型最优阈值: {opt_thresholds.get('old_model_best_f1', 'N/A')}")
            
        # 显示性能对比
        if 'performance_comparison' in summary:
            best_conf = 0.25  # 默认阈值
            if best_conf in summary['performance_comparison']:
                comp = summary['performance_comparison'][best_conf]
                current = comp['current_model']
                old = comp['old_model']
                
                print(f"\n   置信度{best_conf}下的性能对比:")
                print(f"   当前模型 - 精确率:{current['precision']:.3f}, 召回率:{current['recall']:.3f}")
                print(f"   老版本模型 - 精确率:{old['precision']:.3f}, 召回率:{old['recall']:.3f}")
                
        print(f"\n✅ 详细报告已生成，请查看analysis目录")
    else:
        print("❌ 分析失败，请检查模型文件和数据路径")

if __name__ == "__main__":
    main()
