#!/usr/bin/env python3
"""
调试版本的Frame_00257.jpg处理器

专门用于重新处理frame_00256和frame_00257，添加详细的调试日志
来追踪"2叁"→"2伍"错误的具体发生位置
"""

import os
import sys
import json
import logging
from pathlib import Path
from typing import Dict, List, Any, Optional
from datetime import datetime

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 导入项目核心模块
from src.modules.phase2_integrator import Phase2Integrator
from src.modules.simple_inheritor import SimpleInheritor
from src.modules.region_transitioner import RegionTransitioner
from src.modules.basic_id_assigner import BasicIDAssigner

# 配置详细的调试日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('debug_frame_00257_processing.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class DebugFrame00257Processor:
    """调试版本的Frame_00257处理器"""
    
    def __init__(self):
        self.source_dir = "legacy_assets/ceshi/calibration_gt"
        self.target_frames = ["frame_00256", "frame_00257"]
        self.debug_results = {}
        
        # 创建调试版本的处理器实例
        self.integrator = Phase2Integrator()
        
        # 启用所有模块的详细日志
        self._enable_debug_logging()
        
    def _enable_debug_logging(self):
        """启用所有相关模块的详细日志"""
        debug_loggers = [
            'src.modules.phase2_integrator',
            'src.modules.simple_inheritor',
            'src.modules.region_transitioner',
            'src.modules.basic_id_assigner',
            'src.modules.dark_card_processor',
            'src.modules.occlusion_compensator'
        ]
        
        for logger_name in debug_loggers:
            debug_logger = logging.getLogger(logger_name)
            debug_logger.setLevel(logging.DEBUG)
            
        logger.info("🔧 已启用所有模块的详细调试日志")
        
    def process_debug_frames(self):
        """调试处理目标帧"""
        logger.info("🔍 开始调试处理frame_00256和frame_00257...")
        
        for frame_name in self.target_frames:
            logger.info(f"🔄 调试处理 {frame_name}...")
            
            try:
                # 读取原始数据
                original_data = self._load_frame_data(frame_name)
                if not original_data:
                    continue
                    
                # 转换为检测格式
                detections = self._convert_to_detections(original_data)
                
                # 详细分析原始检测数据
                self._analyze_original_detections(frame_name, detections)
                
                # 调试处理
                result = self._debug_process_frame(frame_name, detections)
                
                # 分析处理结果
                self._analyze_processing_result(frame_name, result)
                
                # 保存调试结果
                self.debug_results[frame_name] = {
                    "original_data": original_data,
                    "detections": detections,
                    "processing_result": result
                }
                
            except Exception as e:
                logger.error(f"❌ 调试处理 {frame_name} 失败: {e}")
                import traceback
                traceback.print_exc()
                
        # 生成调试报告
        self._generate_debug_report()
        
    def _load_frame_data(self, frame_name: str) -> Optional[Dict]:
        """加载帧数据"""
        label_file = Path(self.source_dir) / "labels" / f"{frame_name}.json"
        
        if not label_file.exists():
            logger.warning(f"⚠️ 标注文件不存在: {label_file}")
            return None
            
        try:
            with open(label_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
                logger.info(f"📊 {frame_name} 原始数据加载成功: {len(data.get('shapes', []))} 个shapes")
                return data
        except Exception as e:
            logger.error(f"❌ 加载 {frame_name} 数据失败: {e}")
            return None
            
    def _convert_to_detections(self, original_data: Dict) -> List[Dict]:
        """转换为检测格式"""
        detections = []
        
        for shape in original_data.get("shapes", []):
            label = shape.get("label", "")
            group_id = shape.get("group_id", 0)
            points = shape.get("points", [])
            
            if len(points) == 4:
                # 计算bbox
                x_coords = [p[0] for p in points]
                y_coords = [p[1] for p in points]
                bbox = [min(x_coords), min(y_coords), max(x_coords), max(y_coords)]
                
                detection = {
                    "label": label,
                    "bbox": bbox,
                    "confidence": 1.0,
                    "group_id": group_id
                }
                detections.append(detection)
                
        return detections
        
    def _analyze_original_detections(self, frame_name: str, detections: List[Dict]):
        """分析原始检测数据"""
        logger.info(f"📊 {frame_name} 原始检测分析:")
        logger.info(f"  总检测数: {len(detections)}")
        
        # 按区域分组
        by_region = {}
        target_cards = []
        
        for detection in detections:
            group_id = detection.get("group_id", 0)
            label = detection.get("label", "")
            
            if group_id not in by_region:
                by_region[group_id] = []
            by_region[group_id].append(label)
            
            # 重点关注"叁"和"伍"
            if "叁" in label or "伍" in label or "三" in label or "五" in label:
                target_cards.append({
                    "label": label,
                    "group_id": group_id,
                    "bbox": detection.get("bbox", [])
                })
                
        # 输出区域分布
        for region_id, labels in by_region.items():
            logger.info(f"  区域{region_id}: {len(labels)}张 - {labels}")
            
        # 输出目标卡牌
        if target_cards:
            logger.info(f"🎯 {frame_name} 目标卡牌（叁/伍）:")
            for card in target_cards:
                logger.info(f"  区域{card['group_id']}: '{card['label']}'")
        else:
            logger.warning(f"⚠️ {frame_name} 未发现目标卡牌（叁/伍）")
            
    def _debug_process_frame(self, frame_name: str, detections: List[Dict]):
        """调试处理单帧"""
        logger.info(f"🔧 开始调试处理 {frame_name}...")
        
        # 在处理前记录状态
        self._log_pre_processing_state(frame_name)
        
        # 处理
        result = self.integrator.process_frame(detections)
        
        # 在处理后记录状态
        self._log_post_processing_state(frame_name, result)
        
        return result
        
    def _log_pre_processing_state(self, frame_name: str):
        """记录处理前状态"""
        logger.info(f"🔧 {frame_name} 处理前状态:")
        
        # 记录SimpleInheritor状态
        if hasattr(self.integrator, 'inheritor'):
            inheritor = self.integrator.inheritor
            if hasattr(inheritor, 'previous_frame_mapping'):
                mapping = inheritor.previous_frame_mapping
                logger.info(f"  previous_frame_mapping: {len(mapping)} 个映射")
                
                # 查找"叁"和"伍"相关的映射
                for key, cards in mapping.items():
                    group_id, label = key
                    if "叁" in label or "伍" in label or "三" in label or "五" in label:
                        logger.info(f"    目标映射: 区域{group_id} '{label}' -> {len(cards)}张卡牌")
                        for i, card in enumerate(cards):
                            twin_id = card.get('twin_id', 'None')
                            logger.info(f"      卡牌{i+1}: twin_id='{twin_id}'")
                            
    def _log_post_processing_state(self, frame_name: str, result):
        """记录处理后状态"""
        logger.info(f"🔧 {frame_name} 处理后状态:")
        
        if hasattr(result, 'digital_twin_cards'):
            cards = result.digital_twin_cards
            logger.info(f"  数字孪生卡牌: {len(cards)} 张")
            
            # 查找目标卡牌
            target_cards = []
            for card in cards:
                label = getattr(card, 'label', '') if hasattr(card, 'label') else card.get('label', '')
                twin_id = getattr(card, 'twin_id', '') if hasattr(card, 'twin_id') else card.get('twin_id', '')
                group_id = getattr(card, 'group_id', 0) if hasattr(card, 'group_id') else card.get('group_id', 0)
                
                if "叁" in label or "伍" in label or "三" in label or "五" in label:
                    target_cards.append({
                        "label": label,
                        "twin_id": twin_id,
                        "group_id": group_id
                    })
                    
            if target_cards:
                logger.info(f"  目标卡牌处理结果:")
                for card in target_cards:
                    logger.info(f"    区域{card['group_id']}: '{card['label']}' -> '{card['twin_id']}'")
            else:
                logger.warning(f"  ⚠️ 未找到目标卡牌处理结果")
                
        # 记录统计信息
        if hasattr(result, 'statistics'):
            stats = result.statistics
            logger.info(f"  处理统计: {stats}")
            
    def _analyze_processing_result(self, frame_name: str, result):
        """分析处理结果"""
        logger.info(f"📈 {frame_name} 处理结果分析:")
        
        if not result:
            logger.warning(f"⚠️ {frame_name} 处理结果为空")
            return
            
        # 检查是否成功
        success = getattr(result, 'success', False)
        logger.info(f"  处理成功: {success}")
        
        if not success:
            errors = getattr(result, 'validation_errors', [])
            logger.warning(f"  处理失败原因: {errors}")
            return
            
        # 分析数字孪生卡牌
        if hasattr(result, 'digital_twin_cards'):
            self._analyze_digital_twin_cards(frame_name, result.digital_twin_cards)
            
    def _analyze_digital_twin_cards(self, frame_name: str, cards):
        """分析数字孪生卡牌"""
        logger.info(f"🎯 {frame_name} 数字孪生卡牌详细分析:")
        
        # 特别关注区域7和区域9
        region_7_cards = []
        region_9_cards = []
        
        for card in cards:
            group_id = getattr(card, 'group_id', 0) if hasattr(card, 'group_id') else card.get('group_id', 0)
            label = getattr(card, 'label', '') if hasattr(card, 'label') else card.get('label', '')
            twin_id = getattr(card, 'twin_id', '') if hasattr(card, 'twin_id') else card.get('twin_id', '')
            
            if group_id == 7:
                region_7_cards.append({"label": label, "twin_id": twin_id})
            elif group_id == 9:
                region_9_cards.append({"label": label, "twin_id": twin_id})
                
        if region_7_cards:
            logger.info(f"  区域7卡牌: {len(region_7_cards)}张")
            for card in region_7_cards:
                logger.info(f"    '{card['label']}' -> '{card['twin_id']}'")
                
        if region_9_cards:
            logger.info(f"  区域9卡牌: {len(region_9_cards)}张")
            for card in region_9_cards:
                logger.info(f"    '{card['label']}' -> '{card['twin_id']}'")
                
                # 检查是否是错误的"2伍"
                if card['twin_id'] == "2伍" and frame_name == "frame_00257":
                    logger.error(f"❌ 发现错误！区域9出现了'2伍'，应该是'2叁'")
                    
        # 检查7→9流转
        if frame_name == "frame_00257":
            self._check_7_to_9_transition(region_7_cards, region_9_cards)
            
    def _check_7_to_9_transition(self, region_7_cards: List[Dict], region_9_cards: List[Dict]):
        """检查7→9流转"""
        logger.info("🔄 检查7→9流转:")
        
        # 查找前一帧（frame_00256）的区域7卡牌
        prev_frame_result = self.debug_results.get("frame_00256")
        if prev_frame_result and hasattr(prev_frame_result["processing_result"], 'digital_twin_cards'):
            prev_cards = prev_frame_result["processing_result"].digital_twin_cards
            prev_region_7_cards = []
            
            for card in prev_cards:
                group_id = getattr(card, 'group_id', 0) if hasattr(card, 'group_id') else card.get('group_id', 0)
                if group_id == 7:
                    label = getattr(card, 'label', '') if hasattr(card, 'label') else card.get('label', '')
                    twin_id = getattr(card, 'twin_id', '') if hasattr(card, 'twin_id') else card.get('twin_id', '')
                    prev_region_7_cards.append({"label": label, "twin_id": twin_id})
                    
            logger.info(f"  前一帧区域7: {prev_region_7_cards}")
            logger.info(f"  当前帧区域9: {region_9_cards}")
            
            # 检查是否正确继承
            for prev_card in prev_region_7_cards:
                if "叁" in prev_card["label"] or "三" in prev_card["label"]:
                    expected_twin_id = prev_card["twin_id"]
                    
                    # 查找当前帧区域9中是否有对应的卡牌
                    found_match = False
                    for curr_card in region_9_cards:
                        if curr_card["twin_id"] == expected_twin_id:
                            found_match = True
                            logger.info(f"✅ 正确继承: {expected_twin_id}")
                            break
                            
                    if not found_match:
                        logger.error(f"❌ 继承失败: 期望'{expected_twin_id}'，但在区域9中未找到")
                        
    def _generate_debug_report(self):
        """生成调试报告"""
        logger.info("📋 生成调试报告...")
        
        report = {
            "debug_time": datetime.now().isoformat(),
            "target_frames": self.target_frames,
            "debug_results": self.debug_results,
            "error_analysis": self._analyze_error_pattern(),
            "recommendations": self._generate_recommendations()
        }
        
        # 保存报告
        report_file = "debug_frame_00257_report.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2, default=str)
            
        logger.info(f"📋 调试报告已保存: {report_file}")
        
    def _analyze_error_pattern(self) -> Dict[str, Any]:
        """分析错误模式"""
        # 这里可以添加更详细的错误模式分析
        return {
            "error_type": "7→9区域流转中的标签错误",
            "expected": "2叁",
            "actual": "2伍",
            "suspected_cause": "继承机制或ID分配问题"
        }
        
    def _generate_recommendations(self) -> List[str]:
        """生成修复建议"""
        return [
            "检查SimpleInheritor的跨区域继承逻辑",
            "验证7→9流转的特殊处理",
            "检查ID分配器的标签映射",
            "增强帧间状态管理",
            "添加7→9流转的专门测试用例"
        ]

def main():
    """主函数"""
    print("🔧 Frame_00257.jpg 调试处理器")
    print("=" * 50)
    
    processor = DebugFrame00257Processor()
    
    try:
        processor.process_debug_frames()
        print("\n✅ 调试处理完成！请查看生成的调试日志和报告。")
        
    except Exception as e:
        logger.error(f"调试处理过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
