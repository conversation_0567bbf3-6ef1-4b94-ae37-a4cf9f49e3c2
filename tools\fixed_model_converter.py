#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复的YOLOv8模型格式转换器

解决ONNX导出置信度为0的问题，确保AnyLabeling兼容性
"""

import os
import sys
import cv2
import numpy as np
from pathlib import Path
from typing import Optional, Dict, Any
import logging

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class FixedModelConverter:
    """修复的模型转换器"""
    
    def __init__(self):
        self.test_image_path = None
        self.find_test_image()
        
    def find_test_image(self):
        """找到测试图像"""
        test_paths = [
            "legacy_assets/ceshi/calibration_gt/images",
            "legacy_assets/ceshi/tupian"
        ]
        
        for test_path in test_paths:
            path = Path(test_path)
            if path.exists():
                for img_file in path.glob("*.jpg"):
                    self.test_image_path = str(img_file)
                    logger.info(f"找到测试图像: {img_file.name}")
                    return
                    
        logger.warning("未找到测试图像，将跳过推理验证")
        
    def validate_pt_model(self, model_path: str) -> bool:
        """验证PT模型是否正常工作"""
        try:
            from ultralytics import YOLO
            
            logger.info(f"验证PT模型: {model_path}")
            model = YOLO(model_path)
            
            if not self.test_image_path:
                logger.warning("没有测试图像，跳过推理验证")
                return True
                
            # 测试推理
            results = model(self.test_image_path, conf=0.25, verbose=False)
            
            if results and results[0].boxes is not None:
                boxes = results[0].boxes
                detection_count = len(boxes)
                if detection_count > 0:
                    conf_range = f"{boxes.conf.min():.3f}-{boxes.conf.max():.3f}"
                    logger.info(f"PT模型正常: 检测到{detection_count}个目标，置信度{conf_range}")
                    return True
                else:
                    logger.warning("PT模型未检测到任何目标")
                    return True  # 可能图像确实没有目标
            else:
                logger.warning("PT模型推理结果为空")
                return False
                
        except Exception as e:
            logger.error(f"PT模型验证失败: {e}")
            return False
            
    def export_onnx_with_multiple_configs(self, model_path: str, output_dir: Optional[str] = None) -> Dict[str, str]:
        """使用多种配置导出ONNX模型"""
        try:
            from ultralytics import YOLO
            
            model_path = Path(model_path)
            if output_dir is None:
                output_dir = model_path.parent
            else:
                output_dir = Path(output_dir)
                output_dir.mkdir(parents=True, exist_ok=True)
                
            model = YOLO(model_path)
            
            # 多种导出配置
            export_configs = {
                'standard': {
                    'format': 'onnx',
                    'imgsz': 640,  # 修复：使用正方形尺寸
                    'dynamic': False,
                    'simplify': True,
                    'opset': 12,
                    'half': False,
                    'optimize': False,  # 关键：关闭优化
                    'batch': 1,
                },
                'optimized': {
                    'format': 'onnx',
                    'imgsz': 640,
                    'dynamic': False,
                    'simplify': True,
                    'opset': 12,
                    'half': False,
                    'optimize': True,  # 开启优化
                    'batch': 1,
                },
                'compatible': {
                    'format': 'onnx',
                    'imgsz': 640,
                    'dynamic': False,
                    'simplify': False,  # 不简化
                    'opset': 11,  # 使用更兼容的版本
                    'half': False,
                    'optimize': False,
                    'batch': 1,
                },
                'anylabeling': {
                    'format': 'onnx',
                    'imgsz': 640,
                    'dynamic': False,
                    'simplify': True,
                    'opset': 12,
                    'half': False,
                    'optimize': False,
                    'batch': 1,
                    # 可能需要的额外参数
                    'workspace': 4,  # GB
                }
            }
            
            successful_exports = {}
            
            for config_name, config in export_configs.items():
                logger.info(f"尝试导出配置: {config_name}")
                
                try:
                    # 执行导出
                    export_result = model.export(**config)
                    
                    # 查找生成的ONNX文件
                    temp_onnx = model_path.with_suffix('.onnx')
                    target_onnx = output_dir / f"{model_path.stem}_{config_name}.onnx"
                    
                    if temp_onnx.exists():
                        # 移动到目标位置
                        if temp_onnx != target_onnx:
                            if target_onnx.exists():
                                target_onnx.unlink()
                            temp_onnx.rename(target_onnx)
                            
                        # 验证导出的模型
                        if self.validate_onnx_model(str(target_onnx)):
                            successful_exports[config_name] = str(target_onnx)
                            logger.info(f"✅ {config_name} 配置导出成功: {target_onnx}")
                        else:
                            logger.warning(f"❌ {config_name} 配置导出验证失败")
                            if target_onnx.exists():
                                target_onnx.unlink()
                    else:
                        logger.warning(f"❌ {config_name} 配置导出失败：未找到ONNX文件")
                        
                except Exception as e:
                    logger.error(f"❌ {config_name} 配置导出异常: {e}")
                    
            return successful_exports
            
        except Exception as e:
            logger.error(f"导出过程失败: {e}")
            return {}
            
    def validate_onnx_model(self, onnx_path: str) -> bool:
        """全面验证ONNX模型"""
        try:
            import onnxruntime as ort
            
            logger.info(f"验证ONNX模型: {Path(onnx_path).name}")
            
            # 1. 基础加载测试
            session = ort.InferenceSession(onnx_path)
            input_info = session.get_inputs()[0]
            output_info = session.get_outputs()[0]
            
            logger.info(f"  输入形状: {input_info.shape}")
            logger.info(f"  输出形状: {output_info.shape}")
            
            # 2. 推理测试
            if not self.test_image_path:
                logger.warning("  没有测试图像，跳过推理验证")
                return True
                
            # 预处理测试图像
            image = cv2.imread(self.test_image_path)
            if image is None:
                logger.warning("  无法读取测试图像")
                return True
                
            # 预处理
            resized = cv2.resize(image, (640, 640))
            input_tensor = resized.transpose(2, 0, 1).astype(np.float32) / 255.0
            input_tensor = np.expand_dims(input_tensor, axis=0)
            
            # ONNX推理
            outputs = session.run(None, {input_info.name: input_tensor})
            output = outputs[0]
            
            # 分析输出
            logger.info(f"  输出数值范围: [{output.min():.8f}, {output.max():.8f}]")
            
            # 提取置信度（假设是YOLO格式）
            if len(output.shape) == 3:
                # 转换为 [detections, attributes]
                if output.shape[1] > output.shape[2]:
                    # [batch, attributes, detections] -> [detections, attributes]
                    data = output[0].transpose()
                else:
                    # [batch, detections, attributes]
                    data = output[0]
                    
                if data.shape[1] >= 5:  # 至少有bbox + confidence
                    confidences = data[:, 4]
                    max_conf = confidences.max()
                    valid_detections = (confidences > 0.1).sum()
                    
                    logger.info(f"  最大置信度: {max_conf:.8f}")
                    logger.info(f"  有效检测数 (>0.1): {valid_detections}")
                    
                    # 关键验证：置信度是否正常
                    if max_conf > 0.01:  # 至少要有合理的置信度
                        logger.info("  ✅ 置信度正常")
                        return True
                    else:
                        logger.warning("  ❌ 置信度异常低，可能导出有问题")
                        return False
                else:
                    logger.warning("  ❌ 输出格式不符合YOLO预期")
                    return False
            else:
                logger.warning("  ❌ 输出维度不符合预期")
                return False
                
        except ImportError:
            logger.error("  需要安装 onnxruntime: pip install onnxruntime")
            return False
        except Exception as e:
            logger.error(f"  ONNX验证失败: {e}")
            return False
            
    def compare_pt_onnx_outputs(self, pt_path: str, onnx_path: str) -> bool:
        """对比PT和ONNX的输出一致性"""
        try:
            from ultralytics import YOLO
            import onnxruntime as ort
            
            if not self.test_image_path:
                logger.warning("没有测试图像，跳过对比验证")
                return True
                
            logger.info("对比PT和ONNX输出一致性...")
            
            # PT推理
            pt_model = YOLO(pt_path)
            pt_results = pt_model(self.test_image_path, conf=0.25, verbose=False)
            
            pt_detections = 0
            pt_conf_range = "N/A"
            if pt_results and pt_results[0].boxes is not None:
                boxes = pt_results[0].boxes
                pt_detections = len(boxes)
                if pt_detections > 0:
                    pt_conf_range = f"{boxes.conf.min():.3f}-{boxes.conf.max():.3f}"
                    
            # ONNX推理
            session = ort.InferenceSession(onnx_path)
            input_info = session.get_inputs()[0]
            
            image = cv2.imread(self.test_image_path)
            resized = cv2.resize(image, (640, 640))
            input_tensor = resized.transpose(2, 0, 1).astype(np.float32) / 255.0
            input_tensor = np.expand_dims(input_tensor, axis=0)
            
            outputs = session.run(None, {input_info.name: input_tensor})
            output = outputs[0]
            
            # 简单分析ONNX输出
            onnx_max_conf = 0
            onnx_valid_detections = 0
            
            if len(output.shape) == 3:
                if output.shape[1] > output.shape[2]:
                    data = output[0].transpose()
                else:
                    data = output[0]
                    
                if data.shape[1] >= 5:
                    confidences = data[:, 4]
                    onnx_max_conf = confidences.max()
                    onnx_valid_detections = (confidences > 0.1).sum()
                    
            logger.info(f"对比结果:")
            logger.info(f"  PT模型: {pt_detections}个检测, 置信度{pt_conf_range}")
            logger.info(f"  ONNX模型: {onnx_valid_detections}个有效检测, 最大置信度{onnx_max_conf:.6f}")
            
            # 判断一致性
            if onnx_max_conf > 0.01 and (onnx_valid_detections > 0 or pt_detections == 0):
                logger.info("  ✅ PT和ONNX输出基本一致")
                return True
            else:
                logger.warning("  ❌ PT和ONNX输出差异较大")
                return False
                
        except Exception as e:
            logger.error(f"对比验证失败: {e}")
            return False
            
    def convert_model(self, model_path: str, output_dir: Optional[str] = None) -> Dict[str, Any]:
        """转换模型的主函数"""
        logger.info("🚀 开始修复的模型转换")
        logger.info("=" * 50)
        
        # 1. 验证输入模型
        if not self.validate_pt_model(model_path):
            return {'success': False, 'error': 'PT模型验证失败'}
            
        # 2. 导出多种配置的ONNX
        successful_exports = self.export_onnx_with_multiple_configs(model_path, output_dir)
        
        if not successful_exports:
            return {'success': False, 'error': '所有ONNX导出配置都失败'}
            
        # 3. 对比验证
        best_onnx = None
        best_score = 0
        
        for config_name, onnx_path in successful_exports.items():
            logger.info(f"验证配置: {config_name}")
            
            if self.compare_pt_onnx_outputs(model_path, onnx_path):
                # 简单评分：standard > compatible > optimized > anylabeling
                score = {'standard': 4, 'compatible': 3, 'optimized': 2, 'anylabeling': 1}.get(config_name, 0)
                if score > best_score:
                    best_score = score
                    best_onnx = onnx_path
                    
        if best_onnx:
            logger.info(f"✅ 推荐使用: {Path(best_onnx).name}")
            
            # 复制最佳版本为默认名称
            default_onnx = Path(output_dir or Path(model_path).parent) / f"{Path(model_path).stem}.onnx"
            if default_onnx != Path(best_onnx):
                import shutil
                shutil.copy2(best_onnx, default_onnx)
                logger.info(f"已复制为默认名称: {default_onnx.name}")
                
            return {
                'success': True,
                'best_onnx': best_onnx,
                'default_onnx': str(default_onnx),
                'all_exports': successful_exports
            }
        else:
            return {
                'success': False,
                'error': '所有导出的ONNX都未通过验证',
                'all_exports': successful_exports
            }

def main():
    """主函数"""
    print("🔧 修复的模型格式转换器")
    print("=" * 50)
    
    # 转换当前模型
    converter = FixedModelConverter()
    
    model_paths = [
        "best.pt",
        "data/processed/train3.0/weights/best.pt"
    ]
    
    for model_path in model_paths:
        if os.path.exists(model_path):
            print(f"\n转换模型: {model_path}")
            result = converter.convert_model(model_path)
            
            if result['success']:
                print(f"✅ 转换成功!")
                print(f"   最佳ONNX: {result['best_onnx']}")
                print(f"   默认ONNX: {result['default_onnx']}")
            else:
                print(f"❌ 转换失败: {result['error']}")
        else:
            print(f"⚠️ 模型文件不存在: {model_path}")
            
    print(f"\n✅ 模型转换完成")

if __name__ == "__main__":
    main()
