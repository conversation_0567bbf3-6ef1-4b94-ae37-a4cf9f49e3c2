#!/usr/bin/env python3
"""
基于数据质量感知的ID分配修复方案

基于人工标注逻辑分析发现：
1. 素材质量问题：多个不关联单局混合
2. 标注一致性极低：大部分区域<20%一致性
3. 唯一高质量区域：区域9（95.1%一致性）

修复策略：
1. 基于高质量区域建立标准模式
2. 实现数据质量感知的ID分配
3. 针对低质量数据的容错处理
"""

import sys
import os
import json
from pathlib import Path
from typing import List, Dict, Any
import logging

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DataQualityAwareIDAssigner:
    """数据质量感知的ID分配器"""
    
    def __init__(self):
        # 基于人工标注分析的区域质量评估
        self.region_quality = {
            1: 0.062,   # 6.2%一致性 - 极低质量
            5: 0.655,   # 65.5%一致性 - 中等质量
            6: 0.181,   # 18.1%一致性 - 低质量
            9: 0.951,   # 95.1%一致性 - 高质量（标准）
            16: 0.062,  # 6.2%一致性 - 极低质量
        }
        
        # 基于高质量区域9的标准模式
        self.standard_pattern = {
            'direction': 'right',  # 从左到右
            'consistency': 0.951,
            'id_assignment': 'spatial_order'  # 空间顺序分配
        }
        
        # 卡牌类型质量评估
        self.card_quality = {
            '六': 0.0,    # 0%连续性
            '四': 0.0,    # 0%连续性
            '一': 0.029,  # 2.9%连续性
            '十': 0.083,  # 8.3%连续性
            '七': 0.152,  # 15.2%连续性
            '三': 0.338,  # 33.8%连续性
            '二': 0.363,  # 36.3%连续性
            '五': 0.429,  # 42.9%连续性
            '九': 0.462,  # 46.2%连续性
            '八': 0.538,  # 53.8%连续性
        }
        
    def assess_data_quality(self, cards: List[Any], region_id: int) -> Dict[str, float]:
        """评估数据质量"""
        region_quality = self.region_quality.get(region_id, 0.1)  # 默认低质量
        
        # 评估卡牌类型质量
        card_qualities = []
        for card in cards:
            card_quality = self.card_quality.get(card.label, 0.3)  # 默认中等质量
            card_qualities.append(card_quality)
            
        avg_card_quality = sum(card_qualities) / len(card_qualities) if card_qualities else 0.1
        
        # 综合质量评估
        overall_quality = (region_quality + avg_card_quality) / 2
        
        return {
            'region_quality': region_quality,
            'card_quality': avg_card_quality,
            'overall_quality': overall_quality,
            'quality_level': self._get_quality_level(overall_quality)
        }
        
    def _get_quality_level(self, quality: float) -> str:
        """获取质量等级"""
        if quality >= 0.7:
            return 'high'
        elif quality >= 0.3:
            return 'medium'
        else:
            return 'low'
            
    def assign_ids_with_quality_awareness(self, cards: List[Any], region_id: int) -> List[Any]:
        """基于质量感知的ID分配"""
        if not cards:
            return cards
            
        # 评估数据质量
        quality_assessment = self.assess_data_quality(cards, region_id)
        quality_level = quality_assessment['quality_level']
        
        logger.info(f"区域{region_id}质量评估: {quality_assessment['overall_quality']:.3f} ({quality_level})")
        
        # 根据质量等级选择分配策略
        if quality_level == 'high':
            return self._high_quality_assignment(cards, region_id)
        elif quality_level == 'medium':
            return self._medium_quality_assignment(cards, region_id)
        else:
            return self._low_quality_assignment(cards, region_id)
            
    def _high_quality_assignment(self, cards: List[Any], region_id: int) -> List[Any]:
        """高质量数据的ID分配（基于区域9的标准模式）"""
        logger.info(f"区域{region_id}使用高质量分配策略")
        
        # 按卡牌类型分组
        cards_by_type = {}
        for card in cards:
            if card.label not in cards_by_type:
                cards_by_type[card.label] = []
            cards_by_type[card.label].append(card)
            
        # 为每种卡牌类型分配ID
        for card_type, type_cards in cards_by_type.items():
            # 严格按照从左到右的空间顺序排序
            sorted_cards = sorted(type_cards, key=lambda c: c.bbox[0])  # 按X坐标排序
            
            # 按顺序分配ID
            for i, card in enumerate(sorted_cards, 1):
                if i <= 4:  # 最多4个物理ID
                    card.twin_id = f"{i}_{card_type}"
                else:
                    card.twin_id = f"虚拟_{card_type}"
                    
        return cards
        
    def _medium_quality_assignment(self, cards: List[Any], region_id: int) -> List[Any]:
        """中等质量数据的ID分配（容错处理）"""
        logger.info(f"区域{region_id}使用中等质量分配策略")
        
        # 使用更宽松的排序规则
        cards_by_type = {}
        for card in cards:
            if card.label not in cards_by_type:
                cards_by_type[card.label] = []
            cards_by_type[card.label].append(card)
            
        for card_type, type_cards in cards_by_type.items():
            # 根据区域特点选择排序方式
            if region_id == 5:  # 弃牌区，从右到左
                sorted_cards = sorted(type_cards, key=lambda c: -c.bbox[0])
            else:  # 默认从左到右
                sorted_cards = sorted(type_cards, key=lambda c: c.bbox[0])
                
            # 分配ID，允许一定的容错
            for i, card in enumerate(sorted_cards, 1):
                if i <= 4:
                    card.twin_id = f"{i}_{card_type}"
                else:
                    card.twin_id = f"虚拟_{card_type}"
                    
        return cards
        
    def _low_quality_assignment(self, cards: List[Any], region_id: int) -> List[Any]:
        """低质量数据的ID分配（最大容错）"""
        logger.info(f"区域{region_id}使用低质量分配策略（最大容错）")
        
        # 对于低质量数据，使用最简单的分配策略
        cards_by_type = {}
        for card in cards:
            if card.label not in cards_by_type:
                cards_by_type[card.label] = []
            cards_by_type[card.label].append(card)
            
        for card_type, type_cards in cards_by_type.items():
            # 简单按检测顺序分配，不强制空间排序
            for i, card in enumerate(type_cards, 1):
                if i <= 4:
                    card.twin_id = f"{i}_{card_type}"
                else:
                    card.twin_id = f"虚拟_{card_type}"
                    
        return cards
        
    def generate_quality_report(self, all_cards: List[Any]) -> Dict:
        """生成质量评估报告"""
        report = {
            'total_cards': len(all_cards),
            'region_distribution': {},
            'quality_distribution': {'high': 0, 'medium': 0, 'low': 0},
            'recommendations': []
        }
        
        # 按区域统计
        cards_by_region = {}
        for card in all_cards:
            region_id = getattr(card, 'group_id', 0)
            if region_id not in cards_by_region:
                cards_by_region[region_id] = []
            cards_by_region[region_id].append(card)
            
        # 评估每个区域
        for region_id, region_cards in cards_by_region.items():
            quality_assessment = self.assess_data_quality(region_cards, region_id)
            
            report['region_distribution'][region_id] = {
                'card_count': len(region_cards),
                'quality_score': quality_assessment['overall_quality'],
                'quality_level': quality_assessment['quality_level']
            }
            
            report['quality_distribution'][quality_assessment['quality_level']] += len(region_cards)
            
        # 生成建议
        low_quality_regions = [
            region_id for region_id, info in report['region_distribution'].items()
            if info['quality_level'] == 'low' and info['card_count'] > 50
        ]
        
        if low_quality_regions:
            report['recommendations'].append(
                f"建议重新标注或过滤区域{low_quality_regions}的数据，这些区域质量较低但样本量大"
            )
            
        high_quality_regions = [
            region_id for region_id, info in report['region_distribution'].items()
            if info['quality_level'] == 'high'
        ]
        
        if high_quality_regions:
            report['recommendations'].append(
                f"区域{high_quality_regions}质量较高，可作为标准模式参考"
            )
            
        return report

def create_enhanced_id_assigner():
    """创建增强的ID分配器"""
    print("🔧 创建数据质量感知的ID分配器")
    
    # 生成修复代码
    fix_code = '''
# 在DigitalTwinCoordinator中集成质量感知分配器
class DigitalTwinCoordinator:
    def __init__(self):
        # ... 现有初始化代码 ...
        self.quality_aware_assigner = DataQualityAwareIDAssigner()
        
    def process_frame(self, detections):
        # ... 现有处理逻辑 ...
        
        # 使用质量感知的ID分配
        enhanced_cards = self.quality_aware_assigner.assign_ids_with_quality_awareness(
            digital_twin_cards, region_id
        )
        
        return {
            'digital_twin_cards': enhanced_cards,
            'quality_report': self.quality_aware_assigner.generate_quality_report(enhanced_cards)
        }
'''
    
    print("📋 修复要点:")
    print("   1. 基于区域9（95.1%一致性）建立标准模式")
    print("   2. 根据数据质量选择不同的分配策略")
    print("   3. 对低质量数据实施最大容错处理")
    print("   4. 生成质量评估报告指导后续优化")
    
    return fix_code

def main():
    """主函数"""
    print("🎯 基于数据质量感知的ID分配修复")
    print("=" * 50)
    
    # 创建质量感知分配器
    assigner = DataQualityAwareIDAssigner()
    
    # 展示质量评估
    print("📊 数据质量评估:")
    print("   高质量区域:")
    for region_id, quality in assigner.region_quality.items():
        if quality > 0.7:
            print(f"     - 区域{region_id}: {quality:.1%}")
            
    print("   中等质量区域:")
    for region_id, quality in assigner.region_quality.items():
        if 0.3 <= quality <= 0.7:
            print(f"     - 区域{region_id}: {quality:.1%}")
            
    print("   低质量区域:")
    for region_id, quality in assigner.region_quality.items():
        if quality < 0.3:
            print(f"     - 区域{region_id}: {quality:.1%}")
            
    # 创建修复方案
    fix_code = create_enhanced_id_assigner()
    
    print(f"\n💡 核心策略:")
    print("   1. 质量感知：根据区域和卡牌质量选择策略")
    print("   2. 标准模式：基于区域9的高质量模式")
    print("   3. 容错处理：对低质量数据最大化容错")
    print("   4. 持续改进：生成质量报告指导优化")
    
    print(f"\n✅ 修复方案已生成")

if __name__ == "__main__":
    main()
