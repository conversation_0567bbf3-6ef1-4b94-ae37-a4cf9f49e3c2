#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
数据验证和清洗层
在检测层和状态转换层之间添加数据质量控制
"""

import numpy as np
import cv2
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass
from enum import Enum
import json
import time
from collections import defaultdict, deque

class ValidationLevel(Enum):
    """验证级别枚举"""
    STRICT = "strict"      # 严格验证
    NORMAL = "normal"      # 正常验证
    LOOSE = "loose"        # 宽松验证

class ValidationResult(Enum):
    """验证结果枚举"""
    PASS = "pass"          # 通过
    WARNING = "warning"    # 警告
    REJECT = "reject"      # 拒绝

@dataclass
class DetectionQuality:
    """检测质量评估结果"""
    confidence_score: float
    position_score: float
    size_score: float
    consistency_score: float
    overall_score: float
    issues: List[str]

class DetectionValidator:
    """
    检测结果验证器
    验证YOLO检测结果的合理性
    """
    
    def __init__(self, config: Dict = None):
        """
        初始化验证器
        
        Args:
            config: 验证配置
        """
        self.config = config or {}
        
        # 验证阈值
        self.thresholds = {
            'min_confidence': self.config.get('min_confidence', 0.3),
            'max_confidence': self.config.get('max_confidence', 1.0),
            'min_bbox_area': self.config.get('min_bbox_area', 100),
            'max_bbox_area': self.config.get('max_bbox_area', 50000),
            'min_aspect_ratio': self.config.get('min_aspect_ratio', 0.3),
            'max_aspect_ratio': self.config.get('max_aspect_ratio', 3.0),
        }
        
        # 卡牌标签验证
        self.valid_labels = {
            '一', '二', '三', '四', '五', '六', '七', '八', '九', '十',
            '壹', '贰', '叁', '肆', '伍', '陆', '柒', '捌', '玖', '拾'
        }
        
        # 区域验证
        self.valid_regions = {1, 2, 3, 4, 5, 6, 7, 8, 9, 16}
        
    def validate_detection(self, detection: Dict) -> Tuple[ValidationResult, DetectionQuality]:
        """
        验证单个检测结果
        
        Args:
            detection: 检测结果字典
            
        Returns:
            验证结果和质量评估
        """
        issues = []
        scores = {}
        
        # 1. 置信度验证
        confidence = detection.get('conf', 0.0)
        scores['confidence'] = self._validate_confidence(confidence, issues)
        
        # 2. 边界框验证
        bbox = detection.get('pos', [0, 0, 0, 0])
        scores['position'] = self._validate_bbox(bbox, issues)
        
        # 3. 尺寸验证
        scores['size'] = self._validate_size(bbox, issues)
        
        # 4. 标签验证
        card_id = detection.get('id', '')
        self._validate_label(card_id, issues)
        
        # 5. 区域验证
        group_id = detection.get('group_id', 0)
        self._validate_region(group_id, issues)
        
        # 计算综合质量分数
        overall_score = np.mean(list(scores.values()))
        
        # 创建质量评估结果
        quality = DetectionQuality(
            confidence_score=scores['confidence'],
            position_score=scores['position'],
            size_score=scores['size'],
            consistency_score=1.0,  # 暂时设为1.0，后续实现时间一致性验证
            overall_score=overall_score,
            issues=issues
        )
        
        # 确定验证结果
        if len(issues) == 0:
            result = ValidationResult.PASS
        elif overall_score > 0.7:
            result = ValidationResult.WARNING
        else:
            result = ValidationResult.REJECT
            
        return result, quality
    
    def _validate_confidence(self, confidence: float, issues: List[str]) -> float:
        """验证置信度"""
        if confidence < self.thresholds['min_confidence']:
            issues.append(f"置信度过低: {confidence:.3f}")
            return 0.0
        elif confidence > self.thresholds['max_confidence']:
            issues.append(f"置信度异常: {confidence:.3f}")
            return 0.5
        else:
            # 置信度越高分数越高
            return min(1.0, confidence / 0.9)
    
    def _validate_bbox(self, bbox: List[float], issues: List[str]) -> float:
        """验证边界框"""
        if len(bbox) != 4:
            issues.append("边界框格式错误")
            return 0.0
        
        x, y, w, h = bbox
        
        # 检查坐标合理性
        if x < 0 or y < 0 or w <= 0 or h <= 0:
            issues.append("边界框坐标异常")
            return 0.0
        
        # 检查宽高比
        aspect_ratio = w / h if h > 0 else 0
        if aspect_ratio < self.thresholds['min_aspect_ratio'] or aspect_ratio > self.thresholds['max_aspect_ratio']:
            issues.append(f"宽高比异常: {aspect_ratio:.2f}")
            return 0.3
        
        return 1.0
    
    def _validate_size(self, bbox: List[float], issues: List[str]) -> float:
        """验证尺寸"""
        if len(bbox) != 4:
            return 0.0
        
        x, y, w, h = bbox
        area = w * h
        
        if area < self.thresholds['min_bbox_area']:
            issues.append(f"检测框过小: {area:.0f}")
            return 0.2
        elif area > self.thresholds['max_bbox_area']:
            issues.append(f"检测框过大: {area:.0f}")
            return 0.2
        else:
            # 面积在合理范围内，分数为1.0
            return 1.0
    
    def _validate_label(self, card_id: str, issues: List[str]):
        """验证卡牌标签"""
        if not card_id:
            issues.append("卡牌ID为空")
            return
        
        # 解析卡牌标签
        if '_' in card_id:
            parts = card_id.split('_')
            if len(parts) >= 2:
                label = parts[1].replace('暗', '')  # 移除暗牌标记
                if label not in self.valid_labels:
                    issues.append(f"无效卡牌标签: {label}")
            else:
                issues.append("卡牌ID格式错误")
        else:
            issues.append("卡牌ID格式错误")
    
    def _validate_region(self, group_id: int, issues: List[str]):
        """验证区域ID"""
        if group_id not in self.valid_regions:
            issues.append(f"无效区域ID: {group_id}")


class DataCleaner:
    """
    数据清洗器
    去除重复、过滤低质量检测、修正异常数据
    """
    
    def __init__(self, config: Dict = None):
        """
        初始化清洗器
        
        Args:
            config: 清洗配置
        """
        self.config = config or {}
        
        # 清洗参数
        self.duplicate_threshold = self.config.get('duplicate_threshold', 0.8)
        self.min_quality_score = self.config.get('min_quality_score', 0.3)
        
    def clean_detections(self, detections: List[Dict], qualities: List[DetectionQuality]) -> Tuple[List[Dict], List[str]]:
        """
        清洗检测结果
        
        Args:
            detections: 检测结果列表
            qualities: 质量评估列表
            
        Returns:
            清洗后的检测结果和清洗日志
        """
        if len(detections) != len(qualities):
            raise ValueError("检测结果和质量评估数量不匹配")
        
        cleaned_detections = []
        cleaning_log = []
        
        # 1. 过滤低质量检测
        filtered_pairs = []
        for detection, quality in zip(detections, qualities):
            if quality.overall_score >= self.min_quality_score:
                filtered_pairs.append((detection, quality))
            else:
                cleaning_log.append(f"移除低质量检测: {detection.get('id', 'unknown')} (分数: {quality.overall_score:.3f})")
        
        # 2. 去除重复检测
        deduplicated_pairs = self._remove_duplicates(filtered_pairs, cleaning_log)
        
        # 3. 修正异常数据
        corrected_pairs = self._correct_anomalies(deduplicated_pairs, cleaning_log)
        
        # 提取清洗后的检测结果
        cleaned_detections = [pair[0] for pair in corrected_pairs]
        
        return cleaned_detections, cleaning_log
    
    def _remove_duplicates(self, detection_pairs: List[Tuple], cleaning_log: List[str]) -> List[Tuple]:
        """去除重复检测"""
        if len(detection_pairs) <= 1:
            return detection_pairs
        
        unique_pairs = []
        
        for i, (detection, quality) in enumerate(detection_pairs):
            is_duplicate = False
            
            for j, (existing_detection, existing_quality) in enumerate(unique_pairs):
                if self._is_duplicate(detection, existing_detection):
                    is_duplicate = True
                    # 保留质量更高的检测
                    if quality.overall_score > existing_quality.overall_score:
                        unique_pairs[j] = (detection, quality)
                        cleaning_log.append(f"替换重复检测: {existing_detection.get('id', 'unknown')} -> {detection.get('id', 'unknown')}")
                    else:
                        cleaning_log.append(f"移除重复检测: {detection.get('id', 'unknown')}")
                    break
            
            if not is_duplicate:
                unique_pairs.append((detection, quality))
        
        return unique_pairs
    
    def _is_duplicate(self, detection1: Dict, detection2: Dict) -> bool:
        """判断两个检测是否重复"""
        # 检查位置重叠
        bbox1 = detection1.get('pos', [0, 0, 0, 0])
        bbox2 = detection2.get('pos', [0, 0, 0, 0])
        
        iou = self._calculate_iou(bbox1, bbox2)
        
        # 检查标签相似性
        id1 = detection1.get('id', '')
        id2 = detection2.get('id', '')
        
        label_similar = self._are_labels_similar(id1, id2)
        
        return iou > self.duplicate_threshold and label_similar
    
    def _calculate_iou(self, bbox1: List[float], bbox2: List[float]) -> float:
        """计算两个边界框的IoU"""
        if len(bbox1) != 4 or len(bbox2) != 4:
            return 0.0
        
        x1, y1, w1, h1 = bbox1
        x2, y2, w2, h2 = bbox2
        
        # 计算交集
        x_left = max(x1, x2)
        y_top = max(y1, y2)
        x_right = min(x1 + w1, x2 + w2)
        y_bottom = min(y1 + h1, y2 + h2)
        
        if x_right <= x_left or y_bottom <= y_top:
            return 0.0
        
        intersection = (x_right - x_left) * (y_bottom - y_top)
        
        # 计算并集
        area1 = w1 * h1
        area2 = w2 * h2
        union = area1 + area2 - intersection
        
        return intersection / union if union > 0 else 0.0
    
    def _are_labels_similar(self, id1: str, id2: str) -> bool:
        """判断两个标签是否相似"""
        # 提取卡牌标签
        label1 = self._extract_label(id1)
        label2 = self._extract_label(id2)
        
        return label1 == label2
    
    def _extract_label(self, card_id: str) -> str:
        """提取卡牌标签"""
        if '_' in card_id:
            parts = card_id.split('_')
            if len(parts) >= 2:
                return parts[1].replace('暗', '')
        return card_id
    
    def _correct_anomalies(self, detection_pairs: List[Tuple], cleaning_log: List[str]) -> List[Tuple]:
        """修正异常数据"""
        corrected_pairs = []
        
        for detection, quality in detection_pairs:
            corrected_detection = detection.copy()
            
            # 修正置信度异常
            conf = corrected_detection.get('conf', 0.0)
            if conf > 1.0:
                corrected_detection['conf'] = 1.0
                cleaning_log.append(f"修正置信度异常: {detection.get('id', 'unknown')} {conf:.3f} -> 1.0")
            elif conf < 0.0:
                corrected_detection['conf'] = 0.0
                cleaning_log.append(f"修正置信度异常: {detection.get('id', 'unknown')} {conf:.3f} -> 0.0")
            
            # 修正边界框异常
            bbox = corrected_detection.get('pos', [0, 0, 0, 0])
            if len(bbox) == 4:
                x, y, w, h = bbox
                if x < 0 or y < 0 or w <= 0 or h <= 0:
                    # 简单修正：设置为最小有效值
                    corrected_bbox = [max(0, x), max(0, y), max(1, w), max(1, h)]
                    corrected_detection['pos'] = corrected_bbox
                    cleaning_log.append(f"修正边界框异常: {detection.get('id', 'unknown')}")
            
            corrected_pairs.append((corrected_detection, quality))
        
        return corrected_pairs


class TemporalValidator:
    """
    时间一致性验证器
    验证连续帧之间的数据一致性
    """

    def __init__(self, config: Dict = None):
        """
        初始化时间验证器

        Args:
            config: 验证配置
        """
        self.config = config or {}

        # 历史数据缓存
        self.history_size = self.config.get('history_size', 5)
        self.detection_history = deque(maxlen=self.history_size)

        # 一致性阈值
        self.position_threshold = self.config.get('position_threshold', 50.0)  # 像素
        self.confidence_threshold = self.config.get('confidence_threshold', 0.3)
        self.max_new_detections = self.config.get('max_new_detections', 5)

    def validate_temporal_consistency(self, current_detections: List[Dict]) -> Tuple[List[Dict], List[str]]:
        """
        验证时间一致性

        Args:
            current_detections: 当前帧的检测结果

        Returns:
            验证后的检测结果和验证日志
        """
        validation_log = []
        validated_detections = current_detections.copy()

        if len(self.detection_history) == 0:
            # 第一帧，直接添加到历史
            self.detection_history.append(current_detections)
            validation_log.append("首帧数据，跳过时间一致性验证")
            return validated_detections, validation_log

        # 获取上一帧数据
        previous_detections = self.detection_history[-1]

        # 1. 检查突然消失的检测
        validated_detections = self._check_missing_detections(
            validated_detections, previous_detections, validation_log
        )

        # 2. 检查突然出现的检测
        validated_detections = self._check_new_detections(
            validated_detections, previous_detections, validation_log
        )

        # 3. 检查位置跳跃
        validated_detections = self._check_position_jumps(
            validated_detections, previous_detections, validation_log
        )

        # 4. 检查置信度异常变化
        validated_detections = self._check_confidence_changes(
            validated_detections, previous_detections, validation_log
        )

        # 更新历史数据
        self.detection_history.append(validated_detections)

        return validated_detections, validation_log

    def _check_missing_detections(self, current: List[Dict], previous: List[Dict], log: List[str]) -> List[Dict]:
        """检查突然消失的检测"""
        # 找到在上一帧存在但当前帧消失的检测
        current_ids = {det.get('id', '') for det in current}

        # 限制恢复数量，避免过度恢复
        max_recoveries = 5
        recovery_count = 0

        for prev_det in previous:
            if recovery_count >= max_recoveries:
                break

            prev_id = prev_det.get('id', '')
            if prev_id and prev_id not in current_ids:
                # 检查是否是高置信度的检测突然消失
                prev_conf = prev_det.get('conf', 0.0)
                # 提高恢复阈值，减少不必要的恢复
                if prev_conf > max(self.confidence_threshold * 2, 0.8):
                    # 可能需要恢复这个检测（大幅降低置信度）
                    recovered_det = prev_det.copy()
                    recovered_det['conf'] = prev_conf * 0.5  # 大幅降低置信度
                    current.append(recovered_det)
                    log.append(f"恢复消失的高置信度检测: {prev_id} (置信度: {prev_conf:.3f} -> {recovered_det['conf']:.3f})")
                    recovery_count += 1

        return current

    def _check_new_detections(self, current: List[Dict], previous: List[Dict], log: List[str]) -> List[Dict]:
        """检查突然出现的检测"""
        previous_ids = {det.get('id', '') for det in previous}
        new_detections = []

        for curr_det in current:
            curr_id = curr_det.get('id', '')
            if curr_id and curr_id not in previous_ids:
                new_detections.append(curr_det)

        # 放宽新检测的限制，避免过度过滤
        adjusted_max_new = max(self.max_new_detections, len(previous) // 2)  # 至少允许上一帧一半数量的新检测

        # 如果新检测过多，可能是误检
        if len(new_detections) > adjusted_max_new:
            # 按置信度排序，只保留置信度最高的几个
            new_detections.sort(key=lambda x: x.get('conf', 0.0), reverse=True)
            kept_detections = new_detections[:adjusted_max_new]
            removed_count = len(new_detections) - adjusted_max_new

            # 从当前检测中移除低置信度的新检测
            current = [det for det in current if det not in new_detections[adjusted_max_new:]]
            log.append(f"移除{removed_count}个可能的误检新检测 (阈值: {adjusted_max_new})")

        return current

    def _check_position_jumps(self, current: List[Dict], previous: List[Dict], log: List[str]) -> List[Dict]:
        """检查位置跳跃"""
        # 建立ID到检测的映射
        prev_map = {det.get('id', ''): det for det in previous}

        for curr_det in current:
            curr_id = curr_det.get('id', '')
            if curr_id in prev_map:
                prev_det = prev_map[curr_id]

                # 计算位置变化
                curr_pos = curr_det.get('pos', [0, 0, 0, 0])
                prev_pos = prev_det.get('pos', [0, 0, 0, 0])

                if len(curr_pos) == 4 and len(prev_pos) == 4:
                    # 计算中心点距离
                    curr_center = [curr_pos[0] + curr_pos[2]/2, curr_pos[1] + curr_pos[3]/2]
                    prev_center = [prev_pos[0] + prev_pos[2]/2, prev_pos[1] + prev_pos[3]/2]

                    distance = np.sqrt((curr_center[0] - prev_center[0])**2 +
                                     (curr_center[1] - prev_center[1])**2)

                    if distance > self.position_threshold:
                        # 位置跳跃过大，进行平滑处理
                        smoothed_pos = self._smooth_position(curr_pos, prev_pos)
                        curr_det['pos'] = smoothed_pos
                        log.append(f"平滑位置跳跃: {curr_id} (距离: {distance:.1f})")

        return current

    def _check_confidence_changes(self, current: List[Dict], previous: List[Dict], log: List[str]) -> List[Dict]:
        """检查置信度异常变化"""
        prev_map = {det.get('id', ''): det for det in previous}

        for curr_det in current:
            curr_id = curr_det.get('id', '')
            if curr_id in prev_map:
                prev_det = prev_map[curr_id]

                curr_conf = curr_det.get('conf', 0.0)
                prev_conf = prev_det.get('conf', 0.0)

                # 检查置信度突然大幅下降
                if prev_conf > 0.8 and curr_conf < 0.3:
                    # 可能是误检，提高置信度阈值
                    smoothed_conf = (prev_conf + curr_conf) / 2
                    curr_det['conf'] = smoothed_conf
                    log.append(f"平滑置信度异常变化: {curr_id} {curr_conf:.3f} -> {smoothed_conf:.3f}")

        return current

    def _smooth_position(self, current_pos: List[float], previous_pos: List[float], alpha: float = 0.7) -> List[float]:
        """平滑位置变化"""
        # 使用指数移动平均进行平滑
        smoothed_pos = []
        for curr, prev in zip(current_pos, previous_pos):
            smoothed = alpha * curr + (1 - alpha) * prev
            smoothed_pos.append(smoothed)
        return smoothed_pos


class DataValidationPipeline:
    """
    数据验证管道
    整合所有验证和清洗功能
    """

    def __init__(self, config: Dict = None):
        """
        初始化验证管道

        Args:
            config: 配置字典
        """
        self.config = config or {}

        # 初始化各个组件
        self.validator = DetectionValidator(self.config.get('validator', {}))
        self.cleaner = DataCleaner(self.config.get('cleaner', {}))
        self.temporal_validator = TemporalValidator(self.config.get('temporal', {}))

        # 统计信息
        self.stats = {
            'total_processed': 0,
            'total_rejected': 0,
            'total_warnings': 0,
            'total_cleaned': 0,
            'processing_times': []
        }

    def process(self, detections: List[Dict]) -> Tuple[List[Dict], Dict]:
        """
        处理检测结果

        Args:
            detections: 原始检测结果

        Returns:
            处理后的检测结果和处理报告
        """
        start_time = time.time()

        if not detections:
            return [], {'status': 'empty', 'message': '无检测结果'}

        processing_log = []

        # 第一步：基础验证
        validated_detections = []
        qualities = []

        for detection in detections:
            result, quality = self.validator.validate_detection(detection)

            if result == ValidationResult.REJECT:
                processing_log.append(f"拒绝检测: {detection.get('id', 'unknown')} - {quality.issues}")
                self.stats['total_rejected'] += 1
            else:
                validated_detections.append(detection)
                qualities.append(quality)

                if result == ValidationResult.WARNING:
                    processing_log.append(f"警告检测: {detection.get('id', 'unknown')} - {quality.issues}")
                    self.stats['total_warnings'] += 1

        # 第二步：数据清洗
        if validated_detections:
            cleaned_detections, cleaning_log = self.cleaner.clean_detections(validated_detections, qualities)
            processing_log.extend(cleaning_log)
            self.stats['total_cleaned'] += len(validated_detections) - len(cleaned_detections)
        else:
            cleaned_detections = []

        # 第三步：时间一致性验证
        if cleaned_detections:
            final_detections, temporal_log = self.temporal_validator.validate_temporal_consistency(cleaned_detections)
            processing_log.extend(temporal_log)
        else:
            final_detections = []

        # 更新统计信息
        processing_time = time.time() - start_time
        self.stats['total_processed'] += len(detections)
        self.stats['processing_times'].append(processing_time)

        # 生成处理报告
        report = {
            'status': 'success',
            'input_count': len(detections),
            'output_count': len(final_detections),
            'rejected_count': self.stats['total_rejected'],
            'warning_count': self.stats['total_warnings'],
            'cleaned_count': self.stats['total_cleaned'],
            'processing_time': processing_time,
            'log': processing_log,
            'quality_scores': [q.overall_score for q in qualities] if qualities else []
        }

        return final_detections, report

    def get_statistics(self) -> Dict:
        """获取处理统计信息"""
        avg_time = np.mean(self.stats['processing_times']) if self.stats['processing_times'] else 0

        return {
            'total_processed': self.stats['total_processed'],
            'total_rejected': self.stats['total_rejected'],
            'total_warnings': self.stats['total_warnings'],
            'total_cleaned': self.stats['total_cleaned'],
            'average_processing_time': avg_time,
            'rejection_rate': self.stats['total_rejected'] / max(1, self.stats['total_processed']),
            'warning_rate': self.stats['total_warnings'] / max(1, self.stats['total_processed'])
        }
