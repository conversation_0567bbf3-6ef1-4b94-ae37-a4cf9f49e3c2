"""
简单测试脚本：验证卡牌尺寸启动控制功能
"""

import sys
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def test_card_size_activation_controller():
    """测试卡牌尺寸启动控制器基本功能"""
    print("🧪 测试卡牌尺寸启动控制器...")
    
    try:
        from src.modules.card_size_activation_controller import (
            CardSizeActivationController,
            CardSizeActivationConfig,
            SizeBaseline
        )
        
        # 创建配置
        config = CardSizeActivationConfig(
            size_threshold=0.85,
            qualified_ratio_threshold=0.9,
            min_card_count=20,
            baseline_cache_enabled=False,
            enable_size_logging=False
        )
        
        # 创建控制器
        controller = CardSizeActivationController(config)
        
        # 设置测试基准
        controller.size_baseline = SizeBaseline(
            width_median=45.0,
            height_median=60.0,
            area_median=2700.0,
            width_std=5.0,
            height_std=8.0,
            sample_count=100,
            confidence_level=0.95
        )
        
        print("✅ 控制器创建成功")
        
        # 测试1：充足且尺寸良好的卡牌
        print("\n📋 测试1：充足且尺寸良好的卡牌")
        good_detections = []
        for i in range(20):
            good_detections.append({
                "group_id": 1,
                "label": "二",
                "bbox": [100 + i*50, 100, 145 + i*50, 160],  # 45x60正常尺寸
                "confidence": 0.9
            })
        
        decision = controller.should_activate_digital_twin(good_detections)
        print(f"   启动决策: {decision.should_activate}")
        print(f"   卡牌数量: {decision.card_count}")
        print(f"   合格率: {decision.qualified_ratio:.1%}")
        print(f"   原因: {decision.reason}")
        
        assert decision.should_activate == True, "应该启动数字孪生"
        assert decision.card_count == 20, "卡牌数量应该是20"
        assert decision.qualified_ratio >= 0.9, "合格率应该≥90%"
        print("✅ 测试1通过")
        
        # 测试2：卡牌数量不足
        print("\n📋 测试2：卡牌数量不足")
        insufficient_detections = good_detections[:19]  # 只有19张
        
        decision = controller.should_activate_digital_twin(insufficient_detections)
        print(f"   启动决策: {decision.should_activate}")
        print(f"   卡牌数量: {decision.card_count}")
        print(f"   原因: {decision.reason}")
        
        assert decision.should_activate == False, "不应该启动数字孪生"
        assert decision.card_count == 19, "卡牌数量应该是19"
        print("✅ 测试2通过")
        
        # 测试3：尺寸质量不佳
        print("\n📋 测试3：尺寸质量不佳")
        poor_detections = []
        for i in range(20):
            poor_detections.append({
                "group_id": 1,
                "label": "二",
                "bbox": [100 + i*50, 100, 120 + i*50, 120],  # 20x20小尺寸
                "confidence": 0.9
            })
        
        decision = controller.should_activate_digital_twin(poor_detections)
        print(f"   启动决策: {decision.should_activate}")
        print(f"   卡牌数量: {decision.card_count}")
        print(f"   合格率: {decision.qualified_ratio:.1%}")
        print(f"   原因: {decision.reason}")
        
        assert decision.should_activate == False, "不应该启动数字孪生"
        assert decision.card_count == 20, "卡牌数量应该是20"
        assert decision.qualified_ratio < 0.9, "合格率应该<90%"
        print("✅ 测试3通过")
        
        # 测试统计信息
        print("\n📊 统计信息:")
        stats = controller.get_statistics()
        print(f"   总决策次数: {stats['activation_stats']['total_decisions']}")
        print(f"   启动次数: {stats['activation_stats']['activated_count']}")
        print(f"   未启动次数: {stats['activation_stats']['deactivated_count']}")
        
        print("\n🎉 卡牌尺寸启动控制器测试全部通过！")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_digital_twin_controller_integration():
    """测试与数字孪生主控器的集成"""
    print("\n🧪 测试数字孪生主控器集成...")
    
    try:
        from src.core.digital_twin_controller import (
            DigitalTwinConfig,
            ProcessingStrategy,
            create_controller_with_size_control
        )
        
        # 创建带有尺寸控制的主控器
        print("   创建主控器...")
        controller = create_controller_with_size_control(
            size_threshold=0.85,
            qualified_ratio_threshold=0.9,
            min_card_count=20
        )
        
        print("✅ 主控器创建成功")
        
        # 验证配置
        assert controller.config.enable_size_activation_control == True
        assert controller.config.size_threshold == 0.85
        assert controller.size_activation_controller is not None
        print("✅ 配置验证通过")
        
        # 测试系统状态
        print("   检查系统状态...")
        status = controller.get_system_status()
        
        assert "size_activation_control" in status
        assert status["size_activation_control"]["enabled"] == True
        assert status["size_activation_control"]["size_threshold"] == 0.85
        print("✅ 系统状态正确")
        
        print("\n🎉 数字孪生主控器集成测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试卡牌尺寸启动控制功能")
    print("=" * 60)
    
    # 运行测试
    test1_passed = test_card_size_activation_controller()
    test2_passed = test_digital_twin_controller_integration()
    
    print("\n" + "=" * 60)
    print("📋 测试结果总结:")
    print(f"   卡牌尺寸启动控制器: {'✅ 通过' if test1_passed else '❌ 失败'}")
    print(f"   数字孪生主控器集成: {'✅ 通过' if test2_passed else '❌ 失败'}")
    
    if test1_passed and test2_passed:
        print("\n🎉 所有测试通过！新功能已成功集成。")
        print("\n💡 功能特点:")
        print("   ✅ 基于0.85尺寸阈值的智能启动")
        print("   ✅ 观战方手牌区20张卡牌检测")
        print("   ✅ 90%合格率要求")
        print("   ✅ 原始数据完整保留")
        print("   ✅ 功能级联控制")
        print("   ✅ 接口完全一致")
        return True
    else:
        print("\n❌ 部分测试失败，请检查实现。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
