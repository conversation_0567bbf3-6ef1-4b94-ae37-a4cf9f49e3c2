#!/usr/bin/env python3
"""
验证Frame_00257.jpg修复效果的脚本

检查修复后的输出是否正确解决了"2叁"→"2伍"的错误
"""

import json
import logging
from pathlib import Path

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def verify_frame_00257_fix():
    """验证frame_00257的修复效果"""
    
    print("🔍 验证Frame_00257.jpg修复效果")
    print("=" * 50)
    
    # 文件路径
    frame_00256_path = Path("output/calibration_gt_final_with_digital_twin/labels/frame_00256.json")
    frame_00257_path = Path("output/calibration_gt_final_with_digital_twin/labels/frame_00257.json")
    
    if not frame_00256_path.exists():
        print(f"❌ 文件不存在: {frame_00256_path}")
        return False
        
    if not frame_00257_path.exists():
        print(f"❌ 文件不存在: {frame_00257_path}")
        return False
    
    # 读取数据
    with open(frame_00256_path, 'r', encoding='utf-8') as f:
        frame_00256_data = json.load(f)
        
    with open(frame_00257_path, 'r', encoding='utf-8') as f:
        frame_00257_data = json.load(f)
    
    print("📊 Frame_00256 分析:")
    analyze_frame(frame_00256_data, "frame_00256")
    
    print("\n📊 Frame_00257 分析:")
    analyze_frame(frame_00257_data, "frame_00257")
    
    # 验证7→9流转
    print("\n🔄 验证7→9流转:")
    verify_7_to_9_transition(frame_00256_data, frame_00257_data)
    
    return True

def analyze_frame(frame_data, frame_name):
    """分析单帧数据"""
    shapes = frame_data.get("shapes", [])
    
    # 按区域分组
    by_region = {}
    target_cards = []
    
    for shape in shapes:
        group_id = shape.get("group_id", 0)
        label = shape.get("label", "")
        twin_id = shape.get("attributes", {}).get("digital_twin_id", "")
        
        if group_id not in by_region:
            by_region[group_id] = []
        by_region[group_id].append({
            "label": label,
            "twin_id": twin_id
        })
        
        # 重点关注"叁"和"伍"相关的卡牌
        if any(char in label for char in ["叁", "伍", "三", "五"]) or any(char in twin_id for char in ["叁", "伍", "三", "五"]):
            target_cards.append({
                "label": label,
                "twin_id": twin_id,
                "group_id": group_id
            })
    
    # 输出区域分布
    for region_id in sorted(by_region.keys()):
        cards = by_region[region_id]
        if cards:  # 只显示有卡牌的区域
            print(f"  区域{region_id}: {len(cards)}张")
            for card in cards:
                print(f"    '{card['label']}' → '{card['twin_id']}'")
    
    # 输出目标卡牌
    if target_cards:
        print(f"  🎯 目标卡牌（叁/伍）:")
        for card in target_cards:
            print(f"    区域{card['group_id']}: '{card['label']}' → '{card['twin_id']}'")
    else:
        print(f"  ⚠️ 未发现目标卡牌（叁/伍）")

def verify_7_to_9_transition(frame_00256_data, frame_00257_data):
    """验证7→9流转"""
    
    # 提取frame_00256区域7的卡牌
    frame_256_region_7_cards = []
    for shape in frame_00256_data.get("shapes", []):
        if shape.get("group_id") == 7:
            label = shape.get("label", "")
            twin_id = shape.get("attributes", {}).get("digital_twin_id", "")
            frame_256_region_7_cards.append({
                "label": label,
                "twin_id": twin_id
            })
    
    # 提取frame_00257区域9的卡牌
    frame_257_region_9_cards = []
    for shape in frame_00257_data.get("shapes", []):
        if shape.get("group_id") == 9:
            label = shape.get("label", "")
            twin_id = shape.get("attributes", {}).get("digital_twin_id", "")
            frame_257_region_9_cards.append({
                "label": label,
                "twin_id": twin_id
            })
    
    print(f"  Frame_00256 区域7: {frame_256_region_7_cards}")
    print(f"  Frame_00257 区域9: {frame_257_region_9_cards}")
    
    # 检查是否有"叁"相关的卡牌
    frame_256_san_cards = [card for card in frame_256_region_7_cards if "叁" in card["label"] or "叁" in card["twin_id"]]
    frame_257_san_cards = [card for card in frame_257_region_9_cards if "叁" in card["label"] or "叁" in card["twin_id"]]
    
    print(f"\n  🔍 '叁'卡牌追踪:")
    print(f"    Frame_00256 区域7: {frame_256_san_cards}")
    print(f"    Frame_00257 区域9: {frame_257_san_cards}")
    
    # 验证修复效果
    success = True
    
    # 检查frame_00256区域7是否有"2叁"
    has_2san_in_256_region_7 = any(card["twin_id"] == "2叁" for card in frame_256_san_cards)
    
    # 检查frame_00257区域9是否有正确的"叁"卡牌（不应该是"2伍"）
    san_cards_in_257_region_9 = [card for card in frame_257_san_cards if "叁" in card["label"]]
    has_correct_san_in_257 = len(san_cards_in_257_region_9) > 0
    
    # 检查是否还有错误的"2伍"配"叁"的情况
    wrong_assignments = [card for card in frame_257_region_9_cards if "叁" in card["label"] and "伍" in card["twin_id"]]
    
    print(f"\n  ✅ 修复验证结果:")
    
    if has_2san_in_256_region_7:
        print(f"    ✅ Frame_00256 区域7 正确包含 '2叁'")
    else:
        print(f"    ⚠️ Frame_00256 区域7 未找到 '2叁'")
        success = False
    
    if has_correct_san_in_257:
        print(f"    ✅ Frame_00257 区域9 正确包含 '叁' 卡牌")
        for card in san_cards_in_257_region_9:
            print(f"      - '{card['label']}' → '{card['twin_id']}'")
    else:
        print(f"    ❌ Frame_00257 区域9 未找到 '叁' 卡牌")
        success = False
    
    if not wrong_assignments:
        print(f"    ✅ 未发现错误的 '叁'→'伍' ID分配")
    else:
        print(f"    ❌ 仍存在错误的 '叁'→'伍' ID分配:")
        for card in wrong_assignments:
            print(f"      - '{card['label']}' → '{card['twin_id']}'")
        success = False
    
    # 检查是否有"2伍"在错误位置
    wrong_2wu_assignments = [card for card in frame_257_region_9_cards if card["twin_id"] == "2伍" and "叁" in card["label"]]
    
    if not wrong_2wu_assignments:
        print(f"    ✅ 未发现 '2伍' 被错误分配给 '叁' 标签")
    else:
        print(f"    ❌ 发现 '2伍' 被错误分配给 '叁' 标签:")
        for card in wrong_2wu_assignments:
            print(f"      - '{card['label']}' → '{card['twin_id']}'")
        success = False
    
    if success:
        print(f"\n  🎉 修复成功！Frame_00257.jpg 的 '2叁'→'2伍' 错误已被修复")
    else:
        print(f"\n  ❌ 修复未完全成功，仍存在问题")
    
    return success

def main():
    """主函数"""
    try:
        success = verify_frame_00257_fix()
        
        if success:
            print(f"\n✅ 验证完成！修复效果良好。")
        else:
            print(f"\n❌ 验证发现问题，需要进一步检查。")
            
    except Exception as e:
        logger.error(f"验证过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
