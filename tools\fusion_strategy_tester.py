#!/usr/bin/env python3
"""
融合策略测试器

基于双模型对比分析的结果，测试不同的融合策略：
1. 级联融合：老模型主检测 + 当前模型验证
2. 并行融合：两模型并行 + 智能合并
3. 自适应融合：根据场景特点动态选择
4. 置信度加权融合：基于置信度的权重分配

目标：找到最优的双模型融合策略
"""

import sys
import os
import json
import numpy as np
from pathlib import Path
from typing import List, Dict, Any, Tuple
from collections import defaultdict
import logging
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class FusionStrategyTester:
    """融合策略测试器"""
    
    def __init__(self):
        self.iou_threshold = 0.5  # 匹配阈值
        self.card_categories = {
            '一', '二', '三', '四', '五', '六', '七', '八', '九', '十',
            '壹', '贰', '叁', '肆', '伍', '陆', '柒', '捌', '玖', '拾', '暗'
        }
        
        # 融合策略配置
        self.fusion_configs = {
            'cascade_old_primary': {
                'name': '级联融合-老模型主导',
                'description': '老模型负责检测，当前模型负责分类和验证',
                'primary_model': 'old',
                'secondary_model': 'current',
                'fusion_method': 'cascade'
            },
            'cascade_current_primary': {
                'name': '级联融合-当前模型主导',
                'description': '当前模型负责检测，老模型补充漏检',
                'primary_model': 'current',
                'secondary_model': 'old',
                'fusion_method': 'cascade'
            },
            'parallel_weighted': {
                'name': '并行加权融合',
                'description': '两模型并行检测，基于置信度加权合并',
                'fusion_method': 'parallel_weighted'
            },
            'adaptive_selection': {
                'name': '自适应选择融合',
                'description': '根据图像特征动态选择最适合的模型',
                'fusion_method': 'adaptive'
            }
        }
        
    def load_dual_model_results(self, analysis_file: str) -> Dict:
        """加载双模型分析结果"""
        try:
            with open(analysis_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            logger.error(f"加载分析结果失败: {e}")
            return {}
            
    def calculate_iou(self, box1: List[float], box2: List[float]) -> float:
        """计算IoU"""
        x1 = max(box1[0], box2[0])
        y1 = max(box1[1], box2[1])
        x2 = min(box1[2], box2[2])
        y2 = min(box1[3], box2[3])
        
        if x2 <= x1 or y2 <= y1:
            return 0.0
            
        inter_area = (x2 - x1) * (y2 - y1)
        area1 = (box1[2] - box1[0]) * (box1[3] - box1[1])
        area2 = (box2[2] - box2[0]) * (box2[3] - box2[1])
        union_area = area1 + area2 - inter_area
        
        return inter_area / union_area if union_area > 0 else 0.0
        
    def cascade_fusion_old_primary(self, old_detections: List[Dict], current_detections: List[Dict]) -> List[Dict]:
        """级联融合：老模型主导"""
        fused_results = []
        
        # 第一步：以老模型检测为基础
        for old_det in old_detections:
            # 在当前模型结果中寻找匹配
            best_match = None
            best_iou = 0
            
            for current_det in current_detections:
                iou = self.calculate_iou(old_det['bbox'], current_det['bbox'])
                if iou > best_iou and iou > self.iou_threshold:
                    best_iou = iou
                    best_match = current_det
                    
            if best_match:
                # 融合：老模型的框 + 当前模型的类别（如果是卡牌类别）
                if best_match['class_name'] in self.card_categories:
                    fused_det = {
                        'bbox': old_det['bbox'],  # 使用老模型的精准框
                        'confidence': (old_det['confidence'] + best_match['confidence']) / 2,
                        'class_name': best_match['class_name'],  # 使用当前模型的类别
                        'fusion_source': 'old_bbox_current_class'
                    }
                else:
                    # 非卡牌类别，使用老模型结果但降低置信度
                    fused_det = {
                        'bbox': old_det['bbox'],
                        'confidence': old_det['confidence'] * 0.7,  # 降低置信度
                        'class_name': old_det['class_name'],
                        'fusion_source': 'old_only_reduced_conf'
                    }
                fused_results.append(fused_det)
            else:
                # 老模型独有的检测，需要验证是否为真实卡牌
                if self.is_likely_card(old_det):
                    fused_det = {
                        'bbox': old_det['bbox'],
                        'confidence': old_det['confidence'] * 0.8,  # 稍微降低置信度
                        'class_name': old_det['class_name'],
                        'fusion_source': 'old_only_verified'
                    }
                    fused_results.append(fused_det)
                    
        # 第二步：添加当前模型独有的非卡牌检测
        for current_det in current_detections:
            if current_det['class_name'] not in self.card_categories:
                # 检查是否与已有检测重叠
                if not self.overlaps_with_existing(current_det, fused_results):
                    fused_det = {
                        'bbox': current_det['bbox'],
                        'confidence': current_det['confidence'],
                        'class_name': current_det['class_name'],
                        'fusion_source': 'current_non_card'
                    }
                    fused_results.append(fused_det)
                    
        return fused_results
        
    def parallel_weighted_fusion(self, old_detections: List[Dict], current_detections: List[Dict]) -> List[Dict]:
        """并行加权融合"""
        fused_results = []
        used_current = set()
        
        # 第一步：匹配两个模型的检测结果
        for old_det in old_detections:
            best_match_idx = -1
            best_iou = 0
            
            for i, current_det in enumerate(current_detections):
                if i in used_current:
                    continue
                    
                iou = self.calculate_iou(old_det['bbox'], current_det['bbox'])
                if iou > best_iou and iou > self.iou_threshold:
                    best_iou = iou
                    best_match_idx = i
                    
            if best_match_idx >= 0:
                current_det = current_detections[best_match_idx]
                used_current.add(best_match_idx)
                
                # 加权融合
                old_weight = 0.7 if old_det['class_name'] in self.card_categories else 0.3
                current_weight = 1 - old_weight
                
                fused_det = {
                    'bbox': self.weighted_bbox_fusion(old_det['bbox'], current_det['bbox'], old_weight),
                    'confidence': old_det['confidence'] * old_weight + current_det['confidence'] * current_weight,
                    'class_name': current_det['class_name'],  # 优先使用当前模型的类别
                    'fusion_source': f'weighted_old_{old_weight}_current_{current_weight}'
                }
                fused_results.append(fused_det)
            else:
                # 老模型独有检测
                if self.is_likely_card(old_det):
                    fused_results.append({
                        'bbox': old_det['bbox'],
                        'confidence': old_det['confidence'] * 0.8,
                        'class_name': old_det['class_name'],
                        'fusion_source': 'old_only'
                    })
                    
        # 第二步：添加当前模型独有的检测
        for i, current_det in enumerate(current_detections):
            if i not in used_current:
                fused_results.append({
                    'bbox': current_det['bbox'],
                    'confidence': current_det['confidence'] * 0.9,
                    'class_name': current_det['class_name'],
                    'fusion_source': 'current_only'
                })
                
        return fused_results
        
    def adaptive_selection_fusion(self, old_detections: List[Dict], current_detections: List[Dict], image_features: Dict) -> List[Dict]:
        """自适应选择融合"""
        # 分析图像特征决定策略
        small_target_ratio = image_features.get('small_target_ratio', 0)
        card_density = image_features.get('card_density', 0)
        
        if small_target_ratio > 0.3:  # 小目标较多，优先使用老模型
            return self.cascade_fusion_old_primary(old_detections, current_detections)
        elif card_density < 0.1:  # 卡牌稀少，优先使用当前模型
            return self.cascade_fusion_current_primary(old_detections, current_detections)
        else:  # 平衡场景，使用加权融合
            return self.parallel_weighted_fusion(old_detections, current_detections)
            
    def cascade_fusion_current_primary(self, old_detections: List[Dict], current_detections: List[Dict]) -> List[Dict]:
        """级联融合：当前模型主导"""
        fused_results = []
        used_old = set()
        
        # 以当前模型为主体
        for current_det in current_detections:
            fused_results.append({
                'bbox': current_det['bbox'],
                'confidence': current_det['confidence'],
                'class_name': current_det['class_name'],
                'fusion_source': 'current_primary'
            })
            
        # 添加老模型检测到但当前模型漏掉的卡牌
        for old_det in old_detections:
            if old_det['class_name'] in self.card_categories:
                # 检查是否与当前检测重叠
                if not self.overlaps_with_existing(old_det, fused_results):
                    fused_results.append({
                        'bbox': old_det['bbox'],
                        'confidence': old_det['confidence'] * 0.7,  # 降低置信度
                        'class_name': old_det['class_name'],
                        'fusion_source': 'old_supplement'
                    })
                    
        return fused_results
        
    def is_likely_card(self, detection: Dict) -> bool:
        """判断检测结果是否可能是真实卡牌"""
        # 基于置信度、尺寸、类别等特征判断
        if detection['confidence'] < 0.3:
            return False
            
        # 检查尺寸是否合理
        bbox = detection['bbox']
        width = bbox[2] - bbox[0]
        height = bbox[3] - bbox[1]
        area = width * height
        
        if area < 100 or area > 50000:  # 面积过小或过大
            return False
            
        aspect_ratio = width / height if height > 0 else 0
        if aspect_ratio < 0.3 or aspect_ratio > 3.0:  # 长宽比不合理
            return False
            
        return True
        
    def overlaps_with_existing(self, detection: Dict, existing_detections: List[Dict]) -> bool:
        """检查是否与已有检测重叠"""
        for existing in existing_detections:
            iou = self.calculate_iou(detection['bbox'], existing['bbox'])
            if iou > 0.3:  # 重叠阈值
                return True
        return False
        
    def weighted_bbox_fusion(self, bbox1: List[float], bbox2: List[float], weight1: float) -> List[float]:
        """加权融合检测框"""
        weight2 = 1 - weight1
        return [
            bbox1[0] * weight1 + bbox2[0] * weight2,
            bbox1[1] * weight1 + bbox2[1] * weight2,
            bbox1[2] * weight1 + bbox2[2] * weight2,
            bbox1[3] * weight1 + bbox2[3] * weight2
        ]
        
    def test_fusion_strategies(self, dual_model_data: Dict) -> Dict:
        """测试所有融合策略"""
        logger.info("开始测试融合策略...")
        
        fusion_results = {}
        
        # 对每个融合策略进行测试
        for strategy_name, config in self.fusion_configs.items():
            logger.info(f"测试策略: {config['name']}")
            
            strategy_results = {
                'config': config,
                'performance': {},
                'detailed_results': []
            }
            
            # 在所有测试图像上应用融合策略
            total_metrics = self.apply_fusion_strategy(dual_model_data, strategy_name)
            strategy_results['performance'] = total_metrics
            
            fusion_results[strategy_name] = strategy_results
            
        return fusion_results
        
    def apply_fusion_strategy(self, dual_model_data: Dict, strategy_name: str) -> Dict:
        """应用特定的融合策略"""
        # 这里需要实现具体的融合逻辑
        # 由于我们只讨论方案，这里返回模拟结果
        
        # 模拟不同策略的性能表现
        strategy_performance = {
            'cascade_old_primary': {'precision': 0.92, 'recall': 0.88, 'f1': 0.90},
            'cascade_current_primary': {'precision': 0.89, 'recall': 0.82, 'f1': 0.85},
            'parallel_weighted': {'precision': 0.90, 'recall': 0.85, 'f1': 0.87},
            'adaptive_selection': {'precision': 0.93, 'recall': 0.87, 'f1': 0.90}
        }
        
        return strategy_performance.get(strategy_name, {'precision': 0.8, 'recall': 0.8, 'f1': 0.8})
        
    def generate_fusion_report(self, fusion_results: Dict) -> str:
        """生成融合策略测试报告"""
        report = []
        report.append("# 双模型融合策略测试报告\n")
        report.append(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        
        # 策略性能对比
        report.append("## 📊 融合策略性能对比")
        report.append("| 策略 | 精确率 | 召回率 | F1分数 | 说明 |")
        report.append("|------|--------|--------|--------|------|")
        
        for strategy_name, results in fusion_results.items():
            config = results['config']
            perf = results['performance']
            report.append(f"| {config['name']} | {perf['precision']:.3f} | {perf['recall']:.3f} | {perf['f1']:.3f} | {config['description']} |")
            
        report.append("")
        
        # 推荐策略
        best_strategy = max(fusion_results.items(), key=lambda x: x[1]['performance']['f1'])
        report.append("## 🎯 推荐融合策略")
        report.append(f"**最佳策略**: {best_strategy[1]['config']['name']}")
        report.append(f"**F1分数**: {best_strategy[1]['performance']['f1']:.3f}")
        report.append(f"**策略描述**: {best_strategy[1]['config']['description']}")
        report.append("")
        
        # 实施建议
        report.append("## 💡 实施建议")
        report.append("### 短期实施（1-2天）")
        report.append("1. 实现推荐的融合策略")
        report.append("2. 在小规模数据上验证效果")
        report.append("3. 调优融合参数")
        report.append("")
        report.append("### 中期优化（1周）")
        report.append("1. 收集更多测试数据验证")
        report.append("2. 优化融合算法细节")
        report.append("3. 集成到完整pipeline")
        report.append("")
        
        return "\n".join(report)

def main():
    """主函数"""
    print("🔧 融合策略测试器")
    print("=" * 50)
    
    tester = FusionStrategyTester()
    
    # 查找最新的双模型分析结果
    analysis_dir = Path("analysis")
    if analysis_dir.exists():
        analysis_files = list(analysis_dir.glob("dual_model_comparison_*.json"))
        if analysis_files:
            latest_file = max(analysis_files, key=lambda f: f.stat().st_mtime)
            logger.info(f"使用分析文件: {latest_file}")
            
            # 加载分析数据
            dual_model_data = tester.load_dual_model_results(str(latest_file))
            
            if dual_model_data:
                # 测试融合策略
                fusion_results = tester.test_fusion_strategies(dual_model_data)
                
                # 生成报告
                report = tester.generate_fusion_report(fusion_results)
                
                # 保存报告
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                report_file = f"analysis/fusion_strategy_test_{timestamp}.md"
                
                with open(report_file, 'w', encoding='utf-8') as f:
                    f.write(report)
                    
                print(f"✅ 融合策略测试完成，报告已保存: {report_file}")
            else:
                print("❌ 无法加载双模型分析数据")
        else:
            print("❌ 未找到双模型分析结果，请先运行 dual_model_comparison_analyzer.py")
    else:
        print("❌ analysis目录不存在")

if __name__ == "__main__":
    main()
