#!/usr/bin/env python3
"""
测试遮挡补偿中的暗牌修复逻辑
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'src', 'core'))

from digital_twin_v3 import create_digital_twin_system

def test_compensation_fix():
    """测试遮挡补偿中的暗牌修复"""
    print("🧪 测试遮挡补偿中的暗牌修复逻辑")
    
    # 创建数字孪生系统
    system = create_digital_twin_system()
    
    # 第一帧：建立偎牌（1明2暗）
    frame1_detections = [
        {
            'label': '暗',
            'bbox': [100, 200, 150, 250],  # 最下面的暗牌
            'confidence': 0.8,
            'group_id': 6
        },
        {
            'label': '暗',
            'bbox': [100, 150, 150, 200],  # 中间的暗牌
            'confidence': 0.8,
            'group_id': 6
        },
        {
            'label': '二',
            'bbox': [100, 100, 150, 150],  # 最上面的明牌
            'confidence': 0.9,
            'group_id': 6
        }
    ]
    
    result1 = system.process_frame(frame1_detections, 1)
    print(f"第一帧结果: {len(result1['digital_twin_cards'])}张卡牌")
    for card in result1['digital_twin_cards']:
        print(f"  - {card.twin_id} (标签: {card.label}, 暗牌: {card.is_dark})")
    
    # 第二帧：模拟暗牌被遮挡，只有明牌
    frame2_detections = [
        {
            'label': '二',
            'bbox': [100, 100, 150, 150],  # 明牌继续存在
            'confidence': 0.9,
            'group_id': 6
        }
    ]
    
    print(f"\n第二帧：模拟暗牌被遮挡")
    result2 = system.process_frame(frame2_detections, 2)
    print(f"第二帧结果: {len(result2['digital_twin_cards'])}张卡牌")
    for card in result2['digital_twin_cards']:
        print(f"  - {card.twin_id} (标签: {card.label}, 暗牌: {card.is_dark})")
    
    # 检查是否有暗牌被正确补偿
    compensated_dark_cards = [card for card in result2['digital_twin_cards'] if card.is_dark]
    print(f"\n🔍 补偿的暗牌:")
    for card in compensated_dark_cards:
        print(f"  - {card.twin_id}")
    
    # 检查是否有错误的通用暗牌ID
    generic_dark_cards = [card for card in compensated_dark_cards if card.twin_id.endswith('_暗')]
    if generic_dark_cards:
        print(f"❌ 发现错误的通用暗牌ID: {[card.twin_id for card in generic_dark_cards]}")
        return False
    else:
        print(f"✅ 所有暗牌都正确关联: {[card.twin_id for card in compensated_dark_cards]}")
        return True

if __name__ == "__main__":
    print("🔧 测试遮挡补偿中的暗牌修复")
    print("=" * 50)
    
    success = test_compensation_fix()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 测试通过! 遮挡补偿中的暗牌修复成功!")
    else:
        print("❌ 测试失败，需要进一步调试")
