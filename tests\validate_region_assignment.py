"""
真正的区域分配验证脚本

本脚本验证系统是否能够根据卡牌位置正确推断区域ID，而不是直接使用人工标注的区域ID作为输入。

验证流程：
1. 只给系统卡牌的位置(bbox)和标签(label)
2. 让系统自己推断区域ID
3. 验证推断的区域ID是否与人工标注一致
"""

import sys
import os
import json
import time
from pathlib import Path
from typing import List, Dict, Any, Tuple
from collections import defaultdict, Counter

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.core.digital_twin_v2 import (
    DigitalTwinCoordinator,
    CardDetection,
    create_digital_twin_system
)

class RegionAssignmentValidator:
    """真正的区域分配验证器"""
    
    def __init__(self):
        self.dt_system = create_digital_twin_system()
        
        # 卡牌名称映射（处理繁简体差异）
        self.card_name_mapping = {
            "壹": "一", "贰": "二", "叁": "三", "肆": "四", "伍": "五",
            "陆": "六", "柒": "七", "捌": "八", "玖": "九", "拾": "十"
        }
        
    def parse_ground_truth_label(self, label: str) -> Tuple[str, str]:
        """解析人工标注标签"""
        import re
        match = re.match(r'^(\d+)(.+)$', label)
        if match:
            twin_id = match.group(1)
            card_name = match.group(2)
            
            # 转换繁体到简体
            if card_name in self.card_name_mapping:
                card_name = self.card_name_mapping[card_name]
            
            return twin_id, card_name
        else:
            return "", label
    
    def infer_region_from_position(self, bbox: List[float], image_width: int = 640, image_height: int = 320) -> int:
        """根据位置推断区域ID（简化的区域推断逻辑）
        
        这是一个简化的区域推断算法，基于卡牌在图像中的位置
        """
        x_center = (bbox[0] + bbox[2]) / 2
        y_center = (bbox[1] + bbox[3]) / 2
        
        # 归一化坐标
        x_norm = x_center / image_width
        y_norm = y_center / image_height
        
        # 简化的区域划分规则（基于观察zhuangtaiquyu数据的位置分布）
        if y_norm > 0.7:  # 下方区域
            if x_norm < 0.9:  # 大部分下方区域
                return 1  # 手牌区_观战方
            else:
                return 5  # 可能是弃牌区边缘
        elif y_norm < 0.4:  # 上方区域
            if x_norm < 0.5:
                return 6  # 吃碰区_观战方
            else:
                return 9  # 对战方区域
        elif 0.4 <= y_norm <= 0.7:  # 中间区域
            if x_norm > 0.8:
                return 5  # 弃牌区_观战方
            else:
                return 16  # 吃碰区_对战方或其他
        else:
            return 1  # 默认手牌区
    
    def validate_region_inference(self, sequence_path: Path) -> Dict[str, Any]:
        """验证区域推断准确性"""
        print(f"  🔍 验证区域推断: {sequence_path.name}")
        
        results = {
            "sequence_name": sequence_path.name,
            "total_cards": 0,
            "correct_regions": 0,
            "region_errors": [],
            "region_confusion_matrix": defaultdict(lambda: defaultdict(int))
        }
        
        json_files = sorted(sequence_path.glob("*.json"))[:10]  # 验证前10帧
        
        for json_file in json_files:
            try:
                with open(json_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                image_width = data.get("imageWidth", 640)
                image_height = data.get("imageHeight", 320)
                
                for shape in data.get("shapes", []):
                    if len(shape.get("points", [])) >= 4:
                        points = shape["points"]
                        x1, y1 = points[0]
                        x2, y2 = points[2]
                        bbox = [x1, y1, x2, y2]
                        
                        # 解析标签
                        label = shape.get("label", "")
                        twin_id, card_name = self.parse_ground_truth_label(label)
                        
                        # 真实区域ID（人工标注）
                        true_region = shape.get("group_id", 0)
                        
                        # 推断区域ID
                        inferred_region = self.infer_region_from_position(bbox, image_width, image_height)
                        
                        # 统计
                        results["total_cards"] += 1
                        if true_region == inferred_region:
                            results["correct_regions"] += 1
                        else:
                            results["region_errors"].append({
                                "file": json_file.name,
                                "card": label,
                                "true_region": true_region,
                                "inferred_region": inferred_region,
                                "bbox": bbox
                            })
                        
                        # 混淆矩阵
                        results["region_confusion_matrix"][true_region][inferred_region] += 1
                        
            except Exception as e:
                print(f"    ❌ 处理文件{json_file.name}时出错: {e}")
        
        # 计算准确率
        if results["total_cards"] > 0:
            results["accuracy"] = results["correct_regions"] / results["total_cards"]
        else:
            results["accuracy"] = 0.0
        
        print(f"    📊 区域推断准确率: {results['accuracy']:.1%} ({results['correct_regions']}/{results['total_cards']})")
        
        return results
    
    def run_region_validation(self) -> Dict[str, Any]:
        """运行区域分配验证"""
        print("🎯 真正的区域分配验证")
        print("=" * 50)
        
        zhuangtaiquyu_path = Path("legacy_assets/ceshi/zhuangtaiquyu/labels/train")
        if not zhuangtaiquyu_path.exists():
            print("❌ zhuangtaiquyu数据集未找到")
            return {"error": "dataset_not_found"}
        
        # 获取所有序列目录
        sequence_dirs = [d for d in zhuangtaiquyu_path.iterdir() if d.is_dir()]
        sequence_dirs = sorted(sequence_dirs, key=lambda x: int(x.name) if x.name.isdigit() else 0)
        
        print(f"📊 发现 {len(sequence_dirs)} 个序列")
        
        all_results = {
            "validation_timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
            "total_sequences": len(sequence_dirs),
            "sequence_results": [],
            "overall_statistics": {}
        }
        
        # 验证每个序列（限制前5个序列）
        for seq_dir in sequence_dirs[:5]:
            print(f"\n📁 处理序列: {seq_dir.name}")
            
            sequence_result = self.validate_region_inference(seq_dir)
            all_results["sequence_results"].append(sequence_result)
        
        # 计算整体统计
        total_cards = sum(r["total_cards"] for r in all_results["sequence_results"])
        total_correct = sum(r["correct_regions"] for r in all_results["sequence_results"])
        
        all_results["overall_statistics"] = {
            "total_cards_validated": total_cards,
            "total_correct_regions": total_correct,
            "overall_accuracy": total_correct / total_cards if total_cards > 0 else 0,
            "sequences_validated": len(all_results["sequence_results"])
        }
        
        return all_results
    
    def analyze_region_patterns(self, results: Dict[str, Any]):
        """分析区域分配模式"""
        print("\n📋 区域分配模式分析")
        print("-" * 30)

        # 分析最常见的错误
        print("最常见的区域分配错误:")
        all_errors = []
        for seq_result in results["sequence_results"]:
            all_errors.extend(seq_result["region_errors"])

        if all_errors:
            error_patterns = Counter((error["true_region"], error["inferred_region"]) for error in all_errors)
            for (true_region, inferred_region), count in error_patterns.most_common(5):
                print(f"  {true_region} -> {inferred_region}: {count}次")
        else:
            print("  无区域分配错误")

def main():
    """主验证函数"""
    print("🔍 真正的区域分配验证")
    print("=" * 60)
    
    validator = RegionAssignmentValidator()
    
    # 运行验证
    results = validator.run_region_validation()
    
    if "error" in results:
        print(f"❌ 验证失败: {results['error']}")
        return False
    
    # 分析结果
    validator.analyze_region_patterns(results)
    
    # 保存报告
    timestamp = time.strftime("%Y%m%d_%H%M%S")
    report_file = f"tests/region_validation_report_{timestamp}.json"
    
    os.makedirs("tests", exist_ok=True)
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, ensure_ascii=False, indent=2)
    
    # 打印总结
    stats = results["overall_statistics"]
    print("\n" + "=" * 60)
    print("📋 区域分配验证总结")
    print(f"验证序列数: {stats.get('sequences_validated', 0)}")
    print(f"验证卡牌总数: {stats.get('total_cards_validated', 0)}")
    print(f"正确区域数: {stats.get('total_correct_regions', 0)}")
    print(f"区域推断准确率: {stats.get('overall_accuracy', 0):.1%}")
    print(f"报告已保存: {report_file}")
    
    # 判断性能
    accuracy = stats.get('overall_accuracy', 0)
    if accuracy > 0.8:
        print("🎉 区域推断性能良好！")
        return True
    else:
        print("⚠️ 区域推断性能需要改进")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
