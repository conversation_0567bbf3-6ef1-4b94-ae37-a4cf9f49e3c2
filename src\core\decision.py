import numpy as np
import random
from typing import Dict, List, Tuple, Optional
from enum import Enum

class ActionPriority(Enum):
    """动作优先级枚举"""
    HU = 1      # 胡牌 - 最高优先级
    PENG = 2    # 碰牌
    PAO = 2     # 跑牌（与碰牌同级）
    CHI = 3     # 吃牌
    PLAY = 4    # 打牌
    PASS = 5    # 过 - 最低优先级

class SimpleRandomAgent:
    """
    简单的随机代理（保持向后兼容）
    """

    def __init__(self, num_actions=5):
        """
        初始化随机代理

        Args:
            num_actions (int): 动作数量
        """
        self.num_actions = num_actions

    def eval_step(self, state):
        """
        评估步骤，随机选择一个合法动作

        Args:
            state (dict): 状态，包含legal_actions

        Returns:
            int: 选择的动作
        """
        legal_actions = state.get('legal_actions', [])

        if not legal_actions:
            return 4  # 默认"过"

        # 如果legal_actions是字符串列表，转换为数字
        int_actions = []
        for action in legal_actions:
            if isinstance(action, str):
                if action == 'play':
                    int_actions.append(0)
                elif action == 'peng':
                    int_actions.append(1)
                elif action == 'chi':
                    int_actions.append(2)
                elif action == 'hu':
                    int_actions.append(3)
                elif action == 'pass':
                    int_actions.append(4)
            else:
                int_actions.append(action)

        if not int_actions:
            return 4  # 默认"过"
            
        return random.choice(int_actions)

class DecisionMaker:
    """
    改进的决策模块
    基于跑胡子规则的智能决策
    """

    def __init__(self, config=None):
        """
        初始化决策模块

        Args:
            config (dict): 配置信息
        """
        self.config = config or {}
        self.seed = self.config.get('seed', 42)
        random.seed(self.seed)
        np.random.seed(self.seed)

        # 动作优先级映射
        self.action_priorities = {
            'hu': ActionPriority.HU.value,
            'peng': ActionPriority.PENG.value,
            'pao': ActionPriority.PAO.value,
            'chi': ActionPriority.CHI.value,
            'play': ActionPriority.PLAY.value,
            'pass': ActionPriority.PASS.value
        }

        # 特殊组合价值
        self.combination_values = {
            '二七十': 10,
            '大小三搭': 8,
            '顺子': 5,
            '刻子': 6
        }

        # 初始化代理（保持向后兼容）
        self.agent = SimpleRandomAgent(num_actions=5)
    
    def make_decision(self, state):
        """
        根据当前状态做出决策

        Args:
            state (dict): 当前状态

        Returns:
            tuple: (动作, 置信度)
        """
        if not isinstance(state, dict):
            raise ValueError("状态必须是字典类型")

        legal_actions = state.get('legal_actions', [])
        if not legal_actions:
            return 'pass', 1.0

        # 按优先级排序动作
        prioritized_actions = self._prioritize_actions(legal_actions)

        # 评估每个动作的价值
        action_values = {}
        for action in prioritized_actions:
            value = self._evaluate_action(action, state)
            action_values[action] = value

        # 选择最佳动作
        best_action = max(action_values.keys(), key=lambda x: action_values[x])
        confidence = self._calculate_confidence(best_action, action_values, state)

        return best_action, confidence
    
    def update_model(self, trajectory, reward):
        """
        更新模型
        
        Args:
            trajectory (list): 轨迹
            reward (float): 奖励
        """
        # 简单代理不需要更新
        pass
    
    def get_win_rate(self, state):
        """
        计算胜率

        Args:
            state (dict): 当前状态

        Returns:
            float: 胜率 (0-1)
        """
        # 支持新旧两种状态格式
        hand_cards = state.get('hand_player', state.get('hand', []))
        chi_peng_cards = state.get('chi_peng_player', state.get('combo_cards', []))
        special_combinations = state.get('special_combinations', [])

        # 基础胜率计算
        base_rate = 0.3

        # 根据手牌数量调整
        hand_count = len(hand_cards)
        if hand_count <= 6:
            base_rate += 0.3  # 手牌少，接近胡牌
        elif hand_count <= 12:
            base_rate += 0.1

        # 根据已有组合调整
        if isinstance(chi_peng_cards, list) and len(chi_peng_cards) > 0:
            if isinstance(chi_peng_cards[0], dict):
                # 新格式：Card对象
                set_count = len(chi_peng_cards) // 3
            else:
                # 旧格式：元组
                set_count = len(chi_peng_cards) // 3
            base_rate += set_count * 0.15

        # 特殊组合加成
        special_bonus = len(special_combinations) * 0.1
        base_rate += special_bonus

        # 确保在合理范围内
        return max(0.05, min(0.95, base_rate))
    
    def format_decision(self, action, confidence, win_rate):
        """
        格式化决策结果
        
        Args:
            action: 动作
            confidence: 置信度
            win_rate: 胜率
            
        Returns:
            dict: 格式化的决策结果
        """
        action_mapping = {
            0: '出牌',
            1: '碰',
            2: '吃',
            3: '胡',
            4: '过',
            'play': '出牌',
            'peng': '碰',
            'chi': '吃',
            'hu': '胡',
            'pass': '过'
        }
        
        action_name = action_mapping.get(action, str(action))
        
        return {
            '推荐动作': action_name,
            '置信度': f"{confidence:.2%}",
            '胜率': f"{win_rate:.2%}",
            '原始动作': action
        }

    def _prioritize_actions(self, legal_actions: List[str]) -> List[str]:
        """按优先级排序动作"""
        return sorted(legal_actions, key=lambda x: self.action_priorities.get(x, 999))

    def _evaluate_action(self, action: str, state: Dict) -> float:
        """评估动作价值"""
        if action == 'hu':
            return self._evaluate_hu_action(state)
        elif action == 'peng':
            return self._evaluate_peng_action(state)
        elif action == 'chi':
            return self._evaluate_chi_action(state)
        elif action == 'play':
            return self._evaluate_play_action(state)
        elif action == 'pass':
            return self._evaluate_pass_action(state)
        else:
            return 0.0

    def _evaluate_hu_action(self, state: Dict) -> float:
        """评估胡牌动作"""
        # 胡牌总是最高价值
        base_value = 100.0

        # 考虑特殊组合加成
        special_combinations = state.get('special_combinations', [])
        bonus = sum(self.combination_values.get(combo.get('type', ''), 0)
                   for combo in special_combinations)

        return base_value + bonus

    def _evaluate_peng_action(self, state: Dict) -> float:
        """评估碰牌动作"""
        base_value = 20.0

        # 检查碰牌后的手牌情况
        hand_cards = state.get('hand_player', state.get('hand', []))

        # 如果手牌较少，碰牌价值更高
        if len(hand_cards) <= 6:
            base_value += 10.0

        # 检查是否有助于形成特殊组合
        if self._helps_special_combination(state):
            base_value += 15.0

        return base_value

    def _evaluate_chi_action(self, state: Dict) -> float:
        """评估吃牌动作"""
        base_value = 15.0

        # 检查比牌选项
        bi_pai_options = state.get('bi_pai_options', [])
        if len(bi_pai_options) > 1:
            # 有多种吃法时，选择最优的
            base_value += 5.0

        # 检查是否有助于胡牌
        if self._helps_winning(state):
            base_value += 10.0

        return base_value

    def _evaluate_play_action(self, state: Dict) -> float:
        """评估打牌动作"""
        base_value = 10.0

        # 根据手牌情况调整
        hand_cards = state.get('hand_player', state.get('hand', []))

        # 手牌过多时，打牌价值较高
        if len(hand_cards) > 15:
            base_value += 5.0

        return base_value

    def _evaluate_pass_action(self, state: Dict) -> float:
        """评估过牌动作"""
        # 过牌通常价值较低，除非有特殊考虑
        base_value = 1.0

        # 检查是否应该避免暴露手牌信息
        if self._should_hide_information(state):
            base_value += 8.0

        return base_value

    def _helps_special_combination(self, state: Dict) -> bool:
        """检查动作是否有助于形成特殊组合"""
        # 简化实现，检查是否接近二七十或大小三搭
        hand_cards = state.get('hand_player', state.get('hand', []))

        # 处理新旧格式
        if hand_cards and isinstance(hand_cards[0], dict):
            # 新格式：Card对象
            card_labels = [card.get('label', '') for card in hand_cards]
        else:
            # 旧格式：元组，需要转换
            card_labels = []
            for card in hand_cards:
                if isinstance(card, tuple) and len(card) == 2:
                    # 简化处理，暂时跳过
                    pass

        # 检查二七十
        er_qi_shi_small = ['二', '七', '十']
        er_qi_shi_large = ['贰', '柒', '拾']

        small_count = sum(1 for label in er_qi_shi_small if label in card_labels)
        large_count = sum(1 for label in er_qi_shi_large if label in card_labels)

        return small_count >= 2 or large_count >= 2

    def _helps_winning(self, state: Dict) -> bool:
        """检查动作是否有助于胡牌"""
        # 简化实现，检查手牌数量和组合情况
        hand_cards = state.get('hand_player', state.get('hand', []))
        chi_peng_cards = state.get('chi_peng_player', state.get('combo_cards', []))

        # 计算已有的组合数
        existing_sets = len(chi_peng_cards) // 3
        remaining_cards = len(hand_cards)

        # 如果接近胡牌条件
        return remaining_cards <= 6 and existing_sets >= 2

    def _should_hide_information(self, state: Dict) -> bool:
        """检查是否应该隐藏手牌信息"""
        # 如果手牌很好，可能不想暴露给对手
        special_combinations = state.get('special_combinations', [])
        return len(special_combinations) > 0

    def _calculate_confidence(self, best_action: str, action_values: Dict, state: Dict) -> float:
        """计算决策置信度"""
        if not action_values:
            return 0.5

        best_value = action_values[best_action]
        all_values = list(action_values.values())

        if len(all_values) == 1:
            return 0.9

        # 计算最佳动作与次佳动作的差距
        sorted_values = sorted(all_values, reverse=True)
        if len(sorted_values) >= 2:
            value_gap = sorted_values[0] - sorted_values[1]
            max_possible_gap = sorted_values[0]

            if max_possible_gap > 0:
                confidence = 0.5 + 0.4 * (value_gap / max_possible_gap)
            else:
                confidence = 0.5
        else:
            confidence = 0.9

        # 特殊情况调整
        if best_action == 'hu':
            confidence = min(0.95, confidence + 0.2)  # 胡牌置信度更高
        elif best_action == 'pass':
            confidence = max(0.3, confidence - 0.2)   # 过牌置信度较低

        return max(0.1, min(0.99, confidence))

def test_decision_maker():
    """测试决策模块"""
    # 创建决策模块
    decision_maker = DecisionMaker()
    
    # 模拟状态
    state = {
        'hand': [(2, 0), (3, 0), (4, 0), (5, 0), (6, 0)],
        'discard_pile': [(7, 0), (8, 0)],
        'opponent_discard_pile': [(10, 0), (1, 1)],
        'combo_cards': [(9, 0), (9, 0), (9, 0)],
        'opponent_combo_cards': [(2, 1), (2, 1), (2, 1)],
        'legal_actions': [0, 1, 2, 4]
    }
    
    # 做出决策
    action, confidence = decision_maker.make_decision(state)
    
    # 计算胜率
    win_rate = decision_maker.get_win_rate(state)
    
    # 格式化决策结果
    result = decision_maker.format_decision(action, confidence, win_rate)
    
    # 打印结果
    print("决策结果:")
    for key, value in result.items():
        print(f"{key}: {value}")

if __name__ == "__main__":
    test_decision_maker() 