"""
基于zhuangtaiquyu人工标注的深度验证脚本

本脚本使用zhuangtaiquyu数据集的人工标注作为标准答案，对以下系统进行全面验证：
1. 数字孪生系统V2.0的ID分配准确性
2. 区域分配逻辑的正确性
3. 记忆机制的有效性
4. 系统整体性能评估

人工标注格式：{ID}{卡牌名称}，例如 1壹、2三、1八 等
这正是GAME_RULES.md中描述的标准格式！
"""

import sys
import os
import json
import time
from pathlib import Path
from typing import List, Dict, Any, Tuple
from collections import defaultdict, Counter
import re

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.core.digital_twin_v2 import (
    DigitalTwinCoordinator,
    CardDetection,
    create_digital_twin_system
)

class GroundTruthValidator:
    """基于人工标注的验证器"""
    
    def __init__(self):
        self.dt_system = create_digital_twin_system()
        self.validation_results = {
            "id_assignment_accuracy": {},
            "region_assignment_accuracy": {},
            "memory_mechanism_effectiveness": {},
            "overall_performance": {}
        }
        
        # 卡牌名称映射（处理繁简体差异）
        self.card_name_mapping = {
            "壹": "一", "贰": "二", "叁": "三", "肆": "四", "伍": "五",
            "陆": "六", "柒": "七", "捌": "八", "玖": "九", "拾": "十"
        }
        
    def parse_ground_truth_label(self, label: str) -> Tuple[str, str]:
        """解析人工标注标签
        
        Args:
            label: 人工标注标签，如 "1壹"、"2三"
            
        Returns:
            (twin_id, card_name): 如 ("1", "一")
        """
        # 使用正则表达式解析
        match = re.match(r'^(\d+)(.+)$', label)
        if match:
            twin_id = match.group(1)
            card_name = match.group(2)
            
            # 转换繁体到简体
            if card_name in self.card_name_mapping:
                card_name = self.card_name_mapping[card_name]
            
            return twin_id, card_name
        else:
            return "", label
    
    def load_ground_truth_sequence(self, sequence_path: Path) -> List[Dict[str, Any]]:
        """加载一个序列的人工标注数据"""
        sequence_data = []
        
        json_files = sorted(sequence_path.glob("*.json"))
        for json_file in json_files:
            try:
                with open(json_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                frame_data = {
                    "frame_file": json_file.name,
                    "ground_truth_cards": [],
                    "image_width": data.get("imageWidth", 640),
                    "image_height": data.get("imageHeight", 320)
                }
                
                # 解析人工标注
                for shape in data.get("shapes", []):
                    if len(shape.get("points", [])) >= 4:
                        points = shape["points"]
                        x1, y1 = points[0]
                        x2, y2 = points[2]
                        
                        # 解析标签
                        label = shape.get("label", "")
                        twin_id, card_name = self.parse_ground_truth_label(label)
                        
                        gt_card = {
                            "original_label": label,
                            "twin_id": twin_id,
                            "card_name": card_name,
                            "bbox": [x1, y1, x2, y2],
                            "group_id": shape.get("group_id", 0),
                            "confidence": 1.0  # 人工标注置信度为1.0
                        }
                        frame_data["ground_truth_cards"].append(gt_card)
                
                sequence_data.append(frame_data)
                
            except Exception as e:
                print(f"    ❌ 加载文件{json_file.name}时出错: {e}")
        
        return sequence_data
    
    def validate_sequence(self, sequence_data: List[Dict[str, Any]], sequence_name: str) -> Dict[str, Any]:
        """验证一个序列"""
        print(f"  🔍 验证序列: {sequence_name}")
        
        # 重置数字孪生系统
        self.dt_system.reset_session()
        
        sequence_results = {
            "sequence_name": sequence_name,
            "total_frames": len(sequence_data),
            "id_matches": 0,
            "id_total": 0,
            "region_matches": 0,
            "region_total": 0,
            "frame_results": [],
            "id_assignment_errors": [],
            "region_assignment_errors": []
        }
        
        for frame_idx, frame_data in enumerate(sequence_data):
            # 转换为检测结果格式
            detections = []
            for gt_card in frame_data["ground_truth_cards"]:
                detection = CardDetection(
                    label=gt_card["card_name"],
                    bbox=gt_card["bbox"],
                    confidence=gt_card["confidence"],
                    group_id=gt_card["group_id"],
                    region_name=f"region_{gt_card['group_id']}",
                    owner="ground_truth"
                )
                detections.append(detection)
            
            # 使用数字孪生系统处理
            dt_result = self.dt_system.process_frame(detections)
            
            # 验证ID分配
            frame_id_matches, frame_id_total, id_errors = self._validate_id_assignment(
                frame_data["ground_truth_cards"], 
                dt_result["digital_twin_cards"],
                frame_idx
            )
            
            # 验证区域分配
            frame_region_matches, frame_region_total, region_errors = self._validate_region_assignment(
                frame_data["ground_truth_cards"],
                dt_result["digital_twin_cards"],
                frame_idx
            )
            
            # 累计统计
            sequence_results["id_matches"] += frame_id_matches
            sequence_results["id_total"] += frame_id_total
            sequence_results["region_matches"] += frame_region_matches
            sequence_results["region_total"] += frame_region_total
            sequence_results["id_assignment_errors"].extend(id_errors)
            sequence_results["region_assignment_errors"].extend(region_errors)
            
            # 记录帧结果
            frame_result = {
                "frame_idx": frame_idx,
                "frame_file": frame_data["frame_file"],
                "id_accuracy": frame_id_matches / frame_id_total if frame_id_total > 0 else 0,
                "region_accuracy": frame_region_matches / frame_region_total if frame_region_total > 0 else 0,
                "consensus_score": dt_result["consensus_score"],
                "total_cards": len(dt_result["digital_twin_cards"])
            }
            sequence_results["frame_results"].append(frame_result)
        
        # 计算序列整体准确率
        sequence_results["id_accuracy"] = sequence_results["id_matches"] / sequence_results["id_total"] if sequence_results["id_total"] > 0 else 0
        sequence_results["region_accuracy"] = sequence_results["region_matches"] / sequence_results["region_total"] if sequence_results["region_total"] > 0 else 0
        
        print(f"    ✅ ID分配准确率: {sequence_results['id_accuracy']:.1%}")
        print(f"    ✅ 区域分配准确率: {sequence_results['region_accuracy']:.1%}")
        
        return sequence_results
    
    def _validate_id_assignment(self, gt_cards: List[Dict], dt_cards: List, frame_idx: int) -> Tuple[int, int, List[Dict]]:
        """验证ID分配准确性"""
        matches = 0
        total = 0
        errors = []
        
        # 创建位置匹配映射
        for gt_card in gt_cards:
            total += 1
            best_match = None
            best_iou = 0.0
            
            # 寻找最佳位置匹配的数字孪生卡牌
            for dt_card in dt_cards:
                if dt_card.label == gt_card["card_name"]:
                    iou = self._calculate_iou(gt_card["bbox"], dt_card.bbox)
                    if iou > best_iou:
                        best_iou = iou
                        best_match = dt_card
            
            if best_match and best_iou > 0.5:  # IoU阈值
                # 检查ID是否匹配
                expected_id = f"{gt_card['twin_id']}_{gt_card['card_name']}"
                actual_id = best_match.twin_id
                
                if expected_id == actual_id:
                    matches += 1
                else:
                    errors.append({
                        "frame_idx": frame_idx,
                        "card_name": gt_card["card_name"],
                        "expected_id": expected_id,
                        "actual_id": actual_id,
                        "iou": best_iou,
                        "error_type": "id_mismatch"
                    })
            else:
                errors.append({
                    "frame_idx": frame_idx,
                    "card_name": gt_card["card_name"],
                    "expected_id": f"{gt_card['twin_id']}_{gt_card['card_name']}",
                    "actual_id": "not_found",
                    "iou": best_iou,
                    "error_type": "card_not_found"
                })
        
        return matches, total, errors
    
    def _validate_region_assignment(self, gt_cards: List[Dict], dt_cards: List, frame_idx: int) -> Tuple[int, int, List[Dict]]:
        """验证区域分配准确性"""
        matches = 0
        total = 0
        errors = []
        
        for gt_card in gt_cards:
            total += 1
            best_match = None
            best_iou = 0.0
            
            # 寻找最佳位置匹配的数字孪生卡牌
            for dt_card in dt_cards:
                if dt_card.label == gt_card["card_name"]:
                    iou = self._calculate_iou(gt_card["bbox"], dt_card.bbox)
                    if iou > best_iou:
                        best_iou = iou
                        best_match = dt_card
            
            if best_match and best_iou > 0.5:
                # 检查区域是否匹配
                expected_region = gt_card["group_id"]
                actual_region = best_match.group_id
                
                if expected_region == actual_region:
                    matches += 1
                else:
                    errors.append({
                        "frame_idx": frame_idx,
                        "card_name": gt_card["card_name"],
                        "expected_region": expected_region,
                        "actual_region": actual_region,
                        "iou": best_iou,
                        "error_type": "region_mismatch"
                    })
        
        return matches, total, errors
    
    def _calculate_iou(self, bbox1: List[float], bbox2: List[float]) -> float:
        """计算IoU"""
        x1_1, y1_1, x2_1, y2_1 = bbox1
        x1_2, y1_2, x2_2, y2_2 = bbox2
        
        # 计算交集
        x1_inter = max(x1_1, x1_2)
        y1_inter = max(y1_1, y1_2)
        x2_inter = min(x2_1, x2_2)
        y2_inter = min(y2_1, y2_2)
        
        if x2_inter <= x1_inter or y2_inter <= y1_inter:
            return 0.0
        
        inter_area = (x2_inter - x1_inter) * (y2_inter - y1_inter)
        
        # 计算并集
        area1 = (x2_1 - x1_1) * (y2_1 - y1_1)
        area2 = (x2_2 - x1_2) * (y2_2 - y1_2)
        union_area = area1 + area2 - inter_area
        
        return inter_area / union_area if union_area > 0 else 0.0

    def run_full_validation(self) -> Dict[str, Any]:
        """运行完整验证"""
        print("🚀 开始基于zhuangtaiquyu人工标注的深度验证")
        print("=" * 60)

        zhuangtaiquyu_path = Path("legacy_assets/ceshi/zhuangtaiquyu/labels/train")
        if not zhuangtaiquyu_path.exists():
            print("❌ zhuangtaiquyu数据集未找到")
            return {"error": "dataset_not_found"}

        # 获取所有序列目录
        sequence_dirs = [d for d in zhuangtaiquyu_path.iterdir() if d.is_dir()]
        sequence_dirs = sorted(sequence_dirs, key=lambda x: int(x.name) if x.name.isdigit() else 0)

        print(f"📊 发现 {len(sequence_dirs)} 个序列")

        all_results = {
            "validation_timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
            "total_sequences": len(sequence_dirs),
            "sequence_results": [],
            "overall_statistics": {},
            "error_analysis": {},
            "recommendations": []
        }

        # 验证每个序列（限制前5个序列进行快速验证）
        for seq_dir in sequence_dirs[:5]:
            print(f"\n📁 处理序列: {seq_dir.name}")

            # 加载序列数据
            sequence_data = self.load_ground_truth_sequence(seq_dir)
            if not sequence_data:
                print(f"  ⚠️ 序列 {seq_dir.name} 无有效数据")
                continue

            print(f"  📄 加载了 {len(sequence_data)} 帧数据")

            # 验证序列
            sequence_result = self.validate_sequence(sequence_data, seq_dir.name)
            all_results["sequence_results"].append(sequence_result)

        # 计算整体统计
        all_results["overall_statistics"] = self._calculate_overall_statistics(all_results["sequence_results"])

        # 错误分析
        all_results["error_analysis"] = self._analyze_errors(all_results["sequence_results"])

        # 生成建议
        all_results["recommendations"] = self._generate_recommendations(all_results)

        return all_results

    def _calculate_overall_statistics(self, sequence_results: List[Dict]) -> Dict[str, Any]:
        """计算整体统计"""
        if not sequence_results:
            return {}

        total_id_matches = sum(r["id_matches"] for r in sequence_results)
        total_id_total = sum(r["id_total"] for r in sequence_results)
        total_region_matches = sum(r["region_matches"] for r in sequence_results)
        total_region_total = sum(r["region_total"] for r in sequence_results)
        total_frames = sum(r["total_frames"] for r in sequence_results)

        # 计算平均共识分数
        all_consensus_scores = []
        for seq_result in sequence_results:
            for frame_result in seq_result["frame_results"]:
                all_consensus_scores.append(frame_result["consensus_score"])

        avg_consensus_score = sum(all_consensus_scores) / len(all_consensus_scores) if all_consensus_scores else 0

        return {
            "total_sequences_validated": len(sequence_results),
            "total_frames_processed": total_frames,
            "overall_id_accuracy": total_id_matches / total_id_total if total_id_total > 0 else 0,
            "overall_region_accuracy": total_region_matches / total_region_total if total_region_total > 0 else 0,
            "average_consensus_score": avg_consensus_score,
            "total_cards_validated": total_id_total,
            "id_assignment_success_rate": total_id_matches / total_id_total if total_id_total > 0 else 0,
            "region_assignment_success_rate": total_region_matches / total_region_total if total_region_total > 0 else 0
        }

    def _analyze_errors(self, sequence_results: List[Dict]) -> Dict[str, Any]:
        """分析错误模式"""
        all_id_errors = []
        all_region_errors = []

        for seq_result in sequence_results:
            all_id_errors.extend(seq_result["id_assignment_errors"])
            all_region_errors.extend(seq_result["region_assignment_errors"])

        # ID错误分析
        id_error_types = Counter(error["error_type"] for error in all_id_errors)
        id_error_cards = Counter(error["card_name"] for error in all_id_errors)

        # 区域错误分析
        region_error_types = Counter(error["error_type"] for error in all_region_errors)
        region_error_patterns = Counter(
            f"{error['expected_region']}->{error['actual_region']}"
            for error in all_region_errors if error["error_type"] == "region_mismatch"
        )

        return {
            "total_id_errors": len(all_id_errors),
            "total_region_errors": len(all_region_errors),
            "id_error_types": dict(id_error_types),
            "id_error_by_card": dict(id_error_cards.most_common(10)),
            "region_error_types": dict(region_error_types),
            "region_error_patterns": dict(region_error_patterns.most_common(10)),
            "sample_id_errors": all_id_errors[:5],
            "sample_region_errors": all_region_errors[:5]
        }

    def _generate_recommendations(self, results: Dict[str, Any]) -> List[str]:
        """生成改进建议"""
        recommendations = []
        stats = results["overall_statistics"]
        errors = results["error_analysis"]

        # ID分配准确率建议
        id_accuracy = stats.get("overall_id_accuracy", 0)
        if id_accuracy < 0.8:
            recommendations.append(f"🔧 ID分配准确率较低({id_accuracy:.1%})，建议优化物理约束管理器的ID分配逻辑")
        elif id_accuracy < 0.9:
            recommendations.append(f"⚡ ID分配准确率中等({id_accuracy:.1%})，可进一步优化帧间继承算法")
        else:
            recommendations.append(f"✅ ID分配准确率良好({id_accuracy:.1%})，系统运行正常")

        # 区域分配准确率建议
        region_accuracy = stats.get("overall_region_accuracy", 0)
        if region_accuracy < 0.8:
            recommendations.append(f"🔧 区域分配准确率较低({region_accuracy:.1%})，建议检查区域分配逻辑")
        elif region_accuracy < 0.9:
            recommendations.append(f"⚡ 区域分配准确率中等({region_accuracy:.1%})，可优化空间关系分析")
        else:
            recommendations.append(f"✅ 区域分配准确率良好({region_accuracy:.1%})，系统运行正常")

        # 共识分数建议
        consensus_score = stats.get("average_consensus_score", 0)
        if consensus_score < 0.6:
            recommendations.append(f"🔧 平均共识分数较低({consensus_score:.3f})，建议调整多帧共识验证参数")
        elif consensus_score < 0.8:
            recommendations.append(f"⚡ 平均共识分数中等({consensus_score:.3f})，可优化异常检测算法")
        else:
            recommendations.append(f"✅ 平均共识分数良好({consensus_score:.3f})，多帧验证机制有效")

        # 错误模式分析建议
        if errors.get("total_id_errors", 0) > 0:
            top_error_card = list(errors.get("id_error_by_card", {}).keys())[0] if errors.get("id_error_by_card") else None
            if top_error_card:
                recommendations.append(f"🎯 '{top_error_card}'卡牌ID分配错误最多，建议重点优化该卡牌的识别逻辑")

        if errors.get("total_region_errors", 0) > 0:
            top_region_pattern = list(errors.get("region_error_patterns", {}).keys())[0] if errors.get("region_error_patterns") else None
            if top_region_pattern:
                recommendations.append(f"🎯 区域流转模式'{top_region_pattern}'错误最多，建议优化该区域间的转换逻辑")

        return recommendations

    def save_validation_report(self, results: Dict[str, Any], output_path: str = None):
        """保存验证报告"""
        if output_path is None:
            timestamp = time.strftime("%Y%m%d_%H%M%S")
            output_path = f"tests/ground_truth_validation_report_{timestamp}.json"

        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)

        print(f"\n📄 验证报告已保存: {output_path}")
        return output_path

def main():
    """主验证函数"""
    print("🎯 基于zhuangtaiquyu人工标注的数字孪生系统V2.0深度验证")
    print("=" * 70)

    validator = GroundTruthValidator()

    # 运行完整验证
    results = validator.run_full_validation()

    if "error" in results:
        print(f"❌ 验证失败: {results['error']}")
        return False

    # 保存报告
    report_path = validator.save_validation_report(results)

    # 打印总结
    stats = results["overall_statistics"]
    print("\n" + "=" * 70)
    print("📋 验证总结")
    print(f"验证序列数: {stats.get('total_sequences_validated', 0)}")
    print(f"处理帧数: {stats.get('total_frames_processed', 0)}")
    print(f"验证卡牌总数: {stats.get('total_cards_validated', 0)}")
    print(f"ID分配准确率: {stats.get('overall_id_accuracy', 0):.1%}")
    print(f"区域分配准确率: {stats.get('overall_region_accuracy', 0):.1%}")
    print(f"平均共识分数: {stats.get('average_consensus_score', 0):.3f}")

    print("\n🎯 改进建议:")
    for i, recommendation in enumerate(results["recommendations"], 1):
        print(f"{i}. {recommendation}")

    print(f"\n📊 详细报告: {report_path}")

    # 判断整体性能
    id_accuracy = stats.get('overall_id_accuracy', 0)
    region_accuracy = stats.get('overall_region_accuracy', 0)

    if id_accuracy > 0.8 and region_accuracy > 0.8:
        print("🎉 系统整体性能良好！")
        return True
    else:
        print("⚠️ 系统性能需要改进")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
