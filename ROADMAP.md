# phz-ai-simple 项目路线图 (v1.0, 更新日期 2025/07/16)

### 阶段1: 基础设置和文档确认
- 目标: 复制资产、完善文档、安装依赖。
- 完成度: 100% [已完成: 环境设置、依赖安装（包括PyTorch cu128 v2.9.0.dev20250711）、目录整理、Ollama v0.9.6安装]
- 更新日期: 2025/7/12

### 阶段2: 核心组件实现
- 目标: 实现 YOLO 视觉检测、RLCard状态表示、MCTS 决策逻辑，并进行集成测试。
- 子任务:
  - 实现 YOLO 卡牌检测 MVP（使用 Ultralytics 库，测试准确率 >95%）。[已完成: 2025/7/13]
    - 创建了detect.py实现基础检测功能
    - 创建了test_detector.py用于测试和评估检测性能
    - 创建了main.py实现实时屏幕捕获和检测
    - 添加了config.yaml配置文件
  - 集成 RLCard 框架作为状态层 [已完成: 2025/7/15]
    - 已安装RLCard库并验证功能
    - 创建了paohuzi_env.py实现跑胡子游戏环境
    - 实现了state_builder.py用于YOLO检测结果到RLCard状态的转换
    - 创建了test_state_conversion.py用于测试状态转换功能
  - 集成决策引擎 [已完成: 2025/7/15]
    - 创建了decision.py实现基于随机代理的简单决策模块
    - 实现了胜率估算和决策格式化
    - 创建了test_decision.py用于测试决策功能
  - 集成测试 [已完成: 2025/7/15]
    - 创建了test_calibration.py用于评估检测模型性能
    - 创建了test_end_to_end.py用于测试端到端流程
    - 创建了test_video.py用于测试视频处理功能
    - 创建了README_TESTS.md文档说明测试方法
    - 优化了group_id分配逻辑，使用空间关系分析更准确地分配卡牌区域
    - 启用GPU加速，显著提升了检测速度（约10倍性能提升）
  - 稳定性优化 [已完成: 2025/7/15]
    - 改进了format_detections_for_state_builder函数，使用空间关系分析
    - 处理了边缘情况，如遮挡和暗牌的识别
    - 记录了性能指标，检测速度从3.22 FPS提升至约30+ FPS（使用GPU）
- 完成度: 100% [已完成: YOLO卡牌检测MVP, RLCard框架集成, 状态转换模块, 决策模块, 测试脚本, 性能优化]
- 更新日期: 2025/7/15

### 阶段2.5: calibration_gt数字孪生ID生成系统 [已完成: 2025/7/18]
- 目标: 为calibration_gt数据集生成完整的数字孪生ID标注
- 子任务:
  - 基础功能验证 [已完成]
    - 验证数字孪生系统在calibration_gt数据集上的可行性
    - 成功生成数字孪生ID，建立处理流程
  - 完整性改进 [已完成]
    - 处理所有371个JSON文件（目标372个，实际存在371个）
    - 增加错误处理和失败帧保存机制
    - 达到91.37%处理成功率
  - 坐标修复 [已完成]
    - 100%保留原始人工标注的精确坐标和区域信息
    - 完全保留原始JSON文件的置信度和元数据
    - 解决坐标随机生成问题
  - 格式优化 [已完成]
    - AnyLabeling格式：数字孪生ID无下划线（2壹、3柒、虚拟二）
    - RLCard格式：保留下划线（2_壹、3_柒、虚拟_二）
    - 清空冗余描述信息，保持标注简洁
- 成果:
  - 处理了12,163张卡牌，生成11,057个数字孪生ID
  - 输出339个完整的双格式标注文件
  - 建立了完整的错误报告和质量保证机制
- 完成度: 100% [已完成: calibration_gt_final_processor.py]
- 更新日期: 2025/7/18

### 阶段2.8: 卡牌尺寸启动控制功能 [已完成: 2025/7/20] 🆕
- 目标: 新增基于卡牌尺寸的智能启动机制，解决牌局展开期的识别错误问题
- 子任务:
  - 新增CardSizeActivationController功能模块 [已完成]
    - 创建了card_size_activation_controller.py核心模块
    - 实现0.85尺寸阈值的智能启动判断
    - 支持观战方手牌区20张卡牌检测
    - 实现90%合格率的质量控制
    - 自动从现有JSON文件提取尺寸基准
  - 集成到DigitalTwinController [已完成]
    - 在统一主控器中集成新的启动控制逻辑
    - 实现功能级联控制（控制整个数字孪生功能链）
    - 支持双路径处理：启动时完整处理，未启动时保留原始数据
    - 添加配置化的参数调整支持
  - 编写测试用例 [已完成]
    - 创建了test_card_size_activation_controller.py单元测试
    - 创建了test_digital_twin_controller_integration.py集成测试
    - 创建了examples/card_size_activation_demo.py功能演示
    - 验证了接口一致性和向后兼容性
  - 更新calibration_gt_final_processor.py [已完成]
    - 使用create_controller_with_size_control()替代原来的create_complete_controller()
    - 自动应用0.85尺寸阈值和相关控制逻辑
    - 在日志中显示启动决策信息
  - 文档更新 [已完成]
    - 更新了数字孪生模块化结构目录
    - 更新了数字孪生ID系统技术规范
    - 更新了数字孪生主控器设计方案
    - 更新了ARCHITECTURE.md和相关技术文档
- 完成度: 100% [已完成: 新功能模块, 主控器集成, 测试验证, 文档更新]
- 更新日期: 2025/7/20

### 阶段3: 规则融合与实时优化
- 目标: 完成跑胡子规则实现，优化实时性能，实现端到端演示。
- 子任务:
  - 决策层改进 [预期完成: 2025/7/17]
    - 实现基于规则的智能体，替代当前的随机代理
    - 实现跑胡子核心策略规则（优先考虑胡牌、评估碰吃价值）
    - 集成loudi.yaml中的特殊规则（比牌、臭牌机制等）
    - 实现特殊牌型组合（二七十、大小三搭）的识别和处理
  - 实际视频测试与模型优化 [预期完成: 2025/7/19]
    - 从现有视频素材中选择10个代表性视频进行测试
    - 实现自动化测试脚本，对比检测结果与基准数据
    - 测量关键指标：准确率、召回率、F1分数、检测延迟
    - 基于测试结果识别并优化YOLO模型的弱点
    - 针对性地增强数据集，特别是对暗牌和遮挡情况
  - 状态转换与区域分配优化 [预期完成: 2025/7/20]
    - 验证智能区域分配算法是否符合预期，排除逻辑冲突
    - 完善YOLO检测结果到RLCard状态的转换逻辑
    - 处理特殊情况：暗牌、遮挡牌、虚拟提示牌
    - 实现卡牌ID的稳定跟踪和状态更新
  - 特殊机制实现 [预期完成: 2025/7/21]
    - 实现比牌机制（展示所有可能的吃牌组合）
    - 实现臭牌机制（记录并避免使用臭牌）
    - 设计UI展示多种吃牌选择和臭牌状态
  - 实时演示系统开发 [预期完成: 2025/7/22]
    - 将视觉层、状态层和决策层完全集成
    - 实现实时屏幕捕获、检测、状态转换和决策的流水线
    - 设计直观的UI显示检测结果、游戏状态和决策建议
    - 实现实时胜率计算和显示
    - 进一步优化GPU利用率，实现并行处理流程
    - 里程碑: 实时demo (屏幕捕获 → 决策)。更新ROADMAP.md with 教训。
- 关键里程碑:
  - 基于规则的智能体完成（7/17）
  - 视频测试结果分析报告（7/19）
  - 特殊机制实现完成（7/21）
  - 实时演示系统可用（7/22）
- 风险与应对策略:
  - 风险: 复杂规则实现难度 – 应对: 分阶段实现，先核心后特殊
  - 风险: 实时性能不足 – 应对: 进一步优化GPU利用，考虑降低分辨率或帧率
  - 风险: 边缘情况处理 – 应对: 实现健壮的错误恢复机制，利用前后帧信息
  - 风险: 集成复杂度 – 应对: 定义清晰的接口规范，增加单元测试覆盖率
- 完成度: 20% [已开始: 创建了测试框架, 优化了GAME_RULES_OPTIMIZED.md文档]
- 预期开始: 2025/7/16
- 预期完成: 2025/7/22

## 阶段2回顾与阶段3风险评估（基于开发过程6-阶段二3.md）

### 游戏规则理解偏差分析

1. **比牌机制和臭牌机制的缺失**
   - **问题**：GAME_RULES_OPTIMIZED.md中详细描述了比牌机制（吃牌时必须展示所有可能组合）和臭牌机制（本可以吃但选择不吃的牌，之后不能再吃），但这些在RLCard环境实现中完全缺失
   - **影响**：决策层无法做出符合游戏规则的正确判断，可能导致非法操作
   - **解决方案**：在阶段3中优先实现这两个核心机制

2. **特殊牌型组合检测逻辑未实现**
   - **问题**："二七十"和"大小三搭"等特殊组合的识别和处理逻辑缺失
   - **影响**：无法正确评估牌型价值，影响决策质量
   - **解决方案**：基于loudi.yaml配置实现完整的特殊组合检测

3. **强制规则优先级未体现**
   - **问题**：胡 > 碰/跑 > 吃的优先级在决策层中没有体现
   - **影响**：可能出现违反游戏规则的决策
   - **解决方案**：重构决策引擎，实现规则优先级约束

### 检测精度与游戏需求不匹配问题

1. **性能指标矛盾**
   - **问题**：测试结果显示"P/R/F1均为0.0"，但声称"检测准确率>95%"
   - **根本原因**：测试方法存在问题，可能使用了错误的基准数据或评估标准
   - **解决方案**：重新建立标准化的测试基准和评估方法

2. **大小字区分能力未验证**
   - **问题**：跑胡子需要准确区分"大字"和"小字"（如"十"vs"拾"），当前检测能力未经充分验证
   - **风险**：可能导致关键牌型误判
   - **解决方案**：建立专门的大小字区分测试集

### 新发现的风险

#### 游戏状态一致性风险（高风险 - 未在原ROADMAP中提及）
- **问题描述**：检测结果的时序不一致可能导致状态混乱
- **具体表现**：
  - 同一张牌在连续帧中可能被分配不同的ID
  - 卡牌状态变化（明牌↔暗牌）缺乏验证机制
  - 缺乏状态回滚和修正机制
- **影响范围**：整个决策系统的可靠性
- **应对策略**：实现状态管理中间层，提供状态验证和错误恢复

#### 规则完整性风险（中风险 - 被严重忽视）
- **问题描述**：当前实现的规则可能只是跑胡子的子集
- **具体表现**：
  - 不同地区规则变体的处理策略缺失
  - 边缘情况的规则处理不完整
  - 规则冲突的解决机制缺失
- **影响范围**：系统的通用性和准确性
- **应对策略**：建立完整的规则配置系统，支持规则变体

### 架构设计偏移分析

1. **三层架构的耦合问题**
   - 设计偏移:
     - 视觉层直接输出给状态层，缺乏中间的数据验证和清洗层
     - 状态层承担了过多责任，既要处理检测结果又要维护游戏状态
     - 决策层过于简化，无法承载复杂的跑胡子规则
   - 建议重构: 应该有一个专门的"游戏状态管理器"来处理检测结果的验证、卡牌ID追踪、状态一致性检查

2. **实时性与准确性的平衡失调**
   - 架构问题:
     - 30+ FPS的检测速度可能是过度优化，跑胡子不需要如此高的帧率
     - 更应该关注检测的稳定性和状态的一致性
     - 缺乏帧间信息的利用，导致检测结果可能出现跳跃

### 阶段3整改计划 - 具体实施方案

#### 立即行动项（阶段三第一天必须解决）

**1. 重新设计状态转换逻辑**
- **时间安排**：2025/7/16（1天）
- **具体任务**：
  - 重构`format_detections_for_state_builder`函数
  - 实现基于游戏规则的智能区域分配算法
  - 添加卡牌状态验证机制（验证ID唯一性、状态合法性）
  - 实现暗牌和特殊牌型的检测逻辑
- **验收标准**：
  - 能正确处理偎牌（1明2暗）状态
  - 能正确处理提牌（1明3暗）状态
  - 状态转换通过所有单元测试
- **负责模块**：`src/state_builder.py`

**2. 建立完整的测试基准**
- **时间安排**：2025/7/16-7/17（1.5天）
- **具体任务**：
  - 创建包含所有特殊情况的测试数据集
  - 重新设计评估指标，解决P/R/F1为0的问题
  - 实现端到端的准确性验证脚本
  - 建立性能回归测试框架
- **验收标准**：
  - 测试数据集覆盖所有卡牌类型和状态
  - 评估指标能正确反映系统性能
  - 自动化测试脚本可重复运行
- **负责模块**：`src/test_*.py`

#### 规则引擎重构（优先级高）

**3. 完整规则集成**
- **时间安排**：2025/7/17-7/18（1.5天）
- **具体任务**：
  - 将`loudi.yaml`中的规则完全集成到RLCard环境
  - 实现强制规则的优先级处理（胡 > 碰/跑 > 吃）
  - 添加比牌机制支持
  - 添加臭牌机制支持
- **验收标准**：
  - 所有loudi.yaml中的规则都有对应实现
  - 规则优先级测试通过
  - 比牌和臭牌机制功能正常
- **负责模块**：`src/decision.py`, `src/game_env.py`

#### 架构调整（阶段三中期）

**4. 添加状态管理中间层**
- **时间安排**：2025/7/18-7/19（1.5天）
- **具体任务**：
  - 创建新模块`src/state_manager.py`
  - 实现检测结果的验证和清洗功能
  - 实现卡牌ID的稳定跟踪和状态更新
  - 添加状态回滚和错误恢复机制
- **验收标准**：
  - 状态管理器能处理所有边缘情况
  - 卡牌ID在整个游戏过程中保持稳定
  - 错误恢复机制测试通过
- **负责模块**：新建`src/state_manager.py`

**5. 重新平衡性能目标**
- **时间安排**：2025/7/19-7/20（1天）
- **具体任务**：
  - 降低帧率要求到10-15 FPS，提高检测准确性
  - 实现智能跳帧和关键帧检测
  - 加强帧间信息的利用
  - 优化GPU利用率
- **验收标准**：
  - 系统稳定运行在10-15 FPS
  - 检测准确率提升到>98%
  - 关键帧检测功能正常
- **负责模块**：`src/detect.py`, `src/main.py`

#### 风险缓解措施

**游戏状态一致性风险缓解**：
- 实现状态快照和回滚机制
- 添加状态变化的合法性验证
- 建立状态异常的自动检测和修复

**规则完整性风险缓解**：
- 建立规则配置的版本管理
- 实现规则冲突的检测和解决
- 添加规则变体的支持框架

#### 每日检查点

- **Day 1 (7/16)**：状态转换逻辑重构完成，测试基准建立开始
- **Day 2 (7/17)**：测试基准完成，规则引擎重构开始
- **Day 3 (7/18)**：规则引擎完成，状态管理中间层开始
- **Day 4 (7/19)**：状态管理完成，性能优化开始
- **Day 5 (7/20)**：性能优化完成，集成测试开始
- **Day 6 (7/21)**：特殊机制实现
- **Day 7 (7/22)**：实时演示系统完成

### 阶段4: 扩展与优化 (Week 3-4)
- 目标: 增加多模态支持，训练专用模型，完成系统文档。
- 子任务:
  - 加多模态 (Ollama + LLaVA) 绕过复杂检测。
  - 训练小模型: 用RLCard训练RL代理 (基于收集的数据)。
  - 里程碑: 完整系统，胜率计算90%准确。最终文档更新。
- 完成度: 0%
- 预期开始: 2025/7/23
- 预期完成: 2025/8/6

## 版本控制策略
- 使用GitHub进行代码版本管理
- 模型文件(.pt)通过.gitignore排除，手动管理
- 每个阶段完成后创建标签版本
- 实验性功能在单独分支开发

## 风险与缓解
- 风险: AI遗忘 – 缓解: 每次@文档。
- 风险: 理解不深 – 缓解: 小步测试，每步跑demo体验。
- 更新机制: 每周复盘，标记"已完成调整"。

## 长期愿景
- 扩展语音输出、持续提高决策胜率。
- 探索更多卡牌游戏的通用框架。






