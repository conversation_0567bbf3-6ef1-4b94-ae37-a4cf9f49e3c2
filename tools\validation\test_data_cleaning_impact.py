#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
数据清洗影响测试器

专门测试数据清洗对检测结果的影响，这可能是导致与AnyLabeling差异的主要原因。
"""

import os
import sys
import cv2
import json
import time
from pathlib import Path
from typing import Dict, List, Any, Tuple
import numpy as np

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)

from src.core.detect import CardDetector
from ultralytics import YOLO


class DataCleaningImpactTester:
    """数据清洗影响测试器"""
    
    def __init__(self):
        """初始化测试器"""
        self.model_path = r"D:\phz-ai-simple\data\processed\train9.0\weights\best.pt"
        self.onnx_model_path = r"D:\phz-ai-simple\data\processed\train9.0\weights\best.onnx"
        self.test_images_dir = "legacy_assets/ceshi/calibration_gt/images"
        
        print(f"🔍 数据清洗影响测试器初始化")
        print(f"   - 模型: {self.model_path}")
        print(f"   - 测试图像: {self.test_images_dir}")
    
    def test_data_cleaning_impact(self, max_test_images: int = 10) -> Dict[str, Any]:
        """测试数据清洗影响"""
        print(f"🚀 开始测试数据清洗影响...")
        
        # 收集测试图像
        test_images = self._collect_test_images(max_test_images)
        print(f"📊 使用 {len(test_images)} 张图像进行测试")
        
        results = {
            'raw_yolo_results': [],
            'carddetector_no_cleaning': [],
            'carddetector_with_cleaning': [],
            'onnx_results': []
        }
        
        # 1. 测试原始YOLO输出
        print(f"\n🔬 测试1: 原始YOLO模型输出")
        results['raw_yolo_results'] = self._test_raw_yolo(test_images)
        
        # 2. 测试CardDetector不使用数据清洗
        print(f"\n🔬 测试2: CardDetector (无数据清洗)")
        results['carddetector_no_cleaning'] = self._test_carddetector_no_cleaning(test_images)
        
        # 3. 测试CardDetector使用数据清洗
        print(f"\n🔬 测试3: CardDetector (有数据清洗)")
        results['carddetector_with_cleaning'] = self._test_carddetector_with_cleaning(test_images)
        
        # 4. 测试ONNX模型
        print(f"\n🔬 测试4: ONNX模型输出")
        results['onnx_results'] = self._test_onnx_model(test_images)
        
        # 生成对比分析
        analysis = self._analyze_results(results)
        
        # 生成报告
        report = self._generate_report(results, analysis)
        
        print(f"✅ 数据清洗影响测试完成")
        return report
    
    def _collect_test_images(self, max_images: int) -> List[str]:
        """收集测试图像"""
        test_images = []
        
        for filename in sorted(os.listdir(self.test_images_dir))[:max_images]:
            if filename.lower().endswith(('.jpg', '.jpeg', '.png')):
                test_images.append(os.path.join(self.test_images_dir, filename))
        
        return test_images
    
    def _test_raw_yolo(self, test_images: List[str]) -> List[Dict[str, Any]]:
        """测试原始YOLO输出"""
        results = []
        model = YOLO(self.model_path)
        
        for image_path in test_images:
            image = cv2.imread(image_path)
            
            # 使用极低阈值获取所有可能的检测
            yolo_result = model(image, conf=0.01, iou=0.1, verbose=False)[0]
            
            detections = []
            if yolo_result.boxes is not None:
                for i, box in enumerate(yolo_result.boxes):
                    cls_id = int(box.cls[0])
                    conf = float(box.conf[0])
                    xyxy = box.xyxy[0].tolist()
                    
                    detections.append({
                        'cls_id': cls_id,
                        'confidence': conf,
                        'bbox_xyxy': xyxy
                    })
            
            results.append({
                'image': os.path.basename(image_path),
                'detection_count': len(detections),
                'detections': detections
            })
            
            print(f"   {os.path.basename(image_path)}: {len(detections)} 个检测")
        
        return results
    
    def _test_carddetector_no_cleaning(self, test_images: List[str]) -> List[Dict[str, Any]]:
        """测试CardDetector不使用数据清洗"""
        results = []
        
        # 创建不使用数据清洗的检测器
        detector = CardDetector(
            model_path=self.model_path,
            conf_threshold=0.01,
            iou_threshold=0.1,
            enable_validation=False  # 关闭数据清洗
        )
        
        for image_path in test_images:
            image = cv2.imread(image_path)
            detections = detector.detect_image(image)
            
            results.append({
                'image': os.path.basename(image_path),
                'detection_count': len(detections),
                'detections': detections
            })
            
            print(f"   {os.path.basename(image_path)}: {len(detections)} 个检测")
        
        return results
    
    def _test_carddetector_with_cleaning(self, test_images: List[str]) -> List[Dict[str, Any]]:
        """测试CardDetector使用数据清洗"""
        results = []
        
        # 创建使用数据清洗的检测器
        detector = CardDetector(
            model_path=self.model_path,
            conf_threshold=0.01,
            iou_threshold=0.1,
            enable_validation=True  # 启用数据清洗
        )
        
        for image_path in test_images:
            image = cv2.imread(image_path)
            detections = detector.detect_image(image)
            
            results.append({
                'image': os.path.basename(image_path),
                'detection_count': len(detections),
                'detections': detections
            })
            
            print(f"   {os.path.basename(image_path)}: {len(detections)} 个检测")
        
        return results
    
    def _test_onnx_model(self, test_images: List[str]) -> List[Dict[str, Any]]:
        """测试ONNX模型"""
        results = []
        
        if not os.path.exists(self.onnx_model_path):
            print(f"   ⚠️ ONNX模型不存在: {self.onnx_model_path}")
            return results
        
        try:
            model = YOLO(self.onnx_model_path)
            
            for image_path in test_images:
                image = cv2.imread(image_path)
                
                # 使用极低阈值
                onnx_result = model(image, conf=0.01, iou=0.1, verbose=False)[0]
                
                detections = []
                if onnx_result.boxes is not None:
                    for i, box in enumerate(onnx_result.boxes):
                        cls_id = int(box.cls[0])
                        conf = float(box.conf[0])
                        xyxy = box.xyxy[0].tolist()
                        
                        detections.append({
                            'cls_id': cls_id,
                            'confidence': conf,
                            'bbox_xyxy': xyxy
                        })
                
                results.append({
                    'image': os.path.basename(image_path),
                    'detection_count': len(detections),
                    'detections': detections
                })
                
                print(f"   {os.path.basename(image_path)}: {len(detections)} 个检测")
                
        except Exception as e:
            print(f"   ❌ ONNX测试失败: {e}")
            results.append({'error': str(e)})
        
        return results
    
    def _analyze_results(self, results: Dict[str, List]) -> Dict[str, Any]:
        """分析结果"""
        analysis = {}
        
        # 计算总检测数
        for test_name, test_results in results.items():
            if test_results and 'error' not in test_results[0]:
                total_detections = sum(r['detection_count'] for r in test_results)
                analysis[test_name] = {
                    'total_detections': total_detections,
                    'avg_detections_per_image': total_detections / len(test_results) if test_results else 0
                }
        
        # 计算数据清洗的影响
        if 'carddetector_no_cleaning' in analysis and 'carddetector_with_cleaning' in analysis:
            no_clean = analysis['carddetector_no_cleaning']['total_detections']
            with_clean = analysis['carddetector_with_cleaning']['total_detections']
            
            analysis['data_cleaning_impact'] = {
                'detections_without_cleaning': no_clean,
                'detections_with_cleaning': with_clean,
                'filtered_out': no_clean - with_clean,
                'filter_rate': (no_clean - with_clean) / no_clean * 100 if no_clean > 0 else 0
            }
        
        # 对比ONNX和PyTorch
        if 'raw_yolo_results' in analysis and 'onnx_results' in analysis:
            pytorch_total = analysis['raw_yolo_results']['total_detections']
            onnx_total = analysis['onnx_results']['total_detections']
            
            analysis['model_format_comparison'] = {
                'pytorch_detections': pytorch_total,
                'onnx_detections': onnx_total,
                'difference': onnx_total - pytorch_total,
                'difference_percentage': (onnx_total - pytorch_total) / pytorch_total * 100 if pytorch_total > 0 else 0
            }
        
        return analysis
    
    def _generate_report(self, results: Dict[str, List], analysis: Dict[str, Any]) -> Dict[str, Any]:
        """生成报告"""
        
        # 找出最可能的原因
        likely_causes = []
        
        # 检查数据清洗影响
        if 'data_cleaning_impact' in analysis:
            impact = analysis['data_cleaning_impact']
            if impact['filter_rate'] > 10:
                likely_causes.append({
                    'cause': '数据清洗过滤过多检测结果',
                    'evidence': f"过滤了 {impact['filtered_out']} 个检测 ({impact['filter_rate']:.1f}%)",
                    'recommendation': '关闭数据验证和清洗 (enable_validation=False)',
                    'severity': 'high' if impact['filter_rate'] > 30 else 'medium'
                })
        
        # 检查模型格式差异
        if 'model_format_comparison' in analysis:
            comp = analysis['model_format_comparison']
            if abs(comp['difference_percentage']) > 5:
                likely_causes.append({
                    'cause': 'ONNX vs PyTorch模型输出差异',
                    'evidence': f"检测数量差异: {comp['difference_percentage']:.1f}%",
                    'recommendation': '使用ONNX模型或检查模型转换',
                    'severity': 'medium'
                })
        
        report = {
            'test_summary': {
                'total_images_tested': len(results.get('raw_yolo_results', [])),
                'tests_performed': len(results),
                'likely_causes_found': len(likely_causes)
            },
            'detailed_results': results,
            'analysis': analysis,
            'likely_causes': likely_causes,
            'recommendations': [cause['recommendation'] for cause in likely_causes]
        }
        
        # 保存报告
        report_path = "output/data_cleaning_impact_report.json"
        os.makedirs(os.path.dirname(report_path), exist_ok=True)
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        return report


def main():
    """主函数"""
    print("🔍 数据清洗影响测试器")
    print("=" * 50)
    
    # 创建测试器
    tester = DataCleaningImpactTester()
    
    # 执行测试
    report = tester.test_data_cleaning_impact(max_test_images=10)
    
    # 打印结果
    print("\n📊 测试结果汇总:")
    summary = report['test_summary']
    print(f"   测试图像数: {summary['total_images_tested']}")
    print(f"   执行测试数: {summary['tests_performed']}")
    print(f"   发现可能原因: {summary['likely_causes_found']}")
    
    print("\n📈 检测数量对比:")
    analysis = report['analysis']
    for test_name, test_analysis in analysis.items():
        if 'total_detections' in test_analysis:
            print(f"   {test_name}: {test_analysis['total_detections']} 个检测")
    
    if 'data_cleaning_impact' in analysis:
        impact = analysis['data_cleaning_impact']
        print(f"\n🔍 数据清洗影响:")
        print(f"   清洗前: {impact['detections_without_cleaning']} 个检测")
        print(f"   清洗后: {impact['detections_with_cleaning']} 个检测")
        print(f"   过滤掉: {impact['filtered_out']} 个检测 ({impact['filter_rate']:.1f}%)")
    
    if report['likely_causes']:
        print(f"\n🎯 可能的原因:")
        for i, cause in enumerate(report['likely_causes'], 1):
            print(f"   {i}. {cause['cause']}")
            print(f"      证据: {cause['evidence']}")
            print(f"      建议: {cause['recommendation']}")
            print(f"      严重程度: {cause['severity']}")
    
    print(f"\n📁 详细报告: output/data_cleaning_impact_report.json")


if __name__ == "__main__":
    main()
