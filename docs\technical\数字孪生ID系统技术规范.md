@ -0,0 +1,580 @@
# 数字孪生ID系统技术规范

## 📋 文档信息
- **版本**：v4.0
- **更新日期**：2025-07-30
- **适用范围**：数字孪生ID统一主控器系统
- **状态**：正式发布
- **更新内容**：基于深度架构分析的完整技术规范更新

## 🎯 技术规范概述

本文档定义了数字孪生ID统一主控器系统的完整技术规范，包括系统架构、模块职责、数据流分析、技术实现细节、集成接口规范等。基于对 `digital_twin_controller.py` 为核心的模块化架构深度分析，结合 `GAME_RULES.md` 的业务逻辑要求。

## 🏗️ 系统架构规范

### 核心架构层次图
```
数字孪生ID系统 (Digital Twin ID System)
│
├── 🎮 统一主控器层 (Controller Layer)
│   └── DigitalTwinController
│       ├── ProcessingStrategy (策略选择)
│       ├── CardSizeActivationController (尺寸启动控制)
│       └── PerformanceMonitor (性能监控)
│
├── 🔧 集成器层 (Integrator Layer)
│   ├── Phase1Integrator (基础功能集成器)
│   │   ├── DataValidator → SimpleInheritor → BasicIDAssigner
│   │   └── 适用场景：简单场景、高性能要求
│   │
│   └── Phase2Integrator (完整功能集成器) ⭐
│       ├── 数据验证 → 虚拟区域处理 → 简单继承 → 区域2互斥
│       ├── 区域流转 → 暗牌处理 → ID分配 → 空间排序
│       ├── 遮挡补偿 → 第21张牌跟踪 → 游戏边界检测
│       └── 适用场景：生产环境、复杂场景
│
├── 🧩 功能模块层 (Module Layer)
│   ├── 核心处理模块
│   │   ├── DataValidator (数据验证器)
│   │   ├── SimpleInheritor (简单继承器)
│   │   ├── BasicIDAssigner (基础ID分配器)
│   │   └── GlobalIDManager (全局ID管理器)
│   │
│   ├── 高级处理模块
│   │   ├── VirtualRegionProcessor (虚拟区域处理器)
│   │   ├── Region2Processor (区域2处理器)
│   │   ├── RegionTransitioner (区域流转器)
│   │   ├── DarkCardProcessor (暗牌处理器)
│   │   ├── SpatialSorter (空间排序器)
│   │   └── OcclusionCompensator (遮挡补偿器)
│   │
│   └── 专业化模块
│       ├── Card21Tracker (第21张牌跟踪器)
│       └── GameBoundaryDetector (游戏边界检测器)
│
└── 🔌 测试工具层 (Testing Tool Layer)
    └── CalibrationGTFinalProcessor (标定数据处理工具)
        ├── 定位：测试和验证工具
        ├── 功能：批量处理标注数据
        └── 集成：使用Phase2Integrator进行处理
```

### 设计原则
1. **统一入口**：所有数字孪生功能通过DigitalTwinController访问
2. **分层架构**：控制器→集成器→模块的清晰层次结构
3. **策略模式**：支持Phase1/Phase2策略动态切换
4. **模块解耦**：各功能模块独立，通过标准接口通信
5. **状态管理**：支持跨帧状态保持和继承机制
6. **配置集中**：统一的配置管理和性能监控

## 📊 模块职责与接口规范

### 1. 统一主控器 (DigitalTwinController)

**核心职责：**
- 提供统一的数字孪生处理入口
- 管理处理策略的动态切换
- 协调各集成器的工作流程
- 监控系统性能和状态

**主要接口：**
```python
class DigitalTwinController:
    def process_frame(self, detections: List[Dict], strategy: Optional[ProcessingStrategy] = None) -> ProcessingResult
    def switch_strategy(self, new_strategy: ProcessingStrategy) -> None
    def get_system_status(self) -> Dict[str, Any]
    def get_performance_stats(self) -> Dict[str, Any]
    def reset_system(self) -> None
```

**关键特性：**
- 支持PHASE1_BASIC和PHASE2_COMPLETE策略
- 集成CardSizeActivationController进行智能启动控制
- 提供性能监控和统计功能
- 支持配置驱动的模块开关控制

### 2. 第二阶段集成器 (Phase2Integrator) ⭐

**核心职责：**
- 集成9个功能模块实现完整数字孪生功能
- 管理复杂的数据处理流水线
- 维护跨帧状态和继承关系
- 提供生产级的处理能力

**处理流程：**
```
输入检测数据
    ↓
1. 数据验证 (DataValidator)
    ↓
2. 虚拟区域处理 (VirtualRegionProcessor)
    ↓
3. 区域2互斥处理 (Region2Processor)
    ↓
4. 简单继承 (SimpleInheritor)
    ↓
5. 区域流转 (RegionTransitioner)
    ↓
6. 暗牌处理 (DarkCardProcessor)
    ↓
7. ID分配 (BasicIDAssigner)
    ↓
8. 空间排序 (SpatialSorter)
    ↓
9. 遮挡补偿 (OcclusionCompensator)
    ↓
10. 第21张牌跟踪 (Card21Tracker)
    ↓
11. 游戏边界检测 (GameBoundaryDetector)
    ↓
输出处理结果
```

**关键特性：**
- 维护GlobalIDManager确保ID全局唯一性
- 支持跨帧状态保持（继承器、补偿器等）
- 提供详细的处理统计和调试信息
- 集成游戏边界检测和自动重置机制

### 3. 核心功能模块详解

#### 3.1 数据验证器 (DataValidator)
**职责：** 验证和清理输入数据
**接口：** `validate_data(detections: List[Dict]) -> ValidationResult`
**功能：**
- 边界框格式验证 [x1, y1, x2, y2]
- 置信度范围检查 [0.0, 1.0]
- 标签有效性验证（21个有效标签）
- 区域ID合法性检查 [1-17]

#### 3.2 简单继承器 (SimpleInheritor)
**职责：** 基于区域+标签的ID继承机制
**接口：** `process_inheritance(current_cards: List[Dict]) -> InheritanceResult`
**核心算法：**
```python
# 继承匹配条件
key = (card['group_id'], card['label'])
if key in previous_frame_mapping:
    # 执行内容替换继承
    inherited_card = inherit_card_content_replacement(current_card, previous_card)
```
**关键特性：**
- 基于(区域ID, 标签)的精确匹配
- 内容替换继承：保持ID不变，更新位置信息
- 维护previous_frame_mapping支持跨帧继承

#### 3.3 基础ID分配器 (BasicIDAssigner)
**职责：** 为新卡牌分配数字孪生ID
**接口：** `assign_ids(cards: List[Dict], inherited_cards: List[Dict]) -> IDAssignmentResult`
**核心组件：**
- **GlobalIDManager：** 全局ID注册表和计数器管理
- **ID分配策略：** 物理ID优先，虚拟ID备用
- **空间排序：** 按区域和空间位置排序分配

**ID格式规范：**
```python
# 物理ID：{序号}{牌面}
"1二", "2三", "3四", "4五"

# 虚拟ID：虚拟{牌面}
"虚拟二", "虚拟三"

# 暗牌ID：{序号}{牌面}暗
"1二暗", "2三暗"
```

#### 3.4 区域流转器 (RegionTransitioner)
**职责：** 处理卡牌跨区域流转
**接口：** `process_transitions(current_cards: List[Dict], previous_cards: List[Dict]) -> TransitionResult`
**流转路径定义：**
```python
transition_paths = {
    # 观战方流转路径
    1: [2, 6, 14, 15],      # 手牌 → 调整/吃碰区/赢方区域
    2: [1, 4],              # 调整 → 手牌/打牌
    3: [1, 5, 6, 16],       # 抓牌 → 手牌/弃牌/吃碰区
    4: [5, 6, 16],          # 打牌 → 弃牌/吃碰区

    # 对战方流转路径
    7: [6, 16, 9],          # 抓牌 → 吃碰区/弃牌
    8: [6, 16, 9],          # 打牌 → 吃碰区/弃牌
    9: [11],                # 弃牌 → 最终弃牌
}
```

#### 3.5 暗牌处理器 (DarkCardProcessor)
**职责：** 处理暗牌的身份推断和关联
**接口：** `process_dark_cards(cards: List[Dict]) -> DarkCardResult`
**处理逻辑：**
- **空间分列：** 按X坐标对明暗牌进行列分组
- **序号分配：** 每列内暗牌从下到上分配1、2、3序号
- **类别关联：** 从同列明牌获取卡牌类别
- **ID生成：** 生成格式为"{序号}{类别}暗"的ID

#### 3.6 遮挡补偿器 (OcclusionCompensator)
**职责：** 补偿被遮挡或暂时消失的卡牌
**接口：** `process_compensation(current_cards: List[Dict]) -> CompensationResult`
**补偿策略：**
```python
compensation_config = {
    "max_compensation_per_card": 3,    # 每张牌最多补偿3次
    "max_total_cards": 80,             # 总卡牌数限制
    "no_compensation_regions": {3, 5, 10, 11, 12},  # 不需要补偿的区域
    "stable_regions": {6, 14, 15, 16}  # 优先补偿的稳定区域
}
```

## 📊 数据格式规范

### 输入数据格式
```python
# 检测结果标准格式
DetectionInput = {
    'label': str,           # 牌面标签，如：'二', '三', '四', '暗'
    'bbox': List[float],    # 边界框 [x1, y1, x2, y2]
    'confidence': float,    # 置信度 [0.0, 1.0]
    'group_id': int        # 区域ID，标识卡牌所在区域
}
```

### 输出数据格式
```python
@dataclass
class DigitalTwinCard:
    """数字孪生卡牌标准数据结构"""
    # 基础属性
    label: str              # 牌面标签
    bbox: List[float]       # 边界框
    confidence: float       # 置信度
    group_id: int          # 区域ID

    # 数字孪生属性
    twin_id: str           # 数字孪生ID（如：1二、2二、虚拟二）
    is_virtual: bool       # 是否为虚拟牌
    is_dark: bool          # 是否为暗牌
    sequence_number: int   # 序号 [1, 2, 3, 4]

    # 状态属性
    inherited: bool        # 是否继承自前一帧
    region_transitioned: bool  # 是否发生区域流转
    compensated: bool      # 是否为补偿卡牌

    # 元数据
    frame_id: Optional[str]    # 帧ID
    timestamp: Optional[float] # 时间戳
```

## � 数据流分析

### 系统数据流转图
```
外部检测数据 (Raw Detections)
    ↓
[DigitalTwinController] 统一入口
    ↓
[CardSizeActivationController] 尺寸启动控制
    ↓ (满足启动条件)
[Phase2Integrator] 完整功能处理
    ↓
┌─────────────────────────────────────────────────────────────┐
│                    Phase2 处理流水线                          │
├─────────────────────────────────────────────────────────────┤
│ 1. [DataValidator] 数据验证                                  │
│    输入: 原始检测数据                                         │
│    输出: 清理后的有效数据                                     │
│    ↓                                                        │
│ 2. [VirtualRegionProcessor] 虚拟区域处理                     │
│    输入: 有效检测数据                                         │
│    输出: 物理卡牌 + 虚拟卡牌                                  │
│    ↓                                                        │
│ 3. [Region2Processor] 区域2互斥处理                          │
│    输入: 物理卡牌                                            │
│    输出: 互斥处理后的卡牌                                     │
│    ↓                                                        │
│ 4. [SimpleInheritor] 简单继承                               │
│    输入: 当前帧卡牌 + 前一帧映射                              │
│    输出: 继承卡牌 + 新卡牌                                    │
│    ↓                                                        │
│ 5. [RegionTransitioner] 区域流转                            │
│    输入: 继承结果 + 前一帧数据                                │
│    输出: 流转卡牌 + 新卡牌                                    │
│    ↓                                                        │
│ 6. [DarkCardProcessor] 暗牌处理                              │
│    输入: 流转结果                                            │
│    输出: 暗牌关联后的卡牌                                     │
│    ↓                                                        │
│ 7. [BasicIDAssigner] ID分配                                 │
│    输入: 新卡牌 + 已继承卡牌                                  │
│    输出: 完整ID分配的卡牌                                     │
│    ↓                                                        │
│ 8. [SpatialSorter] 空间排序                                 │
│    输入: ID分配后的卡牌                                       │
│    输出: 空间排序后的卡牌                                     │
│    ↓                                                        │
│ 9. [OcclusionCompensator] 遮挡补偿                           │
│    输入: 排序后的卡牌 + 前一帧记录                            │
│    输出: 补偿后的完整卡牌列表                                 │
│    ↓                                                        │
│ 10. [Card21Tracker] 第21张牌跟踪                            │
│     输入: 补偿后的卡牌                                       │
│     输出: 第21张牌标记的卡牌                                  │
│     ↓                                                       │
│ 11. [GameBoundaryDetector] 游戏边界检测                      │
│     输入: 最终卡牌列表                                       │
│     输出: 边界检测结果 + 重置信号                             │
└─────────────────────────────────────────────────────────────┘
    ↓
[ProcessingResult] 统一结果格式
    ↓
外部调用方 (如CalibrationGTFinalProcessor)
```

### 关键数据结构流转

#### 1. 继承机制数据流
```python
# SimpleInheritor内部数据流
previous_frame_mapping: Dict[(group_id, label), card_data]
    ↓ (帧间匹配)
current_cards → 匹配检查 → inherited_cards + new_cards
    ↓ (更新映射)
updated_previous_frame_mapping
```

#### 2. ID分配数据流
```python
# GlobalIDManager状态管理
global_id_registry: Dict[twin_id, card_info]
id_counters: Dict[label, count]
    ↓ (ID分配)
new_cards → ID可用性检查 → 物理ID/虚拟ID分配
    ↓ (注册更新)
updated_global_id_registry
```

#### 3. 区域流转数据流
```python
# RegionTransitioner流转逻辑
transition_paths: Dict[source_region, List[target_regions]]
previous_cards_by_id: Dict[twin_id, card_data]
    ↓ (流转检测)
current_cards → 流转匹配 → transitioned_cards + new_cards
```

## 🔧 技术实现细节

### 1. 帧间状态管理机制

**问题背景：**
基于前期分析发现的frame_00257.jpg输出错误，根本原因是帧间状态管理缺陷。

**解决方案：**
```python
class Phase2Integrator:
    def __init__(self):
        # 关键：维护跨帧状态的模块实例
        self.inheritor = create_simple_inheritor()  # 保持previous_frame_mapping
        self.occlusion_compensator = create_occlusion_compensator()  # 保持previous_frame_cards
        self.card21_tracker = create_card21_tracker()  # 保持跨帧跟踪状态

    def process_frame(self, detections):
        # 每帧处理时，模块实例保持不变，状态得以延续
        inheritance_result = self.inheritor.process_inheritance(cards)
        # ... 其他处理
```

**关键特性：**
- **状态持久化：** 模块实例在整个处理过程中保持不变
- **映射维护：** SimpleInheritor维护previous_frame_mapping
- **历史记录：** OcclusionCompensator维护compensation_history
- **跨帧跟踪：** Card21Tracker维护牌局状态

### 2. ID分配算法详解

**核心算法：**
```python
def assign_physical_id(self, card, label):
    # 1. 检查可用ID
    available_id = self.global_id_manager.get_next_available_id(label)

    if available_id:
        # 2. 分配物理ID
        card['twin_id'] = available_id  # 格式：1二、2三、3四、4五
        card['is_virtual'] = False
        card['sequence_number'] = int(available_id[0])

        # 3. 注册到全局管理器
        self.global_id_manager.register_id(available_id, card)
    else:
        # 4. 分配虚拟ID
        virtual_id = f"虚拟{label}"
        card['twin_id'] = virtual_id
        card['is_virtual'] = True
```

**ID冲突解决：**
```python
# 允许特定区域对的ID共存
allowed_cross_region_pairs = [(3, 16), (4, 16)]

def can_coexist(self, twin_id, new_region, existing_region):
    return (new_region, existing_region) in self.allowed_cross_region_pairs
```

### 3. 区域流转规则引擎

**流转路径定义：**
```python
transition_paths = {
    # 观战方完整流转链
    1: [2, 6, 14, 15],      # 手牌 → 调整/吃碰区/赢方区域
    2: [1, 4],              # 调整 ↔ 手牌 → 打牌
    3: [1, 5, 6, 16],       # 抓牌 → 手牌/弃牌/吃碰区
    4: [5, 6, 16],          # 打牌 → 弃牌/吃碰区

    # 对战方完整流转链
    7: [6, 16, 9],          # 抓牌 → 吃碰区/弃牌
    8: [6, 16, 9],          # 打牌 → 吃碰区/弃牌
    9: [11],                # 弃牌 → 最终弃牌
}
```

**特殊流转处理：**
```python
def _handle_special_7_to_9_transition(self, current_cards, previous_cards):
    """处理7→9区域流转（对战方抓牌区→弃牌区）"""
    # 这是frame_00257.jpg错误的关键修复点
    for current_card in current_cards:
        if current_card['group_id'] == 9:  # 当前在区域9
            # 查找前一帧区域7中相同标签的卡牌
            for prev_card in previous_cards:
                if (prev_card['group_id'] == 7 and
                    prev_card['label'] == current_card['label']):
                    # 执行ID继承
                    current_card['twin_id'] = prev_card['twin_id']
                    current_card['inherited'] = True
                    current_card['region_transitioned'] = True
```

## � 集成接口规范

### 1. 统一主控器接口

**核心接口：**
```python
class DigitalTwinController:
    """数字孪生统一主控器接口"""

    def process_frame(self,
                     detections: List[Dict[str, Any]],
                     strategy: Optional[ProcessingStrategy] = None) -> ProcessingResult:
        """
        处理单帧数据 - 统一入口

        Args:
            detections: 检测结果列表
            strategy: 处理策略，如果为None则使用默认策略

        Returns:
            处理结果
        """
        pass

    def switch_strategy(self, new_strategy: ProcessingStrategy) -> None:
        """切换处理策略"""
        pass

    def get_system_status(self) -> Dict[str, Any]:
        """获取系统状态信息"""
        pass

    def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计信息"""
        pass

    def reset_system(self) -> None:
        """重置系统状态（新局开始时调用）"""
        pass
```

**工厂函数接口：**
```python
def create_digital_twin_controller(config: Optional[DigitalTwinConfig] = None) -> DigitalTwinController:
    """创建数字孪生主控器的工厂函数"""
    pass

def create_default_controller() -> DigitalTwinController:
    """创建使用默认配置的数字孪生主控器"""
    pass

def create_complete_controller() -> DigitalTwinController:
    """创建使用完整功能的数字孪生主控器"""
    pass

def create_controller_with_size_control(size_threshold: float = 0.85,
                                       qualified_ratio_threshold: float = 0.9,
                                       min_card_count: int = 20) -> DigitalTwinController:
    """创建带有卡牌尺寸启动控制的数字孪生主控器"""
    pass
```

### 2. 配置接口规范

```python
@dataclass
class DigitalTwinConfig:
    """数字孪生系统配置"""
    strategy: ProcessingStrategy = ProcessingStrategy.PHASE2_COMPLETE
    enable_logging: bool = True
    log_level: str = "INFO"
    performance_monitoring: bool = True

    # 模块开关
    enable_inheritance: bool = True
    enable_region_transition: bool = True
    enable_dark_card_processing: bool = True
    enable_occlusion_compensation: bool = True

    # 卡牌尺寸启动控制配置
    enable_size_activation_control: bool = True
    size_threshold: float = 0.85
    qualified_ratio_threshold: float = 0.9
    min_card_count: int = 10

    # 性能配置
    max_cards_per_frame: int = 50
    max_virtual_cards: int = 20

    # 输出配置
    dual_output_enabled: bool = True
    preserve_original_data: bool = True
```

### 3. 处理结果接口

```python
@dataclass
class ProcessingResult:
    """处理结果统一格式"""
    success: bool
    processed_cards: List[Dict[str, Any]]
    statistics: Dict[str, Any]
    validation_errors: Optional[List[str]] = None
    validation_warnings: Optional[List[str]] = None
    processing_time: float = 0.0
    strategy_used: str = ""

    # 统计信息结构
    statistics = {
        'total_cards': int,           # 总卡牌数
        'inherited_cards': int,       # 继承卡牌数
        'new_cards': int,            # 新卡牌数
        'virtual_cards': int,        # 虚拟卡牌数
        'dark_cards': int,           # 暗牌数
        'compensated_cards': int,    # 补偿卡牌数
        'inheritance_rate': float,   # 继承率
        'processing_time': float     # 处理时间（毫秒）
    }
```

### 4. 集成示例

#### 4.1 基础集成示例
```python
from src.core.digital_twin_controller import create_complete_controller

# 创建数字孪生主控器
controller = create_complete_controller()

# 处理单帧数据
detections = [
    {'label': '二', 'bbox': [100, 100, 200, 200], 'confidence': 0.9, 'group_id': 1},
    {'label': '三', 'bbox': [300, 100, 400, 200], 'confidence': 0.8, 'group_id': 1}
]

result = controller.process_frame(detections)

# 检查处理结果
if result.success:
    print(f"处理成功，共{len(result.processed_cards)}张卡牌")
    print(f"继承率：{result.statistics['inheritance_rate']:.2%}")
else:
    print(f"处理失败：{result.validation_errors}")
```

#### 4.2 测试工具集成示例（CalibrationGTFinalProcessor）
```python
class CalibrationGTFinalProcessor:
    def __init__(self, config: FinalProcessorConfig):
        # 🔧 关键：使用Phase2Integrator保持跨帧状态
        self.digital_twin_controller = Phase2Integrator()

    def process_frame(self, frame_file, frame_name):
        # 转换标注数据为检测格式
        card_detections = self._convert_shapes_to_detections(card_shapes)
        all_detections = self._convert_all_shapes_to_detections(original_data.get("shapes", []))

        # 使用数字孪生主控器处理
        dt_result = self.digital_twin_controller.process_frame(all_detections)

        # 生成双轨输出
        dual_result = self._generate_dual_format_final(
            dt_result, original_data, card_shapes, non_card_shapes
        )
```

### 5. 模块接口标准

```python
class BaseModule(ABC):
    """模块基类，定义标准接口"""

    @abstractmethod
    def process(self, input_data: Any) -> Any:
        """处理输入数据，返回处理结果"""
        pass

    def get_statistics(self) -> Dict[str, Any]:
        """获取模块统计信息"""
        return {}

    def reset(self):
        """重置模块状态"""
        pass

    def get_status(self) -> Dict[str, Any]:
        """获取模块状态"""
        return {"status": "active"}
```

## 📏 ID分配规范

### ID格式标准
```python
# 物理ID格式：{序号}{牌面}
PHYSICAL_ID_FORMAT = r"^[1-4][二三四五六七八九十壹]$"
# 示例：1二, 2三, 3四, 4五

# 虚拟ID格式：虚拟{牌面}
VIRTUAL_ID_FORMAT = r"^虚拟[二三四五六七八九十壹]$"
# 示例：虚拟二, 虚拟三

# 暗牌ID格式：{序号}{牌面}暗
DARK_ID_FORMAT = r"^[1-4][二三四五六七八九十壹]暗$"
# 示例：1二暗, 2三暗
```

### ID分配规则
1. **物理ID优先**：每种牌面最多分配4个物理ID
2. **虚拟ID补充**：超过4张时分配虚拟ID
3. **暗牌关联**：暗牌关联到具体牌面（1暗 → 1二暗）
4. **ID稳定性**：一经分配的ID永不改变

### 计数器管理
```python
# ID计数器结构
id_counters = {
    '二': 2,    # 当前已分配2个物理ID
    '三': 1,    # 当前已分配1个物理ID
    '四': 4,    # 已达到物理ID上限
    # ... 其他牌面
}

# 虚拟ID计数器
virtual_counters = {
    '二': 1,    # 当前有1个虚拟ID
    '四': 2,    # 当前有2个虚拟ID
    # ... 其他牌面
}
```

## 🎯 卡牌尺寸启动控制规范 🆕

### 启动控制接口
```python
class CardSizeActivationController:
    """卡牌尺寸启动控制器接口"""

    def should_activate_digital_twin(self, detections: List[Dict]) -> ActivationDecision:
        """判断是否应该启动数字孪生功能"""
        pass

    def get_statistics(self) -> Dict[str, Any]:
        """获取启动控制统计信息"""
        pass
```

### 启动决策数据结构
```python
@dataclass
class ActivationDecision:
    """启动决策结果"""
    should_activate: bool           # 是否应该启动数字孪生
    qualified_ratio: float          # 尺寸合格率
    card_count: int                # 观战方手牌数量
    reason: str                    # 决策原因
    preserve_data: bool            # 是否保留原始数据
    size_analysis: Optional[Dict] = None     # 尺寸分析详情
    baseline_info: Optional[Dict] = None     # 基准信息
    timestamp: Optional[float] = None        # 决策时间戳
```

### 配置规范
```python
@dataclass
class CardSizeActivationConfig:
    """卡牌尺寸启动控制配置"""
    size_threshold: float = 0.85           # 尺寸阈值
    qualified_ratio_threshold: float = 0.9 # 合格卡牌比例阈值
    min_card_count: int = 20               # 最少卡牌数
    baseline_cache_enabled: bool = True    # 缓存基准数据
    enable_size_logging: bool = True       # 启用尺寸日志
```

### 启动条件规范
1. **卡牌数量要求**：观战方手牌区(group_id=1)卡牌数量 ≥ 20张
2. **尺寸质量要求**：尺寸合格率 ≥ 90%（基于0.85尺寸阈值）
3. **数据有效性**：自动过滤UI元素和无效标签
4. **基准自适应**：从现有JSON文件自动学习尺寸基准

### 处理模式规范
```python
# 启动模式：条件满足时的处理流程
ACTIVATION_MODE = {
    'digital_twin_enabled': True,
    'processing_mode': 'full_pipeline',
    'data_preservation': False,
}

# 保留模式：条件不满足时的处理流程
PRESERVATION_MODE = {
    'digital_twin_enabled': False,
    'processing_mode': 'passthrough',
    'data_preservation': True,
}
```

## ⚡ 性能规范

### 时间复杂度要求
- **单模块处理**：O(n)，n为卡牌数量
- **系统整体处理**：O(n)，线性时间复杂度
- **内存复杂度**：O(k+m)，k为区域-标签组合数，m为牌面类型数

### 性能指标要求
```python
PERFORMANCE_REQUIREMENTS = {
    'processing_time_per_frame': 10,      # 每帧处理时间 < 10ms
    'memory_usage': 50,                   # 内存使用 < 50MB
    'inheritance_rate': 0.90,             # 继承率 > 90%
    'id_assignment_accuracy': 0.95,       # ID分配准确率 > 95%
    'system_availability': 0.999,         # 系统可用性 > 99.9%
}
```

### 质量指标
```python
QUALITY_METRICS = {
    'code_coverage': 0.90,                # 代码覆盖率 > 90%
    'unit_test_pass_rate': 1.0,          # 单元测试通过率 = 100%
    'integration_test_pass_rate': 1.0,    # 集成测试通过率 = 100%
    'documentation_coverage': 0.95,       # 文档覆盖率 > 95%
}
```

## 🔒 安全规范

### 数据验证
```python
# 输入数据验证规则
VALIDATION_RULES = {
    'label': {
        'type': str,
        'allowed_values': ['二', '三', '四', '五', '六', '七', '八', '九', '十', '壹', '暗'],
        'required': True
    },
    'bbox': {
        'type': list,
        'length': 4,
        'element_type': float,
        'required': True
    },
    'confidence': {
        'type': float,
        'range': [0.0, 1.0],
        'required': True
    },
    'group_id': {
        'type': int,
        'range': [1, 10],
        'required': True
    }
}
```

### 错误处理
```python
# 标准错误类型
class DigitalTwinError(Exception):
    """数字孪生系统基础错误"""
    pass

class ValidationError(DigitalTwinError):
    """数据验证错误"""
    pass

class ProcessingError(DigitalTwinError):
    """处理过程错误"""
    pass

class SystemError(DigitalTwinError):
    """系统级错误"""
    pass
```

## 📊 监控规范

### 系统状态监控
```python
# 系统状态结构
system_status = {
    'frame_count': int,           # 处理帧数
    'inheritance_rate': float,    # 继承率
    'processing_speed': float,    # 处理速度（帧/秒）
    'memory_usage': float,        # 内存使用（MB）
    'error_count': int,          # 错误计数
    'warning_count': int,        # 警告计数
    'uptime': float,             # 运行时间（秒）
    'last_reset_time': str,      # 最后重置时间
}
```

### 性能监控
```python
# 性能指标监控
performance_metrics = {
    'avg_processing_time': float,     # 平均处理时间
    'max_processing_time': float,     # 最大处理时间
    'min_processing_time': float,     # 最小处理时间
    'throughput': float,              # 吞吐量（帧/秒）
    'success_rate': float,            # 成功率
    'error_rate': float,              # 错误率
}
```

## 🧪 测试规范

### 单元测试要求
```python
# 测试覆盖率要求
TEST_COVERAGE_REQUIREMENTS = {
    'line_coverage': 0.90,        # 行覆盖率 > 90%
    'branch_coverage': 0.85,      # 分支覆盖率 > 85%
    'function_coverage': 0.95,    # 函数覆盖率 > 95%
}

# 测试用例要求
TEST_CASE_REQUIREMENTS = {
    'normal_cases': True,         # 正常情况测试
    'edge_cases': True,           # 边界情况测试
    'error_cases': True,          # 错误情况测试
    'performance_tests': True,    # 性能测试
}
```

### 集成测试要求
```python
# 集成测试场景
INTEGRATION_TEST_SCENARIOS = [
    'single_card_processing',     # 单卡处理
    'multiple_cards_processing',  # 多卡处理
    'inheritance_scenarios',      # 继承场景
    'region_transition_scenarios', # 区域流转场景
    'dark_card_scenarios',        # 暗牌处理场景
    'occlusion_scenarios',        # 遮挡补偿场景
    'system_reset_scenarios',     # 系统重置场景
    'size_activation_scenarios',  # 卡牌尺寸启动控制场景 🆕
]
```

## 📚 文档规范

### 代码文档要求
```python
# 函数文档标准
def process_frame(self, detections: List[Dict]) -> ProcessingResult:
    """
    处理单帧检测数据
    
    Args:
        detections: 检测结果列表，每个元素包含label, bbox, confidence, group_id
        
    Returns:
        ProcessingResult: 包含处理后的卡牌列表和统计信息
        
    Raises:
        ValidationError: 输入数据验证失败
        ProcessingError: 处理过程中发生错误
        
    Example:
        >>> system = create_phase2_integrator()
        >>> detections = [{'label': '二', 'bbox': [0,0,100,100], 'confidence': 0.9, 'group_id': 1}]
        >>> result = system.process_frame(detections)
        >>> print(len(result.processed_cards))
        1
    """
    pass
```

### API文档要求
- **完整性**：覆盖所有公开接口
- **准确性**：与实际实现保持一致
- **示例性**：提供清晰的使用示例
- **时效性**：及时更新文档内容

## 🔄 版本控制规范

### 版本号规范
- **格式**：MAJOR.MINOR.PATCH
- **MAJOR**：不兼容的API修改
- **MINOR**：向后兼容的功能性新增
- **PATCH**：向后兼容的问题修正

### 变更日志规范
```markdown
## [2.0.0] - 2025-07-21
### Added
- 数字孪生ID模块化系统第二阶段功能
- 区域流转器模块
- 暗牌处理器模块
- 遮挡补偿器模块

### Changed
- 重构系统架构为模块化设计
- 优化ID分配算法

### Removed
- 删除老版本memory_manager依赖
- 清理过时的测试文件
```

## � 未来集成路线图

### 1. 项目架构集成方案

**当前状态：**
- `digital_twin_controller.py` 作为统一主控器已完成
- `Phase2Integrator` 作为核心处理引擎已稳定
- `CalibrationGTFinalProcessor` 作为测试工具验证系统功能

**集成步骤：**

#### 阶段1：核心集成（已完成）
```python
# 当前集成方式
from src.modules.phase2_integrator import Phase2Integrator

class YourApplication:
    def __init__(self):
        self.digital_twin_system = Phase2Integrator()

    def process_detection_results(self, detections):
        return self.digital_twin_system.process_frame(detections)
```

#### 阶段2：统一主控器集成（推荐）
```python
# 推荐的集成方式
from src.core.digital_twin_controller import create_complete_controller

class YourApplication:
    def __init__(self):
        self.digital_twin_controller = create_complete_controller()

    def process_detection_results(self, detections):
        return self.digital_twin_controller.process_frame(detections)
```

#### 阶段3：高级功能集成
```python
# 带有尺寸启动控制的集成
from src.core.digital_twin_controller import create_controller_with_size_control

class ProductionApplication:
    def __init__(self):
        self.controller = create_controller_with_size_control(
            size_threshold=0.85,
            qualified_ratio_threshold=0.9,
            min_card_count=20
        )

    def process_frame_with_quality_control(self, detections):
        result = self.controller.process_frame(detections)

        # 检查处理质量
        if result.statistics['inheritance_rate'] < 0.8:
            self.handle_low_quality_frame(result)

        return result
```

### 2. 关键技术问题解决方案

#### 2.1 帧间状态管理问题
**问题：** CalibrationGTFinalProcessor逐帧独立处理导致状态丢失
**解决方案：**
```python
# ❌ 错误方式：每帧创建新实例
def process_frame(self, frame_data):
    processor = Phase2Integrator()  # 状态丢失
    return processor.process_frame(frame_data)

# ✅ 正确方式：保持实例状态
class FrameProcessor:
    def __init__(self):
        self.digital_twin = Phase2Integrator()  # 状态保持

    def process_frame(self, frame_data):
        return self.digital_twin.process_frame(frame_data)
```

#### 2.2 ID继承机制优化
**当前机制：** 基于(区域ID, 标签)的精确匹配
**优化方向：**
- 增加空间位置辅助匹配
- 支持模糊匹配容错机制
- 优化区域流转检测算法

#### 2.3 性能优化建议
```python
# 性能监控集成
def monitor_performance(self):
    stats = self.controller.get_performance_stats()

    if stats['avg_processing_time'] > 10:  # 超过10ms
        self.optimize_processing_strategy()

    if stats['memory_usage'] > 50:  # 超过50MB
        self.trigger_garbage_collection()
```

### 3. 测试和验证策略

#### 3.1 单元测试覆盖
```python
# 关键模块测试
def test_simple_inheritor():
    inheritor = create_simple_inheritor()

    # 测试继承机制
    result = inheritor.process_inheritance(current_cards)
    assert result.inheritance_rate > 0.9

def test_id_assignment():
    assigner = create_basic_id_assigner()

    # 测试ID分配
    result = assigner.assign_ids(new_cards)
    assert all(card.get('twin_id') for card in result.assigned_cards)
```

#### 3.2 集成测试场景
- **场景1：** 完整牌局流程测试
- **场景2：** 边界条件处理测试
- **场景3：** 性能压力测试
- **场景4：** 错误恢复测试

### 4. 部署和运维指南

#### 4.1 配置管理
```python
# 生产环境配置
production_config = DigitalTwinConfig(
    strategy=ProcessingStrategy.PHASE2_COMPLETE,
    enable_logging=True,
    log_level="INFO",
    performance_monitoring=True,

    # 启用所有功能模块
    enable_inheritance=True,
    enable_region_transition=True,
    enable_dark_card_processing=True,
    enable_occlusion_compensation=True,

    # 性能优化配置
    max_cards_per_frame=50,
    max_virtual_cards=20
)
```

#### 4.2 监控指标
```python
# 关键监控指标
MONITORING_METRICS = {
    'processing_time': 'avg < 10ms',
    'inheritance_rate': 'avg > 90%',
    'memory_usage': 'max < 50MB',
    'error_rate': 'rate < 1%',
    'system_availability': 'uptime > 99.9%'
}
```

## �📝 总结

本技术规范基于对数字孪生ID系统的深度架构分析，提供了完整的技术标准和集成指南：

### ✅ 核心成果
1. **系统架构图**：清晰展示了控制器→集成器→模块的层次结构
2. **模块职责说明**：详细描述了11个功能模块的职责和接口
3. **数据流分析**：完整的数据流转图和关键数据结构说明
4. **技术实现细节**：包括帧间状态管理、ID分配算法、区域流转规则
5. **集成接口规范**：提供了完整的API接口和集成示例

### 🎯 规范价值
1. **架构清晰**：基于实际代码分析的准确架构描述
2. **问题导向**：针对frame_00257.jpg等实际问题提供解决方案
3. **实用性强**：提供了具体的集成示例和最佳实践
4. **可维护性**：为系统维护和扩展提供标准框架
5. **质量保证**：确保系统的高质量、高性能交付

### 🚀 下一步行动
1. **立即可用**：当前系统已可直接集成使用
2. **持续优化**：基于实际使用反馈持续改进
3. **扩展支持**：为新功能模块提供标准化框架
4. **性能提升**：基于监控数据进行性能优化

遵循本技术规范将确保数字孪生ID系统在项目中的成功集成和稳定运行。