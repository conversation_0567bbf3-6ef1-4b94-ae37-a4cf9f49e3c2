#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
跑胡子AI端到端测试
功能：
1. 加载测试图像
2. 运行完整流程：检测 -> 状态转换 -> 决策
3. 可视化结果
4. 测量性能指标
"""

import os
import sys
import json
import cv2
import numpy as np
import argparse
import time
from pathlib import Path
from tqdm import tqdm

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from src.core.detect import CardDetector
from src.core.state_builder import StateBuilder, format_detections_for_state_builder
from src.core.decision import DecisionMaker

def visualize_results(image, detections, formatted_detections, decision):
    """
    可视化端到端结果
    
    Args:
        image: 输入图像
        detections: 检测结果
        formatted_detections: 格式化后的检测结果
        decision: 决策结果
        
    Returns:
        可视化后的图像
    """
    # 复制图像
    vis_img = image.copy()
    
    # 绘制检测结果
    for det in detections:
        bbox = det["bbox"]
        x, y, w, h = [int(v) for v in bbox]
        
        # 绘制边界框
        cv2.rectangle(vis_img, (x, y), (x + w, y + h), (0, 255, 0), 2)
        
        # 绘制标签
        label = f"{det['label']} {det['confidence']:.2f}"
        cv2.putText(vis_img, label, (x, y - 5), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 2)
    
    # 绘制group_id
    for det in formatted_detections:
        x, y, w, h = [int(v) for v in det["bbox"]]
        group_id = det["group_id"]
        cv2.putText(vis_img, f"G:{group_id}", (x, y + h + 15), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 255), 2)
    
    # 绘制决策结果
    decision_text = f"决策: {decision['action']}, 胜率: {decision['win_rate']:.2f}"
    cv2.putText(vis_img, decision_text, (10, 30), 
               cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 0, 0), 2)
    
    return vis_img

def test_end_to_end(image_dir, model_path, num_images=10, output_dir='output'):
    """
    测试端到端流程：检测 -> 状态转换 -> 决策
    
    Args:
        image_dir: 图像目录
        model_path: 模型路径
        num_images: 要处理的图像数量
        output_dir: 输出目录
    """
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    # 初始化组件
    detector = CardDetector(model_path)
    state_builder = StateBuilder()
    decision_maker = DecisionMaker()
    
    # 获取图像文件列表
    image_files = [f for f in os.listdir(image_dir) if f.endswith(('.jpg', '.jpeg', '.png'))]
    image_files = sorted(image_files)[:num_images]
    
    results = {}
    total_time = 0
    
    for img_file in image_files:
        print(f"处理图像: {img_file}")
        
        # 读取图像
        img_path = os.path.join(image_dir, img_file)
        image = cv2.imread(img_path)
        
        if image is None:
            print(f"无法读取图像: {img_path}")
            continue
        
        start_time = time.time()
        
        # 步骤1：检测卡牌
        detection_start = time.time()
        detections = detector.detect_image(image)
        detection_time = time.time() - detection_start
        
        # 步骤2：格式化检测结果并构建游戏状态
        state_start = time.time()
        formatted_detections = format_detections_for_state_builder(detections, image.shape)
        game_state = state_builder.yolo_to_rlcard_state(formatted_detections)
        state_time = time.time() - state_start
        
        # 步骤3：决策
        decision_start = time.time()
        action, confidence = decision_maker.make_decision(game_state)
        win_rate = decision_maker.get_win_rate(game_state)
        decision_time = time.time() - decision_start

        # 格式化决策结果
        decision = {
            'action': action,
            'confidence': confidence,
            'win_rate': win_rate
        }
        
        # 计算总时间
        total_process_time = time.time() - start_time
        total_time += total_process_time
        
        # 可视化结果
        vis_img = visualize_results(image, detections, formatted_detections, decision)
        
        # 保存可视化结果
        output_path = os.path.join(output_dir, f"e2e_{img_file}")
        cv2.imwrite(output_path, vis_img)
        
        # 记录结果
        results[img_file] = {
            "process_times": {
                "detection": detection_time,
                "state_building": state_time,
                "decision_making": decision_time,
                "total": total_process_time
            },
            "decision": decision
        }
        
        print(f"处理时间: {total_process_time:.2f}秒 (检测: {detection_time:.2f}秒, 状态: {state_time:.2f}秒, 决策: {decision_time:.2f}秒)")
    
    # 计算平均处理时间
    avg_time = total_time / len(image_files) if image_files else 0
    fps = 1 / avg_time if avg_time > 0 else 0
    
    print(f"平均处理时间: {avg_time:.2f}秒 ({fps:.2f} FPS)")
    
    # 保存结果
    results["summary"] = {
        "avg_time": avg_time,
        "fps": fps
    }
    
    with open(os.path.join(output_dir, "end_to_end_results.json"), 'w') as f:
        json.dump(results, f, indent=2)
    
    print(f"处理完成，结果保存到 {output_dir}")

def parse_args():
    """解析命令行参数"""
    import argparse
    parser = argparse.ArgumentParser(description='端到端测试')
    parser.add_argument('--image_dir', type=str, default='ceshi/calibration_gt/images', help='图像目录')
    parser.add_argument('--model_path', type=str, default='best.pt', help='模型路径')
    parser.add_argument('--num_images', type=int, default=10, help='要处理的图像数量')
    parser.add_argument('--output_dir', type=str, default='output/end_to_end', help='输出目录')
    return parser.parse_args()

def main():
    """主函数"""
    args = parse_args()
    test_end_to_end(args.image_dir, args.model_path, args.num_images, args.output_dir)

if __name__ == "__main__":
    # 测试参数
    image_dir = "legacy_assets/ceshi/calibration_gt/images"
    model_path = "best.pt"  # 使用相对路径
    num_images = 10
    output_dir = "output/end_to_end"

    # 检查路径是否存在
    if not os.path.exists(image_dir):
        print(f"❌ 测试图片目录不存在: {image_dir}")
        print("请检查以下可能的路径:")
        possible_paths = [
            "legacy_assets/ceshi/calibration_gt/images",
            "data/calibration_gt/images",
            "ceshi/calibration_gt/images"
        ]
        for path in possible_paths:
            if os.path.exists(path):
                print(f"✅ 找到: {path}")
            else:
                print(f"❌ 不存在: {path}")
        exit(1)

    if not os.path.exists(model_path):
        print(f"❌ 模型文件不存在: {model_path}")
        print("请确保模型文件存在")
        exit(1)

    # 运行测试
    test_end_to_end(image_dir, model_path, num_images, output_dir)