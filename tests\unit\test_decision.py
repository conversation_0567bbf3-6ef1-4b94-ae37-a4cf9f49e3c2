import os
import sys
from pprint import pprint

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from src.core.decision import DecisionMaker
from src.core.state_builder import StateBuilder

def test_decision_maker():
    """测试决策模块"""
    print("===== 测试决策模块 =====")
    
    # 创建决策模块
    decision_maker = DecisionMaker()
    
    # 模拟状态
    state = {
        'hand': [(2, 0), (3, 0), (4, 0), (5, 0), (6, 0)],
        'discard_pile': [(7, 0), (8, 0)],
        'opponent_discard_pile': [(10, 0), (1, 1)],
        'combo_cards': [(9, 0), (9, 0), (9, 0)],
        'opponent_combo_cards': [(2, 1), (2, 1), (2, 1)],
        'legal_actions': [0, 1, 2, 4]
    }
    
    # 做出决策
    action, confidence = decision_maker.make_decision(state)
    
    # 计算胜率
    win_rate = decision_maker.get_win_rate(state)
    
    # 格式化决策结果
    result = decision_maker.format_decision(action, confidence, win_rate)
    
    # 打印结果
    print("决策结果:")
    for key, value in result.items():
        print(f"{key}: {value}")

def test_integration_with_state_builder():
    """测试与状态转换模块的集成"""
    print("\n===== 测试与状态转换模块的集成 =====")
    
    # 创建状态转换模块
    state_builder = StateBuilder()
    
    # 创建决策模块
    decision_maker = DecisionMaker()
    
    # 模拟YOLO检测结果
    yolo_detections = {
        'cards': [
            {'id': '1_二', 'group_id': 1, 'pos': [100, 100, 50, 70], 'conf': 0.95},
            {'id': '2_三', 'group_id': 1, 'pos': [160, 100, 50, 70], 'conf': 0.93},
            {'id': '3_四', 'group_id': 1, 'pos': [220, 100, 50, 70], 'conf': 0.91},
            {'id': '4_五', 'group_id': 1, 'pos': [280, 100, 50, 70], 'conf': 0.94},
            {'id': '5_六', 'group_id': 1, 'pos': [340, 100, 50, 70], 'conf': 0.92},
            
            {'id': '6_七', 'group_id': 5, 'pos': [100, 200, 50, 70], 'conf': 0.90},
            {'id': '7_八', 'group_id': 5, 'pos': [160, 200, 50, 70], 'conf': 0.89},
            
            {'id': '8_九', 'group_id': 8, 'pos': [100, 300, 50, 70], 'conf': 0.88},
        ]
    }
    
    # 转换为RLCard状态
    rlcard_state = state_builder.yolo_to_rlcard_state(yolo_detections)
    
    # 做出决策
    action, confidence = decision_maker.make_decision(rlcard_state)
    
    # 计算胜率
    win_rate = decision_maker.get_win_rate(rlcard_state)
    
    # 格式化决策结果
    result = decision_maker.format_decision(action, confidence, win_rate)
    
    # 打印状态和决策结果
    print("RLCard状态:")
    pprint(rlcard_state)
    
    print("\n决策结果:")
    for key, value in result.items():
        print(f"{key}: {value}")
    
    # 转换为显示格式
    display_state = state_builder.rlcard_to_display_format(rlcard_state)
    
    print("\n显示格式:")
    pprint(display_state)

def test_edge_cases():
    """测试边缘情况"""
    print("\n===== 测试边缘情况 =====")
    
    # 创建决策模块
    decision_maker = DecisionMaker()
    
    # 测试空状态
    print("\n测试空状态:")
    try:
        action, confidence = decision_maker.make_decision({})
        win_rate = decision_maker.get_win_rate({})
        result = decision_maker.format_decision(action, confidence, win_rate)
        pprint(result)
    except Exception as e:
        print(f"错误: {e}")
    
    # 测试没有合法动作的状态
    print("\n测试没有合法动作的状态:")
    no_actions_state = {
        'hand': [(2, 0), (3, 0)],
        'legal_actions': []
    }
    action, confidence = decision_maker.make_decision(no_actions_state)
    win_rate = decision_maker.get_win_rate(no_actions_state)
    result = decision_maker.format_decision(action, confidence, win_rate)
    pprint(result)

if __name__ == "__main__":
    test_decision_maker()
    test_integration_with_state_builder()
    test_edge_cases() 