#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
交叉验证测试脚本
对比YOLO检测结果与calibration_gt标注答案，发现类别错位问题
"""

import sys
import os
import cv2
import json
import numpy as np
from pathlib import Path
from collections import defaultdict, Counter

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from src.core.detect import CardDetector

class CrossValidationTest:
    """交叉验证测试类"""
    
    def __init__(self):
        """初始化"""
        self.base_path = Path("legacy_assets/ceshi")
        self.detector = None
        self.validation_results = {}
        
    def setup(self):
        """设置测试环境"""
        print("🔧 设置交叉验证测试环境...")
        
        # 初始化检测器
        model_path = "best.pt"
        if os.path.exists(model_path):
            self.detector = CardDetector(model_path, enable_validation=False)  # 禁用验证层，获取原始结果
            print("✅ 检测器初始化成功")
        else:
            raise FileNotFoundError(f"模型文件不存在: {model_path}")
    
    def load_ground_truth(self, frame_file: str) -> list:
        """加载真实标注"""
        json_file = frame_file.replace('.jpg', '.json')
        json_path = self.base_path / "calibration_gt" / "labels" / json_file
        
        if not json_path.exists():
            return []
        
        try:
            with open(json_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            ground_truth = []
            for shape in data.get('shapes', []):
                ground_truth.append({
                    'label': shape.get('label', ''),
                    'score': shape.get('score', 0.0),
                    'points': shape.get('points', [])
                })
            
            return ground_truth
            
        except Exception as e:
            print(f"   ⚠️ 无法加载标注文件 {json_file}: {e}")
            return []
    
    def compare_detection_with_ground_truth(self, frame_file: str):
        """对比检测结果与真实标注"""
        print(f"\n🔍 交叉验证: {frame_file}")
        
        # 读取图片
        img_path = self.base_path / "calibration_gt" / "images" / frame_file
        if not img_path.exists():
            print(f"   ❌ 图片不存在: {frame_file}")
            return
        
        image = cv2.imread(str(img_path))
        if image is None:
            print(f"   ❌ 无法读取图片: {frame_file}")
            return
        
        # YOLO检测
        detections = self.detector.detect_image(image)
        
        # 加载真实标注
        ground_truth = self.load_ground_truth(frame_file)
        
        # 对比结果
        print(f"   📊 YOLO检测结果 ({len(detections)}个):")
        yolo_labels = []
        for i, det in enumerate(detections):
            label = det.get('label', 'unknown')
            conf = det.get('confidence', 0.0)
            yolo_labels.append(label)
            print(f"      {i+1}. {label} (置信度: {conf:.3f})")
        
        print(f"   📋 真实标注 ({len(ground_truth)}个):")
        gt_labels = []
        for i, gt in enumerate(ground_truth):
            label = gt.get('label', '')
            score = gt.get('score', 0.0)
            gt_labels.append(label)
            if score is not None:
                print(f"      {i+1}. {label} (分数: {score:.3f})")
            else:
                print(f"      {i+1}. {label} (分数: N/A)")
        
        # 分析差异
        yolo_set = set(yolo_labels)
        gt_set = set(gt_labels)
        
        common_labels = yolo_set & gt_set
        yolo_only = yolo_set - gt_set
        gt_only = gt_set - yolo_set
        
        print(f"   🎯 对比分析:")
        print(f"      共同标签: {list(common_labels)}")
        print(f"      YOLO独有: {list(yolo_only)}")
        print(f"      标注独有: {list(gt_only)}")
        
        # 计算准确性
        if gt_labels:
            accuracy = len(common_labels) / len(gt_set) if gt_set else 0
            print(f"      标签准确率: {accuracy:.1%}")
        else:
            accuracy = 0
        
        # 记录结果
        self.validation_results[frame_file] = {
            'yolo_labels': yolo_labels,
            'gt_labels': gt_labels,
            'common_labels': list(common_labels),
            'yolo_only': list(yolo_only),
            'gt_only': list(gt_only),
            'accuracy': accuracy
        }
        
        return accuracy
    
    def test_key_frames(self):
        """测试关键帧"""
        print("🎯 关键帧交叉验证测试")
        print("="*60)
        
        # 测试关键帧
        key_frames = [
            "frame_00000.jpg",  # 打鸟选择画面
            "frame_00025.jpg",  # 牌局进行中
            "frame_00247.jpg",  # 小结算画面
            "frame_00371.jpg"   # 牌局结束画面
        ]
        
        total_accuracy = 0
        valid_frames = 0
        
        for frame_file in key_frames:
            accuracy = self.compare_detection_with_ground_truth(frame_file)
            if accuracy is not None:
                total_accuracy += accuracy
                valid_frames += 1
        
        if valid_frames > 0:
            avg_accuracy = total_accuracy / valid_frames
            print(f"\n📊 关键帧平均准确率: {avg_accuracy:.1%}")
        
        return avg_accuracy if valid_frames > 0 else 0
    
    def test_random_frames(self, num_frames=10):
        """测试随机帧"""
        print(f"\n🎲 随机帧交叉验证测试 ({num_frames}帧)")
        print("="*60)
        
        # 获取所有可用的标注文件
        labels_path = self.base_path / "calibration_gt" / "labels"
        json_files = list(labels_path.glob("*.json"))
        
        if len(json_files) < num_frames:
            num_frames = len(json_files)
            print(f"   调整测试数量为: {num_frames}")
        
        # 随机选择帧
        import random
        selected_files = random.sample(json_files, num_frames)
        
        total_accuracy = 0
        valid_frames = 0
        
        for json_file in selected_files:
            frame_file = json_file.name.replace('.json', '.jpg')
            accuracy = self.compare_detection_with_ground_truth(frame_file)
            if accuracy is not None:
                total_accuracy += accuracy
                valid_frames += 1
        
        if valid_frames > 0:
            avg_accuracy = total_accuracy / valid_frames
            print(f"\n📊 随机帧平均准确率: {avg_accuracy:.1%}")
        
        return avg_accuracy if valid_frames > 0 else 0
    
    def analyze_label_mapping(self):
        """分析标签映射问题"""
        print(f"\n🔍 标签映射分析")
        print("="*60)
        
        # 统计所有YOLO检测的标签
        yolo_label_count = Counter()
        gt_label_count = Counter()
        
        for frame_file, result in self.validation_results.items():
            for label in result['yolo_labels']:
                yolo_label_count[label] += 1
            for label in result['gt_labels']:
                gt_label_count[label] += 1
        
        print(f"📊 YOLO检测标签统计:")
        for label, count in yolo_label_count.most_common():
            print(f"   {label}: {count}次")
        
        print(f"\n📋 真实标注标签统计:")
        for label, count in gt_label_count.most_common():
            print(f"   {label}: {count}次")
        
        # 分析可能的映射错误
        print(f"\n🚨 可能的标签映射错误:")
        
        yolo_labels = set(yolo_label_count.keys())
        gt_labels = set(gt_label_count.keys())
        
        unmapped_yolo = yolo_labels - gt_labels
        unmapped_gt = gt_labels - yolo_labels
        
        if unmapped_yolo:
            print(f"   YOLO检测到但标注中没有的标签: {list(unmapped_yolo)}")
        
        if unmapped_gt:
            print(f"   标注中有但YOLO未检测到的标签: {list(unmapped_gt)}")
        
        # 检查ID_TO_LABEL映射
        self.check_model_label_mapping()
    
    def check_model_label_mapping(self):
        """检查模型标签映射"""
        print(f"\n🔧 检查模型标签映射")
        
        # 检查detect.py中的ID_TO_LABEL映射
        try:
            from src.core.detect import ID_TO_LABEL
            print(f"   当前ID_TO_LABEL映射:")
            for id, label in ID_TO_LABEL.items():
                print(f"      {id}: {label}")
        except ImportError:
            print(f"   ⚠️ 无法导入ID_TO_LABEL映射")
    
    def generate_correction_suggestions(self):
        """生成修正建议"""
        print(f"\n💡 修正建议")
        print("="*60)
        
        # 基于交叉验证结果生成建议
        all_gt_labels = set()
        all_yolo_labels = set()
        
        for result in self.validation_results.values():
            all_gt_labels.update(result['gt_labels'])
            all_yolo_labels.update(result['yolo_labels'])
        
        print(f"1. 标签映射修正:")
        print(f"   - 真实标注中的标签: {sorted(all_gt_labels)}")
        print(f"   - YOLO检测的标签: {sorted(all_yolo_labels)}")
        
        print(f"\n2. 建议的修正措施:")
        print(f"   - 检查并更新ID_TO_LABEL映射表")
        print(f"   - 重新训练模型或调整类别映射")
        print(f"   - 验证模型配置文件中的类别定义")
        
        print(f"\n3. 测试验证:")
        print(f"   - 使用calibration_gt标注作为验证基准")
        print(f"   - 实施持续的交叉验证测试")
        print(f"   - 建立自动化的标签一致性检查")
    
    def save_results(self):
        """保存验证结果"""
        with open("cross_validation_results.json", 'w', encoding='utf-8') as f:
            json.dump(self.validation_results, f, ensure_ascii=False, indent=2)
        
        print(f"\n💾 交叉验证结果已保存至: cross_validation_results.json")
    
    def run_cross_validation(self):
        """运行完整的交叉验证"""
        print("🚀 YOLO检测结果与calibration_gt标注交叉验证")
        print("发现并修正类别错位问题")
        
        try:
            # 设置环境
            self.setup()
            
            # 测试关键帧
            key_frame_accuracy = self.test_key_frames()
            
            # 测试随机帧
            random_frame_accuracy = self.test_random_frames(10)
            
            # 分析标签映射
            self.analyze_label_mapping()
            
            # 生成修正建议
            self.generate_correction_suggestions()
            
            # 保存结果
            self.save_results()
            
            # 总结
            print(f"\n" + "="*60)
            print(f"📊 交叉验证总结")
            print(f"="*60)
            print(f"   关键帧准确率: {key_frame_accuracy:.1%}")
            print(f"   随机帧准确率: {random_frame_accuracy:.1%}")
            
            if key_frame_accuracy < 0.5 or random_frame_accuracy < 0.5:
                print(f"   🚨 检测到严重的标签映射问题！")
                print(f"   📋 需要立即修正模型标签映射")
            else:
                print(f"   ✅ 标签映射基本正确")
            
            print(f"\n🎉 交叉验证完成！")
            
        except Exception as e:
            print(f"❌ 交叉验证过程中出现错误: {e}")
            import traceback
            traceback.print_exc()

def main():
    """主函数"""
    validator = CrossValidationTest()
    validator.run_cross_validation()

if __name__ == "__main__":
    main()
