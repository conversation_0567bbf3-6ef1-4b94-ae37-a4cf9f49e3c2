#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
修正后验证测试脚本
验证标签映射修正的效果
"""

import sys
import os
import cv2
import json
from pathlib import Path

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from src.core.detect import CardDetector

def test_corrected_mapping():
    """测试修正后的映射"""
    print("🔍 测试修正后的标签映射...")
    
    # 初始化检测器
    detector = CardDetector("best.pt", enable_validation=False)
    
    # 测试关键帧
    test_cases = [
        {
            "file": "frame_00000.jpg",
            "expected": ["打鸟选择", "已准备"],
            "description": "打鸟选择画面"
        },
        {
            "file": "frame_00371.jpg", 
            "expected": ["牌局结束"],
            "description": "牌局结束画面"
        }
    ]
    
    base_path = Path("legacy_assets/ceshi/calibration_gt/images")
    
    for test_case in test_cases:
        print(f"\n🖼️  测试 {test_case['description']} ({test_case['file']}):")
        
        img_path = base_path / test_case['file']
        if not img_path.exists():
            print(f"   ❌ 图片不存在")
            continue
        
        image = cv2.imread(str(img_path))
        if image is None:
            print(f"   ❌ 无法读取图片")
            continue
        
        # 检测
        detections = detector.detect_image(image)
        detected_labels = [det.get('label', 'unknown') for det in detections]
        
        print(f"   检测结果: {detected_labels}")
        print(f"   预期结果: {test_case['expected']}")
        
        # 检查是否包含预期标签
        success = all(label in detected_labels for label in test_case['expected'])
        if success:
            print(f"   ✅ 映射修正成功")
        else:
            print(f"   ❌ 映射修正失败")
    
    print("\n🎉 修正后验证测试完成")

if __name__ == "__main__":
    test_corrected_mapping()
