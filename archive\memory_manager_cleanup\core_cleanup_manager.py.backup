#!/usr/bin/env python3
"""
Core目录清理管理器
深度分析并清理src/core目录中的重复、过时版本
"""

import os
import shutil
import json
import ast
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Set, Tuple

class CoreCleanupManager:
    """Core目录清理管理器"""
    
    def __init__(self, project_root: str = "."):
        self.project_root = Path(project_root)
        self.core_dir = self.project_root / "src" / "core"
        self.backup_dir = self.project_root / "archive" / "core_backup"
        self.cleanup_log = []
        
        # 确保备份目录存在
        self.backup_dir.mkdir(parents=True, exist_ok=True)
        (self.backup_dir / "backups").mkdir(exist_ok=True)
        (self.backup_dir / "old_versions").mkdir(exist_ok=True)
        
    def analyze_core_directory(self):
        """深度分析core目录"""
        print("🔍 深度分析src/core目录")
        print("-" * 50)
        
        if not self.core_dir.exists():
            print(f"❌ Core目录不存在: {self.core_dir}")
            return
        
        files = list(self.core_dir.glob("*.py"))
        
        # 分类文件
        backup_files = []
        old_version_files = []
        core_files = []
        
        for file_path in files:
            filename = file_path.name
            
            if any(suffix in filename for suffix in ['.backup', '.backup2', '.backup_before_fix', '.backup_disable_validation']):
                backup_files.append(file_path)
            elif any(suffix in filename for suffix in ['_old.py', '_v2.py', 'improved_', 'synchronized_dual_format_validator.py']):
                old_version_files.append(file_path)
            else:
                core_files.append(file_path)
        
        print(f"📊 文件分析结果:")
        print(f"  总文件数: {len(files)}")
        print(f"  备份文件: {len(backup_files)}")
        print(f"  过时版本: {len(old_version_files)}")
        print(f"  核心文件: {len(core_files)}")
        
        return {
            "backup_files": backup_files,
            "old_version_files": old_version_files,
            "core_files": core_files
        }
    
    def check_dependencies(self, files_to_remove: List[Path]) -> Dict[str, List[str]]:
        """检查文件依赖关系"""
        print("\n🔍 检查依赖关系")
        
        dependencies = {}
        files_to_remove_names = {f.stem for f in files_to_remove}
        
        # 检查所有Python文件中的导入
        for py_file in self.project_root.rglob("*.py"):
            if py_file.is_file():
                try:
                    with open(py_file, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    # 简单的导入检查
                    for remove_file in files_to_remove:
                        module_name = remove_file.stem
                        if (f"from .{module_name}" in content or 
                            f"import {module_name}" in content or
                            f"from src.core.{module_name}" in content):
                            
                            if str(py_file) not in dependencies:
                                dependencies[str(py_file)] = []
                            dependencies[str(py_file)].append(module_name)
                            
                except Exception as e:
                    continue
        
        if dependencies:
            print("⚠️ 发现依赖关系:")
            for file_path, deps in dependencies.items():
                print(f"  {file_path} 依赖: {', '.join(deps)}")
        else:
            print("✅ 未发现依赖关系")
        
        return dependencies
    
    def cleanup_backup_files(self, backup_files: List[Path]):
        """清理备份文件"""
        print("\n🧹 清理备份文件")
        
        backup_target = self.backup_dir / "backups"
        
        for file_path in backup_files:
            try:
                target_path = backup_target / file_path.name
                shutil.move(str(file_path), str(target_path))
                print(f"  ✅ 已移动: {file_path.name}")
                
                self.cleanup_log.append({
                    "action": "moved_backup",
                    "source": str(file_path),
                    "target": str(target_path),
                    "timestamp": datetime.now().isoformat()
                })
                
            except Exception as e:
                print(f"  ❌ 移动失败: {file_path.name} - {e}")
                self.cleanup_log.append({
                    "action": "failed",
                    "source": str(file_path),
                    "error": str(e),
                    "timestamp": datetime.now().isoformat()
                })
    
    def cleanup_old_versions(self, old_version_files: List[Path]):
        """清理过时版本"""
        print("\n🧹 清理过时版本")
        
        old_versions_target = self.backup_dir / "old_versions"
        
        for file_path in old_version_files:
            try:
                target_path = old_versions_target / file_path.name
                shutil.move(str(file_path), str(target_path))
                print(f"  ✅ 已移动: {file_path.name}")
                
                self.cleanup_log.append({
                    "action": "moved_old_version",
                    "source": str(file_path),
                    "target": str(target_path),
                    "timestamp": datetime.now().isoformat()
                })
                
            except Exception as e:
                print(f"  ❌ 移动失败: {file_path.name} - {e}")
                self.cleanup_log.append({
                    "action": "failed",
                    "source": str(file_path),
                    "error": str(e),
                    "timestamp": datetime.now().isoformat()
                })
    
    def verify_core_integrity(self, core_files: List[Path]):
        """验证核心文件完整性"""
        print("\n🔍 验证核心文件完整性")
        
        expected_core_files = [
            "__init__.py",
            "detect.py",
            "data_validator.py",
            "decision.py",
            "state_builder.py",
            "state_manager.py",
            "enhanced_region_classifier.py",
            "enhanced_spatial_sorter.py",
            "enhanced_state_builder.py",
            "multi_algorithm_region_classifier.py",
            "game_env.py",
            "paohuzi_env.py"
        ]
        
        existing_files = {f.name for f in core_files}
        
        all_present = True
        for expected_file in expected_core_files:
            if expected_file in existing_files:
                print(f"  ✅ {expected_file}")
            else:
                print(f"  ⚠️ 缺失: {expected_file}")
                all_present = False
        
        if all_present:
            print("  🎉 所有核心文件完整！")
        else:
            print("  ⚠️ 发现缺失的核心文件")
        
        return all_present
    
    def generate_cleanup_report(self):
        """生成清理报告"""
        report_path = self.backup_dir / "core_cleanup_report.json"
        
        report = {
            "cleanup_date": datetime.now().isoformat(),
            "core_directory": str(self.core_dir),
            "backup_location": str(self.backup_dir),
            "actions": self.cleanup_log,
            "summary": {
                "total_files_processed": len(self.cleanup_log),
                "successful_moves": len([log for log in self.cleanup_log if log["action"].startswith("moved")]),
                "failed_operations": len([log for log in self.cleanup_log if log["action"] == "failed"])
            }
        }
        
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        print(f"\n📊 清理报告已生成: {report_path}")
        return report
    
    def show_final_structure(self):
        """显示清理后的目录结构"""
        print("\n📁 清理后的core目录结构:")
        print("-" * 30)
        
        if self.core_dir.exists():
            files = sorted(self.core_dir.glob("*.py"))
            for file_path in files:
                file_size = file_path.stat().st_size
                print(f"  📄 {file_path.name} ({file_size:,} bytes)")
        
        print(f"\n📦 备份位置: {self.backup_dir}")
        print("  📁 backups/ - 备份文件")
        print("  📁 old_versions/ - 过时版本")

def main():
    """主函数"""
    print("🚀 Core目录深度清理开始")
    print("=" * 50)
    
    cleanup_manager = CoreCleanupManager()
    
    # 分析目录
    analysis_result = cleanup_manager.analyze_core_directory()
    if not analysis_result:
        return
    
    backup_files = analysis_result["backup_files"]
    old_version_files = analysis_result["old_version_files"]
    core_files = analysis_result["core_files"]
    
    # 检查依赖关系
    files_to_remove = backup_files + old_version_files
    dependencies = cleanup_manager.check_dependencies(files_to_remove)
    
    if dependencies:
        print("\n⚠️ 发现依赖关系，请手动检查后再继续")
        for file_path, deps in dependencies.items():
            print(f"  {file_path} 依赖: {', '.join(deps)}")
        
        response = input("\n是否继续清理? (y/N): ")
        if response.lower() != 'y':
            print("清理已取消")
            return
    
    # 执行清理
    cleanup_manager.cleanup_backup_files(backup_files)
    cleanup_manager.cleanup_old_versions(old_version_files)
    
    # 验证完整性
    cleanup_manager.verify_core_integrity(core_files)
    
    # 生成报告
    report = cleanup_manager.generate_cleanup_report()
    
    # 显示最终结构
    cleanup_manager.show_final_structure()
    
    print("\n" + "=" * 50)
    print("🎉 Core目录清理完成！")
    print(f"📊 处理文件: {report['summary']['total_files_processed']}")
    print(f"✅ 成功移动: {report['summary']['successful_moves']}")
    print(f"❌ 操作失败: {report['summary']['failed_operations']}")
    print(f"📁 备份位置: {cleanup_manager.backup_dir}")

if __name__ == "__main__":
    main()
