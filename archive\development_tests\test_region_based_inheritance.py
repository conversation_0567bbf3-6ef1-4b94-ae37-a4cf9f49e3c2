"""
测试基于区域的继承逻辑
验证只要区域group_id和标签相同，就直接继承，不管外观变化多大
"""

import sys
import logging
sys.path.insert(0, '.')

from src.core.digital_twin_v3 import create_digital_twin_system

# 设置详细日志
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')

def test_region_based_inheritance():
    """测试基于区域的继承逻辑"""
    print("🧪 测试基于区域的继承逻辑")
    print("-" * 50)
    
    dt = create_digital_twin_system()
    
    # 第一帧：在区域1建立基础ID
    frame1 = [
        {'label': '二', 'bbox': [100, 100, 150, 200], 'confidence': 0.9, 'group_id': 1},
        {'label': '二', 'bbox': [200, 100, 250, 200], 'confidence': 0.8, 'group_id': 1},
        {'label': '三', 'bbox': [300, 100, 350, 200], 'confidence': 0.9, 'group_id': 1},
    ]
    
    result1 = dt.process_frame(frame1, frame_id=1)
    cards1 = result1['digital_twin_cards']
    
    print("第一帧ID分配:")
    for card in cards1:
        if not card.is_virtual:
            print(f"  {card.twin_id} - {card.label} - 区域{card.group_id} - bbox: {card.bbox}")
    
    # 第二帧：相同区域，但外观变化很大（位置、大小都变化）
    frame2 = [
        {'label': '二', 'bbox': [50, 50, 80, 120], 'confidence': 0.9, 'group_id': 1},    # 位置和大小都变化很大
        {'label': '二', 'bbox': [400, 300, 500, 450], 'confidence': 0.8, 'group_id': 1}, # 位置和大小都变化很大
        {'label': '三', 'bbox': [600, 500, 700, 650], 'confidence': 0.9, 'group_id': 1}, # 位置和大小都变化很大
    ]
    
    result2 = dt.process_frame(frame2, frame_id=2)
    cards2 = result2['digital_twin_cards']
    
    print("\n第二帧处理（外观变化很大，但区域相同）:")
    for card in cards2:
        if not card.is_virtual:
            print(f"  {card.twin_id} - {card.label} - 区域{card.group_id} - bbox: {card.bbox}")
    
    # 验证继承率
    inheritance_rate = result2['statistics']['inheritance_rate']
    print(f"\n继承率: {inheritance_rate:.1f}%")
    
    # 验证ID是否正确继承
    ids1 = sorted([c.twin_id for c in cards1 if not c.is_virtual])
    ids2 = sorted([c.twin_id for c in cards2 if not c.is_virtual])
    
    print(f"第一帧ID: {ids1}")
    print(f"第二帧ID: {ids2}")
    print(f"ID完全继承: {'✅' if ids1 == ids2 else '❌'}")
    
    return inheritance_rate >= 90  # 期望继承率至少90%

def test_cross_region_movement():
    """测试跨区域移动的继承逻辑"""
    print("\n🧪 测试跨区域移动的继承逻辑")
    print("-" * 50)
    
    dt = create_digital_twin_system()
    
    # 第一帧：卡牌在区域1
    frame1 = [
        {'label': '二', 'bbox': [100, 100, 150, 200], 'confidence': 0.9, 'group_id': 1},
        {'label': '三', 'bbox': [200, 100, 250, 200], 'confidence': 0.8, 'group_id': 1},
    ]
    
    result1 = dt.process_frame(frame1, frame_id=1)
    cards1 = result1['digital_twin_cards']
    
    print("第一帧（区域1）:")
    for card in cards1:
        if not card.is_virtual:
            print(f"  {card.twin_id} - {card.label} - 区域{card.group_id}")
    
    # 第二帧：卡牌移动到区域2（状态变化）
    frame2 = [
        {'label': '二', 'bbox': [300, 300, 350, 400], 'confidence': 0.9, 'group_id': 2},  # 移动到区域2
        {'label': '三', 'bbox': [400, 300, 450, 400], 'confidence': 0.8, 'group_id': 2},  # 移动到区域2
    ]
    
    result2 = dt.process_frame(frame2, frame_id=2)
    cards2 = result2['digital_twin_cards']
    
    print("\n第二帧（移动到区域2）:")
    for card in cards2:
        if not card.is_virtual:
            print(f"  {card.twin_id} - {card.label} - 区域{card.group_id}")
    
    # 验证：跨区域移动时，应该保持原有ID，并进行遮挡补偿
    print(f"\n继承率: {result2['statistics']['inheritance_rate']:.1f}%")
    
    # 检查是否有遮挡补偿
    compensated_cards = [c for c in cards2 if hasattr(c, 'is_compensated') and c.is_compensated]
    print(f"遮挡补偿数量: {len(compensated_cards)}")
    
    # 检查区域2的卡牌是否分配了新ID
    region2_cards = [c for c in cards2 if c.group_id == 2 and not c.is_virtual]
    print(f"区域2新卡牌: {[c.twin_id for c in region2_cards]}")
    
    return len(region2_cards) > 0  # 应该有新的区域2卡牌

def test_same_region_persistence():
    """测试同区域持续性：只要还在同一区域，就应该继承"""
    print("\n🧪 测试同区域持续性")
    print("-" * 50)
    
    dt = create_digital_twin_system()
    
    # 第一帧
    frame1 = [
        {'label': '二', 'bbox': [100, 100, 150, 200], 'confidence': 0.9, 'group_id': 1},
        {'label': '三', 'bbox': [200, 100, 250, 200], 'confidence': 0.8, 'group_id': 1},
    ]
    
    result1 = dt.process_frame(frame1, frame_id=1)
    
    # 连续多帧，每帧都有较大的外观变化，但都在同一区域
    inheritance_rates = []
    
    for frame_id in range(2, 6):  # 第2-5帧
        # 每帧都有不同的位置和大小
        frame = [
            {'label': '二', 'bbox': [100 + frame_id * 50, 100 + frame_id * 30, 150 + frame_id * 20, 200 + frame_id * 40], 'confidence': 0.9, 'group_id': 1},
            {'label': '三', 'bbox': [200 + frame_id * 60, 100 + frame_id * 25, 250 + frame_id * 30, 200 + frame_id * 35], 'confidence': 0.8, 'group_id': 1},
        ]
        
        result = dt.process_frame(frame, frame_id=frame_id)
        inheritance_rate = result['statistics']['inheritance_rate']
        inheritance_rates.append(inheritance_rate)
        
        print(f"第{frame_id}帧继承率: {inheritance_rate:.1f}%")
    
    # 验证所有帧的继承率都很高
    avg_inheritance_rate = sum(inheritance_rates) / len(inheritance_rates)
    print(f"\n平均继承率: {avg_inheritance_rate:.1f}%")
    
    return avg_inheritance_rate >= 90  # 期望平均继承率至少90%

def main():
    """运行所有测试"""
    print("🚀 基于区域的继承逻辑测试")
    print("=" * 60)
    
    tests = [
        test_region_based_inheritance,
        test_cross_region_movement,
        test_same_region_persistence
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"❌ 测试失败: {e}")
            results.append(False)
    
    print("\n" + "=" * 60)
    print("📊 测试总结:")
    success_count = sum(results)
    total_count = len(results)
    
    test_names = ["基于区域的继承", "跨区域移动", "同区域持续性"]
    
    for i, (name, result) in enumerate(zip(test_names, results)):
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  测试{i+1} ({name}): {status}")
    
    print(f"\n🎯 总体结果: {success_count}/{total_count} 测试通过")
    
    if success_count == total_count:
        print("🎉 基于区域的继承逻辑完全正确！")
    else:
        print("⚠️ 继承逻辑需要进一步调整。")

if __name__ == "__main__":
    main()
