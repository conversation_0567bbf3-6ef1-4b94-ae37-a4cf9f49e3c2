"""
测试基于区域状态的继承逻辑
验证删除IOU后的继承机制是否正确工作
"""

import sys
import logging
sys.path.insert(0, '.')

from src.modules.simple_inheritor import SimpleInheritor

# 设置详细日志
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')

def test_region_state_inheritance():
    """测试基于区域状态的继承逻辑"""
    print("🧪 测试基于区域状态的继承逻辑")
    print("-" * 50)
    
    inheritor = SimpleInheritor()
    
    # 第一帧：建立基础ID
    frame1_cards = [
        {'label': '二', 'bbox': [100, 100, 150, 200], 'confidence': 0.9, 'group_id': 1, 'twin_id': '1二'},
        {'label': '二', 'bbox': [200, 100, 250, 200], 'confidence': 0.8, 'group_id': 1, 'twin_id': '2二'},
        {'label': '三', 'bbox': [300, 100, 350, 200], 'confidence': 0.9, 'group_id': 1, 'twin_id': '1三'},
    ]
    
    # 更新前一帧映射
    inheritor._update_previous_frame(frame1_cards)
    
    print("第一帧建立的映射:")
    for key, card in inheritor.previous_frame_mapping.items():
        print(f"  {key} -> {card['twin_id']}")
    
    # 第二帧：相同区域和标签，但位置完全不同
    frame2_cards = [
        {'label': '二', 'bbox': [500, 500, 550, 600], 'confidence': 0.7, 'group_id': 1},  # 位置完全不同
        {'label': '二', 'bbox': [600, 500, 650, 600], 'confidence': 0.6, 'group_id': 1},  # 位置完全不同
        {'label': '三', 'bbox': [700, 500, 750, 600], 'confidence': 0.8, 'group_id': 1},  # 位置完全不同
    ]
    
    result = inheritor.process_inheritance(frame2_cards)
    
    print("\n第二帧继承结果:")
    print(f"继承的卡牌数量: {len(result.inherited_cards)}")
    print(f"新卡牌数量: {len(result.new_cards)}")
    
    for card in result.inherited_cards:
        print(f"  继承: {card.get('twin_id')} (区域{card['group_id']}, 标签{card['label']})")
    
    for card in result.new_cards:
        print(f"  新增: 区域{card['group_id']}, 标签{card['label']}")
    
    # 验证：应该全部继承，因为区域和标签都匹配
    expected_inherited = 3
    actual_inherited = len(result.inherited_cards)
    
    print(f"\n验证结果:")
    print(f"期望继承: {expected_inherited}张")
    print(f"实际继承: {actual_inherited}张")
    print(f"继承率: {result.statistics['inheritance_rate']:.1%}")
    
    success = actual_inherited == expected_inherited
    print(f"测试结果: {'✅ 通过' if success else '❌ 失败'}")
    
    return success

def test_cross_region_movement():
    """测试跨区域移动（应该不继承，作为新卡牌）"""
    print("\n🧪 测试跨区域移动")
    print("-" * 50)
    
    inheritor = SimpleInheritor()
    
    # 第一帧：区域1的卡牌
    frame1_cards = [
        {'label': '二', 'bbox': [100, 100, 150, 200], 'confidence': 0.9, 'group_id': 1, 'twin_id': '1二'},
        {'label': '三', 'bbox': [200, 100, 250, 200], 'confidence': 0.8, 'group_id': 1, 'twin_id': '1三'},
    ]
    
    inheritor._update_previous_frame(frame1_cards)
    
    # 第二帧：相同标签但移动到区域2
    frame2_cards = [
        {'label': '二', 'bbox': [100, 300, 150, 400], 'confidence': 0.7, 'group_id': 2},  # 移动到区域2
        {'label': '三', 'bbox': [200, 300, 250, 400], 'confidence': 0.6, 'group_id': 2},  # 移动到区域2
    ]
    
    result = inheritor.process_inheritance(frame2_cards)
    
    print("跨区域移动结果:")
    print(f"继承的卡牌数量: {len(result.inherited_cards)}")
    print(f"新卡牌数量: {len(result.new_cards)}")
    
    # 验证：应该全部作为新卡牌，因为区域不同
    expected_new = 2
    actual_new = len(result.new_cards)
    
    print(f"\n验证结果:")
    print(f"期望新卡牌: {expected_new}张")
    print(f"实际新卡牌: {actual_new}张")
    
    success = actual_new == expected_new
    print(f"测试结果: {'✅ 通过' if success else '❌ 失败'}")
    
    return success

def test_mixed_inheritance():
    """测试混合继承（部分继承，部分新增）"""
    print("\n🧪 测试混合继承")
    print("-" * 50)
    
    inheritor = SimpleInheritor()
    
    # 第一帧：建立基础
    frame1_cards = [
        {'label': '二', 'bbox': [100, 100, 150, 200], 'confidence': 0.9, 'group_id': 1, 'twin_id': '1二'},
        {'label': '三', 'bbox': [200, 100, 250, 200], 'confidence': 0.8, 'group_id': 1, 'twin_id': '1三'},
    ]
    
    inheritor._update_previous_frame(frame1_cards)
    
    # 第二帧：部分继承，部分新增
    frame2_cards = [
        {'label': '二', 'bbox': [500, 500, 550, 600], 'confidence': 0.7, 'group_id': 1},  # 应该继承
        {'label': '四', 'bbox': [600, 500, 650, 600], 'confidence': 0.6, 'group_id': 1},  # 新标签，应该新增
        {'label': '三', 'bbox': [700, 500, 750, 600], 'confidence': 0.8, 'group_id': 2},  # 新区域，应该新增
    ]
    
    result = inheritor.process_inheritance(frame2_cards)
    
    print("混合继承结果:")
    print(f"继承的卡牌数量: {len(result.inherited_cards)}")
    print(f"新卡牌数量: {len(result.new_cards)}")
    
    for card in result.inherited_cards:
        print(f"  继承: {card.get('twin_id')} (区域{card['group_id']}, 标签{card['label']})")
    
    for card in result.new_cards:
        print(f"  新增: 区域{card['group_id']}, 标签{card['label']}")
    
    # 验证：应该1张继承，2张新增
    expected_inherited = 1
    expected_new = 2
    actual_inherited = len(result.inherited_cards)
    actual_new = len(result.new_cards)
    
    print(f"\n验证结果:")
    print(f"期望继承: {expected_inherited}张，实际: {actual_inherited}张")
    print(f"期望新增: {expected_new}张，实际: {actual_new}张")
    
    success = actual_inherited == expected_inherited and actual_new == expected_new
    print(f"测试结果: {'✅ 通过' if success else '❌ 失败'}")
    
    return success

def main():
    """运行所有测试"""
    print("🚀 基于区域状态的继承逻辑测试")
    print("=" * 60)
    
    tests = [
        test_region_state_inheritance,
        test_cross_region_movement,
        test_mixed_inheritance
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"❌ 测试失败: {e}")
            results.append(False)
    
    print("\n" + "=" * 60)
    print("📊 测试总结:")
    success_count = sum(results)
    total_count = len(results)
    
    test_names = ["区域状态继承", "跨区域移动", "混合继承"]
    
    for i, (name, result) in enumerate(zip(test_names, results)):
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  测试{i+1} ({name}): {status}")
    
    print(f"\n🎯 总体结果: {success_count}/{total_count} 测试通过")
    
    if success_count == total_count:
        print("🎉 基于区域状态的继承逻辑完全正确！")
        print("✅ 已成功删除IOU相关的继承代码")
        print("✅ 继承现在完全基于区域状态(group_id + label)")
    else:
        print("⚠️ 继承逻辑需要进一步调整。")

if __name__ == "__main__":
    main()
