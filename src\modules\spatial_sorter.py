"""
空间排序模块 (SpatialSorter)
根据GAME_RULES.md和GAME_RULES_OPTIMIZED.md的空间分配原则实现标准的空间排序算法

核心原则：
1. 仅适用于同一帧内同时出现多张卡牌的情况
2. 手牌区(1)、吃碰区(6,16)：从下到上，再从左到右
3. 其他物理区域：从左到右，再从上到下
4. 弃牌区特殊处理：区域5从右到左，区域9从左到右
"""

from typing import Dict, List, Any, Tuple
from dataclasses import dataclass
from collections import defaultdict
import logging

logger = logging.getLogger(__name__)

@dataclass
class CardPosition:
    """卡牌位置信息"""
    card: Dict[str, Any]
    x_center: float
    y_center: float
    x_left: float
    y_top: float
    x_right: float
    y_bottom: float
    width: float
    height: float

@dataclass
class SpatialSortingResult:
    """空间排序结果"""
    sorted_cards: List[Dict[str, Any]]
    sorting_applied: bool
    region_id: int
    sorting_rule: str
    statistics: Dict[str, Any]

class SpatialSorter:
    """空间排序器 - 实现GAME_RULES.md的空间分配原则"""
    
    def __init__(self):
        # 排序参数配置
        self.sorting_params = {
            "row_height_threshold": 8.0,    # 行高容差
            "column_width_threshold": 8.0,   # 列宽容差 - 调节到绝对最小值8px避免相邻组混淆
            "position_tolerance": 5.0        # 位置容差
        }
        
        # 区域排序规则映射
        self.region_sorting_rules = {
            1: "bottom_to_top_left_to_right",    # 观战方手牌区
            2: "top_to_bottom_left_to_right",    # 对战方手牌区
            3: "left_to_right_top_to_bottom",    # 对战方弃牌区
            4: "left_to_right_top_to_bottom",    # 观战方打出区
            5: "right_to_left_special",          # 观战方弃牌区（特殊）
            6: "bottom_to_top_left_to_right",    # 观战方吃碰区
            7: "top_to_bottom_left_to_right",    # 对战方手牌区
            8: "top_to_bottom_left_to_right",    # 对战方调整区
            9: "left_to_right_special",          # 对战方出牌区（特殊）
            10: "left_to_right_top_to_bottom",   # 对战方打出区
            11: "left_to_right_top_to_bottom",   # 对战方弃牌区
            12: "left_to_right_top_to_bottom",   # 底牌区
            13: "left_to_right_top_to_bottom",   # 牌堆区
            14: "left_to_right_top_to_bottom",   # 赢方区域
            15: "left_to_right_top_to_bottom",   # 输方区域
            16: "bottom_to_top_left_to_right",   # 对战方吃碰区
        }
        
        logger.info("空间排序器初始化完成 - 基于GAME_RULES.md原则")
    
    def sort_cards_by_spatial_order(self, cards: List[Dict[str, Any]], region_id: int) -> SpatialSortingResult:
        """
        按空间顺序排序卡牌
        
        Args:
            cards: 卡牌列表
            region_id: 区域ID
            
        Returns:
            SpatialSortingResult: 排序结果
        """
        logger.info(f"开始对区域{region_id}的{len(cards)}张卡牌进行空间排序")
        
        # 检查是否需要排序
        if len(cards) <= 1:
            logger.debug(f"区域{region_id}只有{len(cards)}张卡牌，无需排序")
            return SpatialSortingResult(
                sorted_cards=cards,
                sorting_applied=False,
                region_id=region_id,
                sorting_rule="no_sorting_needed",
                statistics={"card_count": len(cards), "sorting_applied": False}
            )
        
        # 获取排序规则
        sorting_rule = self.region_sorting_rules.get(region_id, "left_to_right_top_to_bottom")
        logger.debug(f"区域{region_id}使用排序规则: {sorting_rule}")
        
        # 转换为位置对象
        positions = self._extract_positions(cards)
        
        # 应用排序规则
        sorted_positions = self._apply_sorting_rule(positions, sorting_rule)
        
        # 提取排序后的卡牌
        sorted_cards = [pos.card for pos in sorted_positions]
        
        # 生成统计信息
        statistics = self._generate_sorting_statistics(cards, sorted_cards, sorting_rule)
        
        logger.info(f"区域{region_id}空间排序完成，使用规则: {sorting_rule}")
        
        return SpatialSortingResult(
            sorted_cards=sorted_cards,
            sorting_applied=True,
            region_id=region_id,
            sorting_rule=sorting_rule,
            statistics=statistics
        )
    
    def _extract_positions(self, cards: List[Dict[str, Any]]) -> List[CardPosition]:
        """提取卡牌位置信息"""
        positions = []
        
        for card in cards:
            # 获取边界框信息
            if 'points' in card and card['points']:
                # 使用points格式 [[x1,y1], [x2,y2], [x3,y3], [x4,y4]]
                points = card['points']
                x_coords = [p[0] for p in points]
                y_coords = [p[1] for p in points]
                
                x_left = min(x_coords)
                x_right = max(x_coords)
                y_top = min(y_coords)
                y_bottom = max(y_coords)
                
            elif 'bbox' in card:
                # 使用bbox格式 [x1, y1, x2, y2]
                x_left, y_top, x_right, y_bottom = card['bbox']
            else:
                logger.warning(f"卡牌缺少位置信息: {card.get('label', 'unknown')}")
                continue
            
            # 计算中心点和尺寸
            x_center = (x_left + x_right) / 2
            y_center = (y_top + y_bottom) / 2
            width = x_right - x_left
            height = y_bottom - y_top
            
            position = CardPosition(
                card=card,
                x_center=x_center,
                y_center=y_center,
                x_left=x_left,
                y_top=y_top,
                x_right=x_right,
                y_bottom=y_bottom,
                width=width,
                height=height
            )
            positions.append(position)
        
        return positions

    def _apply_sorting_rule(self, positions: List[CardPosition], sorting_rule: str) -> List[CardPosition]:
        """应用指定的排序规则"""
        if sorting_rule == "bottom_to_top_left_to_right":
            return self._sort_bottom_to_top_left_to_right(positions)
        elif sorting_rule == "top_to_bottom_left_to_right":
            return self._sort_top_to_bottom_left_to_right(positions)
        elif sorting_rule == "left_to_right_top_to_bottom":
            return self._sort_left_to_right_top_to_bottom(positions)
        elif sorting_rule == "right_to_left_special":
            return self._sort_right_to_left_special(positions)
        elif sorting_rule == "left_to_right_special":
            return self._sort_left_to_right_special(positions)
        else:
            # 默认排序
            return self._sort_left_to_right_top_to_bottom(positions)

    def _sort_bottom_to_top_left_to_right(self, positions: List[CardPosition]) -> List[CardPosition]:
        """
        从下到上，再从左到右排序（手牌区1、吃碰区6,16）

        根据GAME_RULES.md：
        - 从最下面一排最左边的牌开始，分配ID 1二
        - 然后是它上边的牌 2二，标记到手牌区最上面结束
        - 再标记右边一列从最下面开始依次往上标
        """
        logger.debug("应用排序规则: 从下到上，再从左到右")

        # 按列分组（X坐标相近的为一列）
        columns = self._group_by_columns(positions)

        sorted_positions = []
        # 从左到右处理列
        for x_key in sorted(columns.keys()):
            column_positions = columns[x_key]
            # 列内从下到上排序（Y坐标大的在前，因为Y轴向下为正）
            column_positions.sort(key=lambda p: -p.y_bottom)  # 使用底部坐标，负号表示从下到上
            sorted_positions.extend(column_positions)

        return sorted_positions

    def _sort_top_to_bottom_left_to_right(self, positions: List[CardPosition]) -> List[CardPosition]:
        """从上到下，再从左到右排序（对战方手牌区2,7,8）"""
        logger.debug("应用排序规则: 从上到下，再从左到右")

        # 按列分组
        columns = self._group_by_columns(positions)

        sorted_positions = []
        # 从左到右处理列
        for x_key in sorted(columns.keys()):
            column_positions = columns[x_key]
            # 列内从上到下排序
            column_positions.sort(key=lambda p: p.y_top)
            sorted_positions.extend(column_positions)

        return sorted_positions

    def _sort_left_to_right_top_to_bottom(self, positions: List[CardPosition]) -> List[CardPosition]:
        """从左到右，再从上到下排序（其他大部分区域）"""
        logger.debug("应用排序规则: 从左到右，再从上到下")

        # 按行分组（Y坐标相近的为一行）
        rows = self._group_by_rows(positions)

        sorted_positions = []
        # 从上到下处理行
        for y_key in sorted(rows.keys()):
            row_positions = rows[y_key]
            # 行内从左到右排序
            row_positions.sort(key=lambda p: p.x_left)
            sorted_positions.extend(row_positions)

        return sorted_positions

    def _sort_right_to_left_special(self, positions: List[CardPosition]) -> List[CardPosition]:
        """
        从右到左特殊排序（观战方弃牌区5）

        根据GAME_RULES.md：
        - 5状态区域内的牌需要从右往左标记
        - 标记到头从上方一排又从右往左标记
        """
        logger.debug("应用排序规则: 从右到左特殊排序（弃牌区5）")

        # 按行分组
        rows = self._group_by_rows(positions)

        sorted_positions = []
        # 从下到上处理行（弃牌是从下往上堆叠的）
        for y_key in sorted(rows.keys(), reverse=True):
            row_positions = rows[y_key]
            # 行内从右到左排序
            row_positions.sort(key=lambda p: -p.x_right)  # 负号表示从右到左
            sorted_positions.extend(row_positions)

        return sorted_positions

    def _sort_left_to_right_special(self, positions: List[CardPosition]) -> List[CardPosition]:
        """
        从左到右特殊排序（对战方出牌区9）

        根据GAME_RULES.md：
        - 9状态区域从左往右标记
        - 标记到头，从下面一排继续从左往右标记
        """
        logger.debug("应用排序规则: 从左到右特殊排序（出牌区9）")

        # 按行分组
        rows = self._group_by_rows(positions)

        sorted_positions = []
        # 从上到下处理行
        for y_key in sorted(rows.keys()):
            row_positions = rows[y_key]
            # 行内从左到右排序
            row_positions.sort(key=lambda p: p.x_left)
            sorted_positions.extend(row_positions)

        return sorted_positions

    def _group_by_rows(self, positions: List[CardPosition]) -> Dict[float, List[CardPosition]]:
        """按行分组（Y坐标相近的为一行）"""
        rows = defaultdict(list)
        tolerance = self.sorting_params["row_height_threshold"]

        for position in positions:
            # 寻找合适的行
            assigned = False
            for y_key in rows.keys():
                if abs(position.y_center - y_key) <= tolerance:
                    rows[y_key].append(position)
                    assigned = True
                    break

            if not assigned:
                # 创建新行
                rows[position.y_center].append(position)

        return rows

    def _group_by_columns(self, positions: List[CardPosition]) -> Dict[float, List[CardPosition]]:
        """按列分组（X坐标相近的为一列）"""
        columns = defaultdict(list)
        tolerance = self.sorting_params["column_width_threshold"]

        for position in positions:
            # 寻找合适的列
            assigned = False
            for x_key in columns.keys():
                if abs(position.x_center - x_key) <= tolerance:
                    columns[x_key].append(position)
                    assigned = True
                    break

            if not assigned:
                # 创建新列
                columns[position.x_center].append(position)

        return columns

    def _generate_sorting_statistics(self, original_cards: List[Dict[str, Any]],
                                   sorted_cards: List[Dict[str, Any]],
                                   sorting_rule: str) -> Dict[str, Any]:
        """生成排序统计信息"""

        # 检查排序是否改变了顺序
        order_changed = False
        if len(original_cards) == len(sorted_cards):
            for i, (orig, sorted_card) in enumerate(zip(original_cards, sorted_cards)):
                if orig != sorted_card:
                    order_changed = True
                    break

        # 提取位置信息用于分析
        positions_info = []
        for card in sorted_cards:
            if 'points' in card and card['points']:
                points = card['points']
                y_coords = [p[1] for p in points]
                y_bottom = max(y_coords)
                positions_info.append({
                    'label': card.get('label', 'unknown'),
                    'y_bottom': y_bottom
                })

        return {
            "card_count": len(sorted_cards),
            "sorting_rule": sorting_rule,
            "order_changed": order_changed,
            "sorting_applied": True,
            "positions_info": positions_info
        }

    def sort_cards_by_type_and_region(self, cards: List[Dict[str, Any]]) -> Dict[str, List[Dict[str, Any]]]:
        """
        按卡牌类型和区域进行分组排序

        这是主要的入口方法，用于处理一帧中的所有卡牌
        """
        logger.info(f"开始对{len(cards)}张卡牌进行分组空间排序")

        # 按区域分组
        cards_by_region = defaultdict(list)
        for card in cards:
            region_id = card.get('group_id', 1)  # 默认区域1
            cards_by_region[region_id].append(card)

        # 对每个区域内的卡牌按类型分组并排序
        all_sorted_cards = []

        for region_id, region_cards in cards_by_region.items():
            logger.debug(f"处理区域{region_id}的{len(region_cards)}张卡牌")

            # 按卡牌类型分组
            cards_by_type = defaultdict(list)
            for card in region_cards:
                card_type = card.get('label', 'unknown')
                cards_by_type[card_type].append(card)

            # 对每种类型的卡牌进行空间排序
            for card_type, type_cards in cards_by_type.items():
                if len(type_cards) > 1:
                    # 多张同类型卡牌需要空间排序
                    sorting_result = self.sort_cards_by_spatial_order(type_cards, region_id)
                    all_sorted_cards.extend(sorting_result.sorted_cards)
                    logger.debug(f"区域{region_id}的{len(type_cards)}张'{card_type}'牌已排序")
                else:
                    # 单张卡牌直接添加
                    all_sorted_cards.extend(type_cards)

        logger.info(f"分组空间排序完成，共处理{len(all_sorted_cards)}张卡牌")
        return all_sorted_cards


def create_spatial_sorter():
    """创建空间排序器实例"""
    return SpatialSorter()
