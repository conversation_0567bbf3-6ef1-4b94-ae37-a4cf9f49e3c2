"""
测试数据验证修复
"""

import sys
import os
sys.path.append('src')

def test_data_validation():
    """测试数据验证修复"""
    print("🧪 测试数据验证修复")
    print("=" * 60)
    
    try:
        from src.modules.data_validator import DataValidator
        
        # 创建验证器
        validator = DataValidator()
        print("✅ 数据验证器创建成功")
        
        # 测试数据（包含字符串类型的confidence和group_id）
        test_detections = [
            {
                'label': '二',
                'bbox': [100, 100, 150, 150],
                'confidence': '0.9',  # 字符串类型
                'group_id': '1'       # 字符串类型
            },
            {
                'label': '暗',
                'bbox': [500, 100, 550, 150],
                'confidence': 0.7,    # 数字类型
                'group_id': 6         # 数字类型
            },
            {
                'label': '三',
                'bbox': [200, 200, 250, 250],
                'confidence': 'invalid',  # 无效字符串
                'group_id': 'invalid'     # 无效字符串
            }
        ]
        
        # 验证数据
        result = validator.validate_detections(test_detections)
        
        print(f"✅ 验证完成:")
        print(f"   有效: {result.is_valid}")
        print(f"   清理后数据: {len(result.cleaned_data)}条")
        print(f"   错误: {len(result.errors)}个")
        print(f"   警告: {len(result.warnings)}个")
        
        if result.errors:
            print("❌ 错误详情:")
            for error in result.errors:
                print(f"     {error}")
        
        if result.warnings:
            print("⚠️ 警告详情:")
            for warning in result.warnings:
                print(f"     {warning}")
        
        # 检查清理后的数据类型
        if result.cleaned_data:
            print("\n📊 清理后数据类型检查:")
            for i, data in enumerate(result.cleaned_data):
                print(f"   数据{i+1}:")
                print(f"     confidence: {type(data['confidence'])} = {data['confidence']}")
                print(f"     group_id: {type(data['group_id'])} = {data['group_id']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_phase2_integration():
    """测试第二阶段系统集成"""
    print("\n🔧 测试第二阶段系统集成")
    print("=" * 60)
    
    try:
        from src.modules import create_phase2_integrator
        
        # 创建系统
        system = create_phase2_integrator()
        print("✅ 第二阶段系统创建成功")
        
        # 测试数据（修复后的格式）
        test_detections = [
            {
                'label': '二',
                'bbox': [100, 100, 150, 150],
                'confidence': '0.9',  # 字符串类型，应该被自动转换
                'group_id': '1'       # 字符串类型，应该被自动转换
            },
            {
                'label': '暗',
                'bbox': [500, 100, 550, 150],
                'confidence': 0.7,
                'group_id': 6
            }
        ]
        
        # 处理测试数据
        result = system.process_frame(test_detections)
        print(f"✅ 处理测试: {'成功' if result.success else '失败'}")
        
        if result.success:
            print(f"   处理卡牌: {len(result.processed_cards)}张")
            for card in result.processed_cards:
                print(f"     {card.get('twin_id', 'N/A')} - 区域{card.get('group_id', 'N/A')}, {card.get('label', 'N/A')}")
                print(f"       confidence类型: {type(card.get('confidence', 'N/A'))}")
                print(f"       group_id类型: {type(card.get('group_id', 'N/A'))}")
        else:
            print("❌ 处理失败:")
            for error in result.validation_errors:
                print(f"     {error}")
        
        return result.success
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 数据验证修复测试")
    print("=" * 80)
    
    # 测试数据验证
    validation_success = test_data_validation()
    
    # 测试系统集成
    integration_success = test_phase2_integration()
    
    print("\n" + "=" * 80)
    if validation_success and integration_success:
        print("🎉 所有测试通过！")
        print("\n✅ 修复状态:")
        print("  - 数据验证器已修复：支持字符串类型的confidence和group_id")
        print("  - 自动类型转换：字符串 → 数字")
        print("  - 第二阶段系统集成正常")
        print("\n🚀 现在可以重新启动calibration_gt_final_processor.py:")
        print("  python calibration_gt_final_processor.py")
        print("\n🎯 预期改进:")
        print("  - 数据验证错误应该大幅减少")
        print("  - 'dict' object has no attribute 'label' 错误已修复")
        print("  - 处理成功率应该显著提高")
    else:
        print("❌ 测试失败，需要进一步修复")

if __name__ == "__main__":
    main()
