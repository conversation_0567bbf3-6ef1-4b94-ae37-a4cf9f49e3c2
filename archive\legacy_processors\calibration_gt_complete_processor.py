#!/usr/bin/env python3
"""
calibration_gt完整处理器 - 修复版本

修复问题：
1. 处理所有372帧（不跳过任何帧）
2. 正确使用原始坐标信息
3. 自动运行，无需用户交互
"""

import os
import json
import shutil
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass
import logging
from datetime import datetime

# 导入项目核心模块
from src.core.digital_twin_v2 import create_digital_twin_system, CardDetection
from src.core.synchronized_dual_format_validator import SynchronizedDualFormatValidator

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

@dataclass
class CompleteProcessorConfig:
    """完整处理器配置"""
    source_dir: str = "legacy_assets/ceshi/calibration_gt"
    output_dir: str = "output/calibration_gt_complete_with_digital_twin"
    image_width: int = 640
    image_height: int = 320
    
    # 有效卡牌类别（21个：20个数值 + 1个暗牌）
    valid_card_labels: List[str] = None
    
    def __post_init__(self):
        if self.valid_card_labels is None:
            self.valid_card_labels = [
                "一", "二", "三", "四", "五", "六", "七", "八", "九", "十",
                "壹", "贰", "叁", "肆", "伍", "陆", "柒", "捌", "玖", "拾",
                "暗"
            ]

class CalibrationGTCompleteProcessor:
    """calibration_gt完整处理器"""
    
    def __init__(self, config: CompleteProcessorConfig):
        self.config = config
        self.digital_twin_system = create_digital_twin_system()
        self.validator = SynchronizedDualFormatValidator()
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
        
        # 处理统计
        self.stats = {
            "total_frames": 0,
            "processed_frames": 0,
            "frames_with_cards": 0,
            "frames_without_cards": 0,
            "total_cards": 0,
            "successful_assignments": 0,
            "validation_errors": 0,
            "consistency_scores": []
        }
        
    def process_complete_dataset(self) -> Dict[str, Any]:
        """处理完整的calibration_gt数据集（所有372帧）"""
        self.logger.info("🎯 开始处理完整calibration_gt数据集（372帧）...")
        
        # 创建输出目录结构
        self._create_output_structure()
        
        # 获取所有标注文件（确保是372个）
        label_files = self._get_all_label_files()
        self.stats["total_frames"] = len(label_files)
        
        self.logger.info(f"📊 找到 {len(label_files)} 个标注文件")
        
        if len(label_files) != 372:
            self.logger.warning(f"⚠️ 预期372个文件，实际找到{len(label_files)}个")
        
        # 按帧序列处理所有文件
        for frame_file in sorted(label_files):
            try:
                self._process_single_frame_complete(frame_file)
                self.stats["processed_frames"] += 1
                
                if self.stats["processed_frames"] % 50 == 0:
                    self.logger.info(f"📈 已处理 {self.stats['processed_frames']}/{self.stats['total_frames']} 帧")
                    
            except Exception as e:
                self.logger.error(f"❌ 处理帧 {frame_file} 失败: {e}")
                continue
                
        # 生成最终报告
        final_report = self._generate_final_report()
        
        self.logger.info("🎉 calibration_gt完整数据集处理完成")
        return final_report
    
    def _create_output_structure(self):
        """创建输出目录结构"""
        output_path = Path(self.config.output_dir)
        
        # 创建主要目录
        directories = [
            output_path,
            output_path / "images",
            output_path / "labels", 
            output_path / "rlcard_format",
            output_path / "validation_reports"
        ]
        
        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)
            
        self.logger.info(f"📁 输出目录结构创建完成: {output_path}")
    
    def _get_all_label_files(self) -> List[Path]:
        """获取所有标注文件（确保372个）"""
        labels_dir = Path(self.config.source_dir) / "labels"
        if not labels_dir.exists():
            raise FileNotFoundError(f"标注目录不存在: {labels_dir}")
            
        # 获取所有JSON文件
        label_files = list(labels_dir.glob("*.json"))
        
        # 验证文件完整性
        expected_files = [f"frame_{i:05d}.json" for i in range(372)]
        actual_files = [f.name for f in label_files]
        
        missing_files = set(expected_files) - set(actual_files)
        if missing_files:
            self.logger.warning(f"⚠️ 缺失文件: {sorted(missing_files)}")
            
        return label_files
    
    def _process_single_frame_complete(self, frame_file: Path):
        """完整处理单个帧文件"""
        frame_name = frame_file.stem
        self.logger.debug(f"🔄 处理帧: {frame_name}")
        
        # 读取原始标注
        with open(frame_file, 'r', encoding='utf-8') as f:
            original_data = json.load(f)
        
        # 转换为CardDetection格式（只处理有效卡牌）
        card_detections = self._convert_to_card_detections(original_data)
        
        if card_detections:
            # 有卡牌的帧：使用数字孪生系统处理
            self.stats["frames_with_cards"] += 1
            self.stats["total_cards"] += len(card_detections)
            
            # 使用数字孪生系统处理
            dt_result = self.digital_twin_system.process_frame(card_detections)
            
            # 生成双轨输出
            dual_result = self.digital_twin_system.export_synchronized_dual_format(
                dt_result,
                self.config.image_width,
                self.config.image_height,
                original_data.get("imagePath", f"{frame_name}.jpg")
            )
            
            # 验证一致性
            validation_result = self._validate_with_error_handling(dual_result, dt_result)
            
            # 更新统计
            self.stats["successful_assignments"] += len(dt_result.digital_twin_cards)
            self.stats["consistency_scores"].append(validation_result['overall_consistency_score'])
            
        else:
            # 无卡牌的帧：保留原始内容，不使用数字孪生系统
            self.stats["frames_without_cards"] += 1
            
            # 创建空的处理结果
            dual_result = {
                'rlcard_format': {"hand": [], "public": [], "opponents": []},
                'anylabeling_format': original_data.copy()
            }
            
            # 创建简单的验证结果
            validation_result = {
                'overall_consistency_score': 1.0,  # 无卡牌帧认为是完全一致的
                'is_consistent': True,
                'detailed_checks': {},
                'issues': [],
                'recommendations': []
            }
        
        # 保存结果（所有帧都保存）
        self._save_frame_results_complete(frame_name, original_data, dual_result, validation_result)
    
    def _convert_to_card_detections(self, anylabeling_data: Dict) -> List[CardDetection]:
        """将AnyLabeling格式转换为CardDetection格式（只处理有效卡牌）"""
        detections = []
        
        for shape in anylabeling_data.get("shapes", []):
            # 只处理有效卡牌类别
            label = shape.get("label", "")
            if label not in self.config.valid_card_labels:
                self.logger.debug(f"跳过非卡牌类别: {label}")
                continue
                
            # 提取边界框信息（使用原始坐标）
            points = shape.get("points", [])
            if len(points) != 4:
                self.logger.warning(f"无效的边界框点数: {len(points)}")
                continue
                
            # 计算边界框 (x, y, width, height) - 使用原始坐标
            x_coords = [p[0] for p in points]
            y_coords = [p[1] for p in points]
            
            x_min, x_max = min(x_coords), max(x_coords)
            y_min, y_max = min(y_coords), max(y_coords)
            
            bbox = [x_min, y_min, x_max - x_min, y_max - y_min]
            
            # 创建CardDetection对象
            detection = CardDetection(
                label=label,
                bbox=bbox,
                confidence=shape.get("score", 1.0),
                group_id=shape.get("group_id", 0)
            )
            
            # 保留原始区域信息
            detection.region_name = shape.get("region_name", "")
            detection.owner = shape.get("owner", "")
            
            detections.append(detection)
            
        self.logger.debug(f"转换完成: {len(detections)} 个有效卡牌检测")
        return detections

    def _validate_with_error_handling(self, dual_result: Dict, dt_result) -> Dict:
        """带错误处理的验证"""
        try:
            validation_result = self.validator.validate_comprehensive(
                dual_result['rlcard_format'],
                dual_result['anylabeling_format'],
                dt_result.digital_twin_cards
            )
            return validation_result
        except Exception as e:
            self.logger.warning(f"验证失败: {e}")
            self.stats["validation_errors"] += 1
            # 返回默认验证结果
            return {
                'overall_consistency_score': 0.0,
                'is_consistent': False,
                'detailed_checks': {},
                'issues': [f"验证失败: {e}"],
                'recommendations': []
            }

    def _save_frame_results_complete(self, frame_name: str, original_data: Dict,
                                   dual_result: Dict, validation_result: Dict):
        """保存帧处理结果（完整版本）"""
        output_path = Path(self.config.output_dir)

        # 复制原始图片
        source_image = Path(self.config.source_dir) / "images" / f"{frame_name}.jpg"
        target_image = output_path / "images" / f"{frame_name}.jpg"

        if source_image.exists():
            shutil.copy2(source_image, target_image)
        else:
            self.logger.warning(f"⚠️ 源图片不存在: {source_image}")

        # 保存增强的AnyLabeling格式
        enhanced_anylabeling = self._enhance_anylabeling_format_complete(
            original_data, dual_result['anylabeling_format']
        )

        labels_file = output_path / "labels" / f"{frame_name}.json"
        with open(labels_file, 'w', encoding='utf-8') as f:
            json.dump(enhanced_anylabeling, f, ensure_ascii=False, indent=2)

        # 保存RLCard格式
        rlcard_file = output_path / "rlcard_format" / f"{frame_name}.json"
        with open(rlcard_file, 'w', encoding='utf-8') as f:
            json.dump(dual_result['rlcard_format'], f, ensure_ascii=False, indent=2)

        # 保存验证报告
        validation_file = output_path / "validation_reports" / f"{frame_name}_validation.json"
        validation_data = {
            "frame_name": frame_name,
            "consistency_score": validation_result['overall_consistency_score'],
            "is_valid": validation_result['is_consistent'],
            "validation_details": validation_result.get('detailed_checks', {}),
            "issues": validation_result.get('issues', []),
            "recommendations": validation_result.get('recommendations', []),
            "timestamp": datetime.now().isoformat(),
            "has_cards": len(dual_result['rlcard_format'].get('hand', [])) > 0
        }

        with open(validation_file, 'w', encoding='utf-8') as f:
            json.dump(validation_data, f, ensure_ascii=False, indent=2)

    def _enhance_anylabeling_format_complete(self, original_data: Dict, dt_anylabeling: Dict) -> Dict:
        """增强AnyLabeling格式（完整版本）"""
        enhanced_data = original_data.copy()

        # 如果有数字孪生处理的shapes，使用增强版本
        if dt_anylabeling.get("shapes"):
            enhanced_shapes = []

            # 处理数字孪生系统生成的shapes
            for shape in dt_anylabeling.get("shapes", []):
                enhanced_shape = shape.copy()

                # 添加数字孪生ID信息到attributes
                if "attributes" not in enhanced_shape:
                    enhanced_shape["attributes"] = {}

                # 从label中提取数字孪生ID（格式如"1_二"）
                label = shape.get("label", "")
                if "_" in label:
                    twin_id, card_name = label.split("_", 1)
                    enhanced_shape["attributes"]["digital_twin_id"] = label
                    enhanced_shape["attributes"]["twin_id"] = twin_id
                    enhanced_shape["attributes"]["card_name"] = card_name
                else:
                    enhanced_shape["attributes"]["digital_twin_id"] = label
                    enhanced_shape["attributes"]["card_name"] = label

                # 添加处理时间戳
                enhanced_shape["attributes"]["processed_timestamp"] = datetime.now().isoformat()
                enhanced_shape["attributes"]["processing_version"] = "digital_twin_v2.0"

                enhanced_shapes.append(enhanced_shape)

            enhanced_data["shapes"] = enhanced_shapes

        # 添加处理元数据
        enhanced_data["processing_metadata"] = {
            "original_shapes_count": len(original_data.get("shapes", [])),
            "enhanced_shapes_count": len(enhanced_data.get("shapes", [])),
            "digital_twin_version": "v2.0",
            "processing_timestamp": datetime.now().isoformat(),
            "source_dataset": "calibration_gt",
            "has_digital_twin_processing": len(dt_anylabeling.get("shapes", [])) > 0
        }

        return enhanced_data

    def _generate_final_report(self) -> Dict[str, Any]:
        """生成最终的处理报告"""
        avg_consistency = (
            sum(self.stats["consistency_scores"]) / len(self.stats["consistency_scores"])
            if self.stats["consistency_scores"] else 0.0
        )

        success_rate = (
            self.stats["successful_assignments"] / self.stats["total_cards"]
            if self.stats["total_cards"] > 0 else 0.0
        )

        final_report = {
            "processing_summary": {
                "total_frames": self.stats["total_frames"],
                "processed_frames": self.stats["processed_frames"],
                "processing_success_rate": self.stats["processed_frames"] / self.stats["total_frames"] if self.stats["total_frames"] > 0 else 0.0,
                "frames_with_cards": self.stats["frames_with_cards"],
                "frames_without_cards": self.stats["frames_without_cards"]
            },
            "card_processing": {
                "total_cards_detected": self.stats["total_cards"],
                "successful_id_assignments": self.stats["successful_assignments"],
                "id_assignment_success_rate": success_rate
            },
            "quality_metrics": {
                "average_consistency_score": avg_consistency,
                "validation_errors": self.stats["validation_errors"],
                "error_rate": self.stats["validation_errors"] / self.stats["processed_frames"] if self.stats["processed_frames"] > 0 else 0.0
            },
            "consistency_distribution": {
                "min_score": min(self.stats["consistency_scores"]) if self.stats["consistency_scores"] else 0.0,
                "max_score": max(self.stats["consistency_scores"]) if self.stats["consistency_scores"] else 0.0,
                "scores_above_95": sum(1 for score in self.stats["consistency_scores"] if score >= 0.95),
                "scores_above_90": sum(1 for score in self.stats["consistency_scores"] if score >= 0.90)
            },
            "output_structure": {
                "output_directory": self.config.output_dir,
                "images_generated": self.stats["processed_frames"],
                "labels_generated": self.stats["processed_frames"],
                "rlcard_files_generated": self.stats["processed_frames"],
                "validation_reports_generated": self.stats["processed_frames"]
            },
            "timestamp": datetime.now().isoformat(),
            "configuration": {
                "source_directory": self.config.source_dir,
                "image_dimensions": f"{self.config.image_width}x{self.config.image_height}",
                "valid_card_labels_count": len(self.config.valid_card_labels)
            }
        }

        # 保存总体验证报告
        report_file = Path(self.config.output_dir) / "validation_reports" / "complete_processing_report.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(final_report, f, ensure_ascii=False, indent=2)

        return final_report

def main():
    """主函数 - 自动运行"""
    print("🎯 calibration_gt完整处理器 - 修复版本")
    print("=" * 60)
    print("📋 修复内容:")
    print("   ✅ 处理所有372帧（不跳过任何帧）")
    print("   ✅ 正确使用原始坐标信息")
    print("   ✅ 自动运行，无需用户交互")
    print("=" * 60)

    # 创建配置
    config = CompleteProcessorConfig()

    # 检查源目录
    if not Path(config.source_dir).exists():
        print(f"❌ 源目录不存在: {config.source_dir}")
        return

    print(f"📂 源目录: {config.source_dir}")
    print(f"📁 输出目录: {config.output_dir}")
    print(f"🎴 有效卡牌类别: {len(config.valid_card_labels)} 个")
    print()

    # 创建处理器并执行
    processor = CalibrationGTCompleteProcessor(config)

    try:
        print("🚀 开始处理...")
        final_report = processor.process_complete_dataset()

        print("\n🎉 处理完成!")
        print(f"📊 处理统计:")
        print(f"   - 总帧数: {final_report['processing_summary']['total_frames']}")
        print(f"   - 成功处理: {final_report['processing_summary']['processed_frames']}")
        print(f"   - 有卡牌帧: {final_report['processing_summary']['frames_with_cards']}")
        print(f"   - 无卡牌帧: {final_report['processing_summary']['frames_without_cards']}")
        print(f"   - 总卡牌数: {final_report['card_processing']['total_cards_detected']}")
        print(f"   - ID分配成功率: {final_report['card_processing']['id_assignment_success_rate']:.2%}")
        print(f"   - 平均一致性分数: {final_report['quality_metrics']['average_consistency_score']:.3f}")
        print(f"   - 验证错误数: {final_report['quality_metrics']['validation_errors']}")

        print(f"\n📁 输出文件:")
        print(f"   - 图片: {final_report['output_structure']['images_generated']} 个")
        print(f"   - 标注文件: {final_report['output_structure']['labels_generated']} 个")
        print(f"   - RLCard格式: {final_report['output_structure']['rlcard_files_generated']} 个")
        print(f"   - 验证报告: {final_report['output_structure']['validation_reports_generated']} 个")

        print(f"\n🎉 所有文件已保存到: {config.output_dir}")

    except Exception as e:
        print(f"❌ 处理失败: {e}")
        logger.exception("处理过程中发生错误")

if __name__ == "__main__":
    main()
