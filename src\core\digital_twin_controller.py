"""
数字孪生系统统一主控器 (DigitalTwinController)
提供统一的入口接口，管理所有数字孪生相关功能

设计理念：
1. 统一入口：所有外部调用都通过主控器
2. 策略切换：支持不同处理策略的动态切换
3. 配置集中：统一管理所有模块配置
4. 监控统一：提供统一的性能监控和日志
"""

from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from enum import Enum
import logging

# 导入不同阶段的集成器
from ..modules.phase1_integrator import create_phase1_integrator
from ..modules.phase2_integrator import create_phase2_integrator

# 导入卡牌尺寸启动控制器
from ..modules.card_size_activation_controller import (
    create_card_size_activation_controller,
    CardSizeActivationConfig
)

# 导入游戏边界检测器
from ..modules.game_boundary_detector import (
    create_game_boundary_detector,
    BoundaryType
)

logger = logging.getLogger(__name__)

class ProcessingStrategy(Enum):
    """处理策略枚举"""
    PHASE1_BASIC = "phase1_basic"           # 第一阶段：基础功能
    PHASE2_COMPLETE = "phase2_complete"     # 第二阶段：完整功能
    CUSTOM = "custom"                       # 自定义策略

@dataclass
class DigitalTwinConfig:
    """数字孪生系统配置"""
    strategy: ProcessingStrategy = ProcessingStrategy.PHASE2_COMPLETE
    enable_logging: bool = True
    log_level: str = "INFO"
    performance_monitoring: bool = True
    
    # 模块开关
    enable_inheritance: bool = True
    enable_region_transition: bool = True
    enable_dark_card_processing: bool = True
    enable_occlusion_compensation: bool = True
    
    # 性能配置
    max_cards_per_frame: int = 50
    max_virtual_cards: int = 20
    
    # 输出配置
    dual_output_enabled: bool = True
    preserve_original_data: bool = True

    # 卡牌尺寸启动控制配置
    enable_size_activation_control: bool = True
    size_threshold: float = 0.85
    qualified_ratio_threshold: float = 0.9
    min_card_count: int = 10  # 降低阈值以支持区域流转场景

    # 游戏边界检测配置
    enable_boundary_detection: bool = True
    auto_reset_on_boundary: bool = True
    boundary_detection_logging: bool = True

@dataclass
class ProcessingResult:
    """处理结果统一格式"""
    success: bool
    processed_cards: List[Dict[str, Any]]
    statistics: Dict[str, Any]
    validation_errors: Optional[List[str]] = None
    validation_warnings: Optional[List[str]] = None
    processing_time: float = 0.0
    strategy_used: str = ""

class DigitalTwinController:
    """数字孪生系统统一主控器"""
    
    def __init__(self, config: Optional[DigitalTwinConfig] = None):
        """
        初始化数字孪生主控器
        
        Args:
            config: 系统配置，如果为None则使用默认配置
        """
        self.config = config or DigitalTwinConfig()
        self.current_strategy = self.config.strategy
        
        # 初始化日志
        self._setup_logging()
        
        # 初始化处理器
        self.processors = {}
        self._initialize_processors()

        # 初始化卡牌尺寸启动控制器
        self.size_activation_controller = None
        if self.config.enable_size_activation_control:
            self._initialize_size_activation_controller()

        # 初始化游戏边界检测器
        self.boundary_detector = None
        if self.config.enable_boundary_detection:
            self._initialize_boundary_detector()

        # 🔧 修复：延迟重置状态管理
        self.pending_reset = False
        self.pending_reset_info = None

        # 性能监控
        self.performance_stats = {
            "total_frames_processed": 0,
            "total_cards_processed": 0,
            "average_processing_time": 0.0,
            "error_count": 0,
            "strategy_usage": {},
            "activation_decisions": {
                "total": 0,
                "activated": 0,
                "deactivated": 0
            }
        }
        
        logger.info(f"数字孪生主控器初始化完成，策略: {self.current_strategy.value}")
    
    def _setup_logging(self):
        """设置日志配置"""
        if self.config.enable_logging:
            logging.basicConfig(
                level=getattr(logging, self.config.log_level),
                format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
    
    def _initialize_processors(self):
        """初始化所有处理器"""
        try:
            # 初始化第一阶段处理器
            self.processors[ProcessingStrategy.PHASE1_BASIC] = create_phase1_integrator()
            logger.info("第一阶段处理器初始化完成")
            
            # 初始化第二阶段处理器
            self.processors[ProcessingStrategy.PHASE2_COMPLETE] = create_phase2_integrator()
            logger.info("第二阶段处理器初始化完成")
            
        except Exception as e:
            logger.error(f"处理器初始化失败: {e}")
            raise

    def _initialize_size_activation_controller(self):
        """初始化卡牌尺寸启动控制器"""
        try:
            size_config = CardSizeActivationConfig(
                size_threshold=self.config.size_threshold,
                qualified_ratio_threshold=self.config.qualified_ratio_threshold,
                min_card_count=self.config.min_card_count,
                enable_size_logging=self.config.enable_logging,
                save_activation_decisions=True
            )

            self.size_activation_controller = create_card_size_activation_controller(size_config)
            logger.info("卡牌尺寸启动控制器初始化完成")

        except Exception as e:
            logger.error(f"卡牌尺寸启动控制器初始化失败: {e}")
            # 如果初始化失败，禁用启动控制
            self.config.enable_size_activation_control = False
            self.size_activation_controller = None

    def _initialize_boundary_detector(self):
        """初始化游戏边界检测器"""
        try:
            logger.info("🔧 开始初始化游戏边界检测器...")
            self.boundary_detector = create_game_boundary_detector()
            logger.info("✅ 游戏边界检测器初始化完成")
            logger.info(f"🔧 边界检测配置: 启用={self.config.enable_boundary_detection}, "
                       f"自动重置={self.config.auto_reset_on_boundary}, "
                       f"日志记录={self.config.boundary_detection_logging}")

        except Exception as e:
            logger.error(f"❌ 游戏边界检测器初始化失败: {e}")
            # 如果初始化失败，禁用边界检测
            self.config.enable_boundary_detection = False
            self.boundary_detector = None
    
    def process_frame(self,
                     detections: List[Dict[str, Any]],
                     strategy: Optional[ProcessingStrategy] = None) -> ProcessingResult:
        """
        处理单帧数据 - 统一入口

        Args:
            detections: 检测结果列表
            strategy: 处理策略，如果为None则使用默认策略

        Returns:
            处理结果
        """
        import time
        start_time = time.time()

        # 确定使用的策略
        used_strategy = strategy or self.current_strategy

        try:
            # 🔧 修复：检查是否有待执行的重置
            if self.pending_reset:
                self._execute_pending_reset()

            # 🆕 游戏边界检测（最高优先级）
            if (self.config.enable_boundary_detection and
                self.boundary_detector is not None):

                # 🔧 调试：记录边界检测调用
                if self.config.boundary_detection_logging:
                    all_labels = [d.get('label', '') for d in detections]
                    logger.info(f"🔍 边界检测调用 - 检测到标签: {all_labels}")

                boundary_result = self.boundary_detector.detect_boundary(detections)

                # 🔧 调试：记录边界检测结果
                if self.config.boundary_detection_logging:
                    logger.info(f"🔍 边界检测结果: {boundary_result.boundary_type.value}, "
                               f"应该重置: {boundary_result.should_reset}, "
                               f"触发标签: {boundary_result.trigger_labels}")

                # 🔧 修复：标记延迟重置，而不是立即重置
                if boundary_result.should_reset and self.config.auto_reset_on_boundary:
                    self.pending_reset = True
                    self.pending_reset_info = boundary_result

                    if self.config.boundary_detection_logging:
                        logger.info(f"🔄 边界检测标记延迟重置: {boundary_result.boundary_type.value}, "
                                   f"触发标签: {boundary_result.trigger_labels}, "
                                   f"将在下一帧执行重置")

            elif self.config.boundary_detection_logging:
                # 🔧 调试：记录边界检测未启用的原因
                if not self.config.enable_boundary_detection:
                    logger.warning("⚠️ 边界检测未启用")
                elif self.boundary_detector is None:
                    logger.warning("⚠️ 边界检测器未初始化")

            # 🔧 修复：过滤出卡牌数据用于尺寸启动控制
            card_detections = self._filter_card_detections(detections)

            # 🆕 卡牌尺寸启动控制检查
            if (self.config.enable_size_activation_control and
                self.size_activation_controller is not None):

                activation_decision = self.size_activation_controller.should_activate_digital_twin(card_detections)

                # 更新启动决策统计
                self.performance_stats["activation_decisions"]["total"] += 1
                if activation_decision.should_activate:
                    self.performance_stats["activation_decisions"]["activated"] += 1
                else:
                    self.performance_stats["activation_decisions"]["deactivated"] += 1

                # 🔧 修复：如果不应该启动完整数字孪生功能，执行最小必要处理
                if not activation_decision.should_activate:
                    return self._execute_minimal_essential_processing(card_detections, activation_decision, used_strategy, start_time)

            # 获取对应的处理器
            processor = self.processors.get(used_strategy)
            if not processor:
                raise ValueError(f"不支持的处理策略: {used_strategy}")

            # 执行数字孪生处理（只使用卡牌数据）
            result = processor.process_frame(card_detections)
            
            # 计算处理时间
            processing_time = time.time() - start_time
            
            # 更新性能统计
            self._update_performance_stats(used_strategy, processing_time, len(detections))
            
            # 构建统一结果格式
            unified_result = ProcessingResult(
                success=result.success,
                processed_cards=result.processed_cards,
                statistics=result.statistics,
                validation_errors=getattr(result, 'validation_errors', []),
                validation_warnings=getattr(result, 'validation_warnings', []),
                processing_time=processing_time,
                strategy_used=used_strategy.value
            )
            
            logger.debug(f"帧处理完成，策略: {used_strategy.value}, 耗时: {processing_time:.3f}s")
            return unified_result
            
        except Exception as e:
            self.performance_stats["error_count"] += 1
            logger.error(f"帧处理失败: {e}")
            
            return ProcessingResult(
                success=False,
                processed_cards=[],
                statistics={},
                validation_errors=[str(e)],
                processing_time=time.time() - start_time,
                strategy_used=used_strategy.value
            )
    
    def switch_strategy(self, new_strategy: ProcessingStrategy):
        """
        切换处理策略
        
        Args:
            new_strategy: 新的处理策略
        """
        if new_strategy not in self.processors:
            raise ValueError(f"不支持的处理策略: {new_strategy}")
        
        old_strategy = self.current_strategy
        self.current_strategy = new_strategy
        self.config.strategy = new_strategy
        
        logger.info(f"处理策略已切换: {old_strategy.value} → {new_strategy.value}")
    
    def get_available_strategies(self) -> List[str]:
        """获取可用的处理策略列表"""
        return [strategy.value for strategy in self.processors.keys()]
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计信息"""
        return self.performance_stats.copy()
    
    def get_system_status(self) -> Dict[str, Any]:
        """获取系统状态信息"""
        status = {
            "current_strategy": self.current_strategy.value,
            "available_strategies": self.get_available_strategies(),
            "performance_stats": self.get_performance_stats(),
            "config": {
                "dual_output_enabled": self.config.dual_output_enabled,
                "enable_inheritance": self.config.enable_inheritance,
                "enable_region_transition": self.config.enable_region_transition,
                "enable_dark_card_processing": self.config.enable_dark_card_processing,
                "enable_occlusion_compensation": self.config.enable_occlusion_compensation,
                "enable_size_activation_control": self.config.enable_size_activation_control
            }
        }

        # 添加卡牌尺寸启动控制状态
        if self.size_activation_controller is not None:
            status["size_activation_control"] = {
                "enabled": True,
                "size_threshold": self.config.size_threshold,
                "qualified_ratio_threshold": self.config.qualified_ratio_threshold,
                "min_card_count": self.config.min_card_count,
                "statistics": self.size_activation_controller.get_statistics()
            }
        else:
            status["size_activation_control"] = {"enabled": False}

        return status
    
    def _update_performance_stats(self, strategy: ProcessingStrategy, processing_time: float, card_count: int):
        """更新性能统计"""
        self.performance_stats["total_frames_processed"] += 1
        self.performance_stats["total_cards_processed"] += card_count
        
        # 更新平均处理时间
        total_frames = self.performance_stats["total_frames_processed"]
        current_avg = self.performance_stats["average_processing_time"]
        self.performance_stats["average_processing_time"] = (
            (current_avg * (total_frames - 1) + processing_time) / total_frames
        )
        
        # 更新策略使用统计
        strategy_key = strategy.value
        if strategy_key not in self.performance_stats["strategy_usage"]:
            self.performance_stats["strategy_usage"][strategy_key] = 0
        self.performance_stats["strategy_usage"][strategy_key] += 1

    def _create_passthrough_result(self,
                                  detections: List[Dict[str, Any]],
                                  activation_decision,
                                  strategy: ProcessingStrategy,
                                  start_time: float) -> ProcessingResult:
        """创建数据保留传递结果"""
        import time
        processing_time = time.time() - start_time

        # 更新性能统计（但不计入正常处理）
        self.performance_stats["total_frames_processed"] += 1

        return ProcessingResult(
            success=True,
            processed_cards=detections,  # 直接传递原始数据
            statistics={
                "digital_twin_enabled": False,
                "activation_decision": {
                    "should_activate": activation_decision.should_activate,
                    "reason": activation_decision.reason,
                    "qualified_ratio": activation_decision.qualified_ratio,
                    "card_count": activation_decision.card_count,
                    "size_threshold": self.config.size_threshold,
                    "qualified_ratio_threshold": self.config.qualified_ratio_threshold
                },
                "data_preservation_mode": True,
                "original_data_preserved": True,
                "total_detections": len(detections),
                "processing_mode": "passthrough"
            },
            validation_errors=[],
            validation_warnings=[f"数字孪生未启动: {activation_decision.reason}"],
            processing_time=processing_time,
            strategy_used=f"{strategy.value}_passthrough"
        )
    
    def reset_stats(self):
        """重置性能统计"""
        self.performance_stats = {
            "total_frames_processed": 0,
            "total_cards_processed": 0,
            "average_processing_time": 0.0,
            "error_count": 0,
            "strategy_usage": {}
        }
        logger.info("性能统计已重置")

    def _execute_minimal_essential_processing(self,
                                            detections: List[Dict[str, Any]],
                                            activation_decision: Any,
                                            strategy: ProcessingStrategy,
                                            start_time: float) -> ProcessingResult:
        """
        执行最小必要处理 - 确保核心游戏规则得到满足

        即使启动控制决定不启动完整数字孪生功能，也要执行：
        1. 区域2互斥处理（核心游戏规则）
        2. 基础数据验证

        Args:
            detections: 检测结果列表
            activation_decision: 启动决策结果
            strategy: 处理策略
            start_time: 开始时间

        Returns:
            最小处理结果
        """
        logger.info("执行最小必要处理：确保区域2互斥规则得到满足")

        try:
            # 导入必要的模块
            from src.modules.region2_processor import create_region2_processor
            from src.modules.data_validator import create_data_validator

            # 创建最小必要的处理器
            data_validator = create_data_validator()
            region2_processor = create_region2_processor()

            # 1. 基础数据验证
            validation_result = data_validator.validate(detections)
            if not validation_result.is_valid:
                logger.warning(f"数据验证失败，但继续执行区域2处理: {validation_result.errors}")

            # 2. 区域2互斥处理（核心规则，必须执行）
            region2_result = region2_processor.process_region2_exclusive(detections)

            # 计算处理时间
            import time
            processing_time = time.time() - start_time

            # 构建最小处理结果
            minimal_result = ProcessingResult(
                success=True,
                processed_cards=region2_result.processed_cards,
                statistics={
                    "processing_mode": "minimal_essential",
                    "activation_decision": {
                        "should_activate": activation_decision.should_activate,
                        "reason": getattr(activation_decision, 'reason', 'Unknown'),
                        "size_stats": getattr(activation_decision, 'size_stats', {})
                    },
                    "region2_processing": {
                        "region1_removed": len(region2_result.region1_removed_cards),
                        "region2_inherited": len(region2_result.region2_inherited_cards),
                        "total_processed": len(region2_result.processed_cards)
                    },
                    "data_validation": {
                        "is_valid": validation_result.is_valid,
                        "errors": validation_result.errors if hasattr(validation_result, 'errors') else [],
                        "warnings": validation_result.warnings if hasattr(validation_result, 'warnings') else []
                    }
                },
                validation_errors=[],
                validation_warnings=["执行了最小必要处理模式"],
                processing_time=processing_time,
                strategy_used=f"{strategy.value}_minimal"
            )

            logger.info(f"最小必要处理完成: 区域1删除{len(region2_result.region1_removed_cards)}张, "
                       f"区域2继承{len(region2_result.region2_inherited_cards)}张, 耗时{processing_time:.3f}s")

            return minimal_result

        except Exception as e:
            logger.error(f"最小必要处理失败: {e}")
            # 如果最小处理也失败，返回原始数据保留结果
            return self._create_passthrough_result(detections, activation_decision, strategy, start_time)

    def _filter_card_detections(self, detections: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """过滤出卡牌检测数据，排除UI标签"""
        # 定义有效的卡牌标签
        valid_card_labels = {
            "一", "二", "三", "四", "五", "六", "七", "八", "九", "十",
            "壹", "贰", "叁", "肆", "伍", "陆", "柒", "捌", "玖", "拾",
            "暗"
        }

        card_detections = []
        for detection in detections:
            label = detection.get('label', '')
            # 检查是否为有效卡牌标签
            if any(card_label in label for card_label in valid_card_labels):
                card_detections.append(detection)

        if self.config.boundary_detection_logging:
            total_count = len(detections)
            card_count = len(card_detections)
            ui_count = total_count - card_count
            logger.info(f"🔧 数据过滤: 总计{total_count}个，卡牌{card_count}个，UI标签{ui_count}个")

        return card_detections

    def _execute_pending_reset(self):
        """执行待处理的重置操作"""
        if self.pending_reset and self.pending_reset_info:
            try:
                if self.config.boundary_detection_logging:
                    logger.info(f"🔄 执行延迟重置: {self.pending_reset_info.boundary_type.value}, "
                               f"触发标签: {self.pending_reset_info.trigger_labels}")

                self._reset_all_processors(self.pending_reset_info)

                # 清除待处理状态
                self.pending_reset = False
                self.pending_reset_info = None

                if self.config.boundary_detection_logging:
                    logger.info("✅ 延迟重置执行完成")

            except Exception as e:
                logger.error(f"❌ 执行延迟重置时发生错误: {e}")
                # 清除待处理状态，避免重复错误
                self.pending_reset = False
                self.pending_reset_info = None

    def _reset_all_processors(self, boundary_result):
        """重置所有处理器（用于单局边界）"""
        try:
            reset_count = 0

            # 重置所有策略的处理器
            for strategy, processor in self.processors.items():
                if hasattr(processor, 'reset_system'):
                    processor.reset_system()
                    reset_count += 1
                    logger.debug(f"已重置处理器: {strategy.value}")

            # 重置卡牌尺寸启动控制器的会话状态
            if (self.size_activation_controller and
                hasattr(self.size_activation_controller, 'reset_session')):
                self.size_activation_controller.reset_session()
                logger.debug("已重置卡牌尺寸启动控制器会话状态")

            # 重置性能统计中的帧计数（保留累计统计）
            self.performance_stats["total_frames_processed"] = 0

            logger.info(f"🔄 系统重置完成: 重置了{reset_count}个处理器, "
                       f"边界类型: {boundary_result.boundary_type.value}, "
                       f"检测方法: {boundary_result.detection_method}")

        except Exception as e:
            logger.error(f"系统重置失败: {e}")
            # 重置失败不应该影响正常处理，只记录错误

    def get_boundary_detection_stats(self) -> Dict[str, Any]:
        """获取边界检测统计信息"""
        if self.boundary_detector:
            return self.boundary_detector.get_detection_stats()
        return {}

    def reset_boundary_detection_stats(self):
        """重置边界检测统计信息"""
        if self.boundary_detector:
            self.boundary_detector.reset_stats()
            logger.info("边界检测统计信息已重置")

def create_digital_twin_controller(config: Optional[DigitalTwinConfig] = None) -> DigitalTwinController:
    """
    创建数字孪生主控器的工厂函数
    
    Args:
        config: 系统配置
        
    Returns:
        数字孪生主控器实例
    """
    return DigitalTwinController(config)

# 便捷的默认实例创建函数
def create_default_controller() -> DigitalTwinController:
    """创建使用默认配置的数字孪生主控器"""
    return create_digital_twin_controller()

def create_basic_controller() -> DigitalTwinController:
    """创建使用基础功能的数字孪生主控器"""
    config = DigitalTwinConfig(strategy=ProcessingStrategy.PHASE1_BASIC)
    return create_digital_twin_controller(config)

def create_complete_controller() -> DigitalTwinController:
    """创建使用完整功能的数字孪生主控器"""
    config = DigitalTwinConfig(strategy=ProcessingStrategy.PHASE2_COMPLETE)
    return create_digital_twin_controller(config)

def create_controller_with_size_control(size_threshold: float = 0.85,
                                       qualified_ratio_threshold: float = 0.9,
                                       min_card_count: int = 20) -> DigitalTwinController:
    """创建带有卡牌尺寸启动控制的数字孪生主控器"""
    config = DigitalTwinConfig(
        strategy=ProcessingStrategy.PHASE2_COMPLETE,
        enable_size_activation_control=True,
        size_threshold=size_threshold,
        qualified_ratio_threshold=qualified_ratio_threshold,
        min_card_count=min_card_count,
        # 🔧 修复：明确启用边界检测功能
        enable_boundary_detection=True,
        auto_reset_on_boundary=True,
        boundary_detection_logging=True
    )
    return create_digital_twin_controller(config)
