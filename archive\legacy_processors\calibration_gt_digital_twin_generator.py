#!/usr/bin/env python3
"""
calibration_gt数字孪生ID生成系统

基于现有标注的智能ID分配方案，为calibration_gt数据集生成带数字孪生ID的完整标注文件。
该系统将现有的高质量区域分配转换为完整的数字孪生追踪系统。

核心功能：
1. 数据转换：将calibration_gt标注转换为CardDetection格式
2. 数字孪生处理：使用当前项目的数字孪生系统V2.0进行处理
3. 双轨输出：生成RLCard和AnyLabeling格式的标注文件
4. 质量保证：完整的验证和一致性检查机制

作者：Augment Agent
日期：2025-07-18
版本：1.0.0
"""

import os
import json
import shutil
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass
import logging
from datetime import datetime

# 导入项目核心模块
from src.core.digital_twin_v2 import create_digital_twin_system, CardDetection
from src.core.synchronized_dual_format_validator import SynchronizedDualFormatValidator

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

@dataclass
class CalibrationGTConfig:
    """calibration_gt处理配置"""
    source_dir: str = "legacy_assets/ceshi/calibration_gt"
    output_dir: str = "output/calibration_gt_with_digital_twin"
    image_width: int = 640
    image_height: int = 320
    
    # 有效卡牌类别（21个：20个数值 + 1个暗牌）
    valid_card_labels: List[str] = None
    
    def __post_init__(self):
        if self.valid_card_labels is None:
            self.valid_card_labels = [
                "一", "二", "三", "四", "五", "六", "七", "八", "九", "十",
                "壹", "贰", "叁", "肆", "伍", "陆", "柒", "捌", "玖", "拾",
                "暗"
            ]

class CalibrationGTConverter:
    """calibration_gt标注转换器"""
    
    def __init__(self, config: CalibrationGTConfig):
        self.config = config
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
        
    def convert_anylabeling_to_detection(self, anylabeling_data: Dict) -> List[CardDetection]:
        """
        将AnyLabeling格式转换为CardDetection格式
        
        Args:
            anylabeling_data: AnyLabeling格式的标注数据
            
        Returns:
            CardDetection对象列表
        """
        detections = []
        
        for shape in anylabeling_data.get("shapes", []):
            # 只处理有效卡牌类别
            label = shape.get("label", "")
            if label not in self.config.valid_card_labels:
                self.logger.debug(f"跳过非卡牌类别: {label}")
                continue
                
            # 提取边界框信息
            points = shape.get("points", [])
            if len(points) != 4:
                self.logger.warning(f"无效的边界框点数: {len(points)}")
                continue
                
            # 计算边界框 (x, y, width, height)
            x_coords = [p[0] for p in points]
            y_coords = [p[1] for p in points]
            
            x_min, x_max = min(x_coords), max(x_coords)
            y_min, y_max = min(y_coords), max(y_coords)
            
            bbox = [x_min, y_min, x_max - x_min, y_max - y_min]
            
            # 创建CardDetection对象
            detection = CardDetection(
                label=label,
                bbox=bbox,
                confidence=shape.get("score", 1.0),  # 使用原始置信度
                group_id=shape.get("group_id", 0)
            )

            # 保留原始区域信息
            detection.region_name = shape.get("region_name", "")
            detection.owner = shape.get("owner", "")
            
            detections.append(detection)
            
        self.logger.info(f"转换完成: {len(detections)} 个有效卡牌检测")
        return detections

class CalibrationGTProcessor:
    """calibration_gt批量处理器"""
    
    def __init__(self, config: CalibrationGTConfig):
        self.config = config
        self.converter = CalibrationGTConverter(config)
        self.digital_twin_system = create_digital_twin_system()
        self.validator = SynchronizedDualFormatValidator()
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
        
        # 处理统计
        self.stats = {
            "total_frames": 0,
            "processed_frames": 0,
            "total_cards": 0,
            "successful_assignments": 0,
            "validation_errors": 0,
            "consistency_scores": []
        }
        
    def process_dataset(self) -> Dict[str, Any]:
        """
        处理整个calibration_gt数据集
        
        Returns:
            处理结果报告
        """
        self.logger.info("开始处理calibration_gt数据集...")
        
        # 创建输出目录结构
        self._create_output_structure()
        
        # 获取所有标注文件
        label_files = self._get_label_files()
        self.stats["total_frames"] = len(label_files)
        
        self.logger.info(f"找到 {len(label_files)} 个标注文件")
        
        # 按帧序列处理
        for frame_file in sorted(label_files):
            try:
                self._process_single_frame(frame_file)
                self.stats["processed_frames"] += 1
            except Exception as e:
                self.logger.error(f"处理帧 {frame_file} 失败: {e}")
                continue
                
        # 生成验证报告
        validation_report = self._generate_validation_report()

        self.logger.info("calibration_gt数据集处理完成")
        return validation_report

    def _create_output_structure(self):
        """创建输出目录结构"""
        output_path = Path(self.config.output_dir)

        # 创建主要目录
        directories = [
            output_path,
            output_path / "images",
            output_path / "labels",
            output_path / "rlcard_format",
            output_path / "validation_reports"
        ]

        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)

        self.logger.info(f"输出目录结构创建完成: {output_path}")

    def _get_label_files(self) -> List[Path]:
        """获取所有标注文件"""
        labels_dir = Path(self.config.source_dir) / "labels"
        if not labels_dir.exists():
            raise FileNotFoundError(f"标注目录不存在: {labels_dir}")

        label_files = list(labels_dir.glob("*.json"))
        return label_files

    def _process_single_frame(self, frame_file: Path):
        """
        处理单个帧文件

        Args:
            frame_file: 标注文件路径
        """
        frame_name = frame_file.stem
        self.logger.debug(f"处理帧: {frame_name}")

        # 读取原始标注
        with open(frame_file, 'r', encoding='utf-8') as f:
            anylabeling_data = json.load(f)

        # 转换为CardDetection格式
        detections = self.converter.convert_anylabeling_to_detection(anylabeling_data)

        if not detections:
            self.logger.warning(f"帧 {frame_name} 没有有效的卡牌检测")
            return

        # 使用数字孪生系统处理
        dt_result = self.digital_twin_system.process_frame(detections)

        # 生成双轨输出
        dual_result = self.digital_twin_system.export_synchronized_dual_format(
            dt_result,
            self.config.image_width,
            self.config.image_height,
            anylabeling_data.get("imagePath", f"{frame_name}.jpg")
        )

        # 验证一致性
        validation_result = self.validator.validate_comprehensive(
            dual_result['rlcard_format'],
            dual_result['anylabeling_format'],
            dt_result.digital_twin_cards
        )

        # 更新统计
        self.stats["total_cards"] += len(detections)
        self.stats["successful_assignments"] += len(dt_result.digital_twin_cards)
        self.stats["consistency_scores"].append(validation_result['overall_consistency_score'])

        if not validation_result['is_consistent']:
            self.stats["validation_errors"] += 1
            self.logger.warning(f"帧 {frame_name} 一致性验证失败: {validation_result['overall_consistency_score']}")

        # 保存结果
        self._save_frame_results(frame_name, anylabeling_data, dual_result, validation_result)

    def _save_frame_results(self, frame_name: str, original_data: Dict,
                           dual_result: Dict, validation_result: Any):
        """
        保存帧处理结果

        Args:
            frame_name: 帧名称
            original_data: 原始标注数据
            dual_result: 双轨输出结果
            validation_result: 验证结果
        """
        output_path = Path(self.config.output_dir)

        # 复制原始图片
        source_image = Path(self.config.source_dir) / "images" / f"{frame_name}.jpg"
        target_image = output_path / "images" / f"{frame_name}.jpg"

        if source_image.exists():
            shutil.copy2(source_image, target_image)

        # 保存增强的AnyLabeling格式（包含数字孪生ID）
        enhanced_anylabeling = self._enhance_anylabeling_format(
            original_data, dual_result['anylabeling_format']
        )

        labels_file = output_path / "labels" / f"{frame_name}.json"
        with open(labels_file, 'w', encoding='utf-8') as f:
            json.dump(enhanced_anylabeling, f, ensure_ascii=False, indent=2)

        # 保存RLCard格式
        rlcard_file = output_path / "rlcard_format" / f"{frame_name}.json"
        with open(rlcard_file, 'w', encoding='utf-8') as f:
            json.dump(dual_result['rlcard_format'], f, ensure_ascii=False, indent=2)

        # 保存验证报告
        validation_file = output_path / "validation_reports" / f"{frame_name}_validation.json"
        validation_data = {
            "frame_name": frame_name,
            "consistency_score": validation_result['overall_consistency_score'],
            "is_valid": validation_result['is_consistent'],
            "validation_details": validation_result.get('detailed_checks', {}),
            "issues": validation_result.get('issues', []),
            "recommendations": validation_result.get('recommendations', []),
            "timestamp": datetime.now().isoformat()
        }

        with open(validation_file, 'w', encoding='utf-8') as f:
            json.dump(validation_data, f, ensure_ascii=False, indent=2)

    def _enhance_anylabeling_format(self, original_data: Dict, dt_anylabeling: Dict) -> Dict:
        """
        增强AnyLabeling格式，添加数字孪生ID信息

        Args:
            original_data: 原始AnyLabeling数据
            dt_anylabeling: 数字孪生系统生成的AnyLabeling格式

        Returns:
            增强后的AnyLabeling格式
        """
        enhanced_data = original_data.copy()
        enhanced_shapes = []

        # 处理数字孪生系统生成的shapes
        for shape in dt_anylabeling.get("shapes", []):
            enhanced_shape = shape.copy()

            # 添加数字孪生ID信息到attributes
            if "attributes" not in enhanced_shape:
                enhanced_shape["attributes"] = {}

            # 从label中提取数字孪生ID（格式如"1_二"）
            label = shape.get("label", "")
            if "_" in label:
                twin_id, card_name = label.split("_", 1)
                enhanced_shape["attributes"]["digital_twin_id"] = label
                enhanced_shape["attributes"]["twin_id"] = twin_id
                enhanced_shape["attributes"]["card_name"] = card_name
            else:
                enhanced_shape["attributes"]["digital_twin_id"] = label
                enhanced_shape["attributes"]["card_name"] = label

            # 添加处理时间戳
            enhanced_shape["attributes"]["processed_timestamp"] = datetime.now().isoformat()
            enhanced_shape["attributes"]["processing_version"] = "digital_twin_v2.0"

            enhanced_shapes.append(enhanced_shape)

        enhanced_data["shapes"] = enhanced_shapes

        # 添加处理元数据
        enhanced_data["processing_metadata"] = {
            "original_shapes_count": len(original_data.get("shapes", [])),
            "enhanced_shapes_count": len(enhanced_shapes),
            "digital_twin_version": "v2.0",
            "processing_timestamp": datetime.now().isoformat(),
            "source_dataset": "calibration_gt"
        }

        return enhanced_data

    def _generate_validation_report(self) -> Dict[str, Any]:
        """
        生成完整的验证报告

        Returns:
            验证报告字典
        """
        avg_consistency = (
            sum(self.stats["consistency_scores"]) / len(self.stats["consistency_scores"])
            if self.stats["consistency_scores"] else 0.0
        )

        success_rate = (
            self.stats["successful_assignments"] / self.stats["total_cards"]
            if self.stats["total_cards"] > 0 else 0.0
        )

        validation_report = {
            "processing_summary": {
                "total_frames": self.stats["total_frames"],
                "processed_frames": self.stats["processed_frames"],
                "processing_success_rate": self.stats["processed_frames"] / self.stats["total_frames"] if self.stats["total_frames"] > 0 else 0.0
            },
            "card_processing": {
                "total_cards_detected": self.stats["total_cards"],
                "successful_id_assignments": self.stats["successful_assignments"],
                "id_assignment_success_rate": success_rate
            },
            "quality_metrics": {
                "average_consistency_score": avg_consistency,
                "validation_errors": self.stats["validation_errors"],
                "error_rate": self.stats["validation_errors"] / self.stats["processed_frames"] if self.stats["processed_frames"] > 0 else 0.0
            },
            "consistency_distribution": {
                "min_score": min(self.stats["consistency_scores"]) if self.stats["consistency_scores"] else 0.0,
                "max_score": max(self.stats["consistency_scores"]) if self.stats["consistency_scores"] else 0.0,
                "scores_above_95": sum(1 for score in self.stats["consistency_scores"] if score >= 0.95),
                "scores_above_90": sum(1 for score in self.stats["consistency_scores"] if score >= 0.90)
            },
            "output_structure": {
                "output_directory": self.config.output_dir,
                "images_generated": self.stats["processed_frames"],
                "labels_generated": self.stats["processed_frames"],
                "rlcard_files_generated": self.stats["processed_frames"],
                "validation_reports_generated": self.stats["processed_frames"]
            },
            "timestamp": datetime.now().isoformat(),
            "configuration": {
                "source_directory": self.config.source_dir,
                "image_dimensions": f"{self.config.image_width}x{self.config.image_height}",
                "valid_card_labels_count": len(self.config.valid_card_labels)
            }
        }

        # 保存总体验证报告
        report_file = Path(self.config.output_dir) / "validation_reports" / "overall_validation_report.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(validation_report, f, ensure_ascii=False, indent=2)

        return validation_report

def main():
    """主函数"""
    print("🎯 calibration_gt数字孪生ID生成系统")
    print("=" * 50)

    # 创建配置
    config = CalibrationGTConfig()

    # 检查源目录
    if not Path(config.source_dir).exists():
        print(f"❌ 源目录不存在: {config.source_dir}")
        return

    print(f"📂 源目录: {config.source_dir}")
    print(f"📁 输出目录: {config.output_dir}")
    print(f"🎴 有效卡牌类别: {len(config.valid_card_labels)} 个")

    # 创建处理器并执行
    processor = CalibrationGTProcessor(config)

    try:
        validation_report = processor.process_dataset()

        print("\n✅ 处理完成!")
        print(f"📊 处理统计:")
        print(f"   - 总帧数: {validation_report['processing_summary']['total_frames']}")
        print(f"   - 成功处理: {validation_report['processing_summary']['processed_frames']}")
        print(f"   - 总卡牌数: {validation_report['card_processing']['total_cards_detected']}")
        print(f"   - ID分配成功率: {validation_report['card_processing']['id_assignment_success_rate']:.2%}")
        print(f"   - 平均一致性分数: {validation_report['quality_metrics']['average_consistency_score']:.3f}")
        print(f"   - 验证错误数: {validation_report['quality_metrics']['validation_errors']}")

        print(f"\n📁 输出文件:")
        print(f"   - 图片: {validation_report['output_structure']['images_generated']} 个")
        print(f"   - 标注文件: {validation_report['output_structure']['labels_generated']} 个")
        print(f"   - RLCard格式: {validation_report['output_structure']['rlcard_files_generated']} 个")
        print(f"   - 验证报告: {validation_report['output_structure']['validation_reports_generated']} 个")

        print(f"\n🎉 所有文件已保存到: {config.output_dir}")

    except Exception as e:
        print(f"❌ 处理失败: {e}")
        logger.exception("处理过程中发生错误")

if __name__ == "__main__":
    main()
