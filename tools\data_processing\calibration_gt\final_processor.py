#!/usr/bin/env python3
"""
calibration_gt最终处理器 - 生产级数据处理工具

功能：
1. 处理calibration_gt数据集
2. 生成双格式输出 (RLCard + AnyLabeling)
3. 完整的数字孪生ID分配
4. 坐标格式修复和验证

使用方法：
    python tools/data_processing/calibration_gt/final_processor.py
    python tools/data_processing/calibration_gt/final_processor.py --input_dir data/calibration_gt/images
"""

import os
import json
import shutil
import sys
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass
import logging
from datetime import datetime
import argparse

# 添加项目根目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(os.path.dirname(current_dir)))
sys.path.insert(0, project_root)

# 导入项目核心模块
from src.core.digital_twin_v2 import create_digital_twin_system, CardDetection

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

@dataclass
class ProcessingConfig:
    """处理配置"""
    input_dir: str = "data/calibration_gt/images"
    output_dir: str = "output/calibration_gt_processed"
    confidence_threshold: float = 0.25
    iou_threshold: float = 0.45
    enable_gpu: bool = True
    batch_size: int = 1
    save_failed_frames: bool = True
    enable_validation: bool = True

class CalibrationGTFinalProcessor:
    """calibration_gt最终处理器"""
    
    def __init__(self, config: ProcessingConfig):
        self.config = config
        self.dt_system = create_digital_twin_system()
        self.stats = {
            'total_processed': 0,
            'successful': 0,
            'failed': 0,
            'total_cards': 0,
            'total_twin_ids': 0
        }
        
        # 创建输出目录
        os.makedirs(config.output_dir, exist_ok=True)
        if config.save_failed_frames:
            os.makedirs(os.path.join(config.output_dir, "failed_frames"), exist_ok=True)
    
    def process_single_image(self, image_path: str) -> Dict[str, Any]:
        """处理单张图像"""
        try:
            # 这里应该调用检测器，但为了示例，我们创建模拟数据
            # 实际使用时需要集成YOLO检测器
            
            # 模拟检测结果
            detections = [
                CardDetection("二", [100, 100, 150, 150], 0.95, 1, "手牌_观战方", "spectator"),
                CardDetection("三", [200, 100, 250, 150], 0.90, 2, "手牌_观战方", "spectator"),
            ]
            
            # 数字孪生处理
            result = self.dt_system.process_frame(detections)
            
            # 获取图像尺寸 (实际使用时从图像文件获取)
            image_width, image_height = 640, 320
            
            # 双轨输出
            dual_result = self.dt_system.export_synchronized_dual_format(
                result, image_width, image_height, image_path
            )
            
            # 更新统计
            self.stats['successful'] += 1
            self.stats['total_cards'] += len(result.get('digital_twin_cards', []))
            
            return {
                'success': True,
                'result': dual_result,
                'stats': {
                    'cards_processed': len(result.get('digital_twin_cards', [])),
                    'consistency_score': dual_result['consistency_validation'].get('consistency_score', 0)
                }
            }
            
        except Exception as e:
            logger.error(f"处理图像失败 {image_path}: {e}")
            self.stats['failed'] += 1
            return {
                'success': False,
                'error': str(e),
                'image_path': image_path
            }
    
    def process_batch(self) -> Dict[str, Any]:
        """批量处理"""
        logger.info(f"开始处理 calibration_gt 数据集")
        logger.info(f"输入目录: {self.config.input_dir}")
        logger.info(f"输出目录: {self.config.output_dir}")
        
        # 获取所有图像文件
        image_extensions = {'.jpg', '.jpeg', '.png', '.bmp'}
        image_files = []
        
        if os.path.exists(self.config.input_dir):
            for file_path in Path(self.config.input_dir).rglob('*'):
                if file_path.suffix.lower() in image_extensions:
                    image_files.append(str(file_path))
        
        if not image_files:
            logger.warning(f"在 {self.config.input_dir} 中未找到图像文件")
            return {'success': False, 'error': '未找到图像文件'}
        
        logger.info(f"找到 {len(image_files)} 个图像文件")
        
        # 处理结果存储
        results = []
        failed_frames = []
        
        # 批量处理
        for i, image_path in enumerate(image_files):
            self.stats['total_processed'] += 1
            
            logger.info(f"处理进度: {i+1}/{len(image_files)} - {os.path.basename(image_path)}")
            
            # 处理单张图像
            result = self.process_single_image(image_path)
            
            if result['success']:
                results.append(result)
                
                # 保存结果文件
                self._save_result_files(image_path, result['result'])
                
            else:
                failed_frames.append(result)
                
                # 保存失败信息
                if self.config.save_failed_frames:
                    self._save_failed_frame(result)
        
        # 生成最终报告
        final_report = self._generate_final_report(results, failed_frames)
        
        # 保存报告
        report_path = os.path.join(self.config.output_dir, "processing_report.json")
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(final_report, f, ensure_ascii=False, indent=2)
        
        logger.info(f"处理完成！报告已保存到: {report_path}")
        
        return final_report
    
    def _save_result_files(self, image_path: str, dual_result: Dict[str, Any]):
        """保存结果文件"""
        base_name = os.path.splitext(os.path.basename(image_path))[0]
        
        # 保存RLCard格式
        rlcard_path = os.path.join(self.config.output_dir, f"{base_name}_rlcard.json")
        with open(rlcard_path, 'w', encoding='utf-8') as f:
            json.dump(dual_result['rlcard_format'], f, ensure_ascii=False, indent=2)
        
        # 保存AnyLabeling格式
        anylabeling_path = os.path.join(self.config.output_dir, f"{base_name}_anylabeling.json")
        with open(anylabeling_path, 'w', encoding='utf-8') as f:
            json.dump(dual_result['anylabeling_format'], f, ensure_ascii=False, indent=2)
        
        # 保存一致性验证
        validation_path = os.path.join(self.config.output_dir, f"{base_name}_validation.json")
        with open(validation_path, 'w', encoding='utf-8') as f:
            json.dump(dual_result['consistency_validation'], f, ensure_ascii=False, indent=2)
    
    def _save_failed_frame(self, failed_result: Dict[str, Any]):
        """保存失败帧信息"""
        failed_dir = os.path.join(self.config.output_dir, "failed_frames")
        base_name = os.path.splitext(os.path.basename(failed_result['image_path']))[0]
        
        failed_path = os.path.join(failed_dir, f"{base_name}_error.json")
        with open(failed_path, 'w', encoding='utf-8') as f:
            json.dump(failed_result, f, ensure_ascii=False, indent=2)
    
    def _generate_final_report(self, results: List[Dict], failed_frames: List[Dict]) -> Dict[str, Any]:
        """生成最终报告"""
        total_consistency_score = sum(
            r['stats']['consistency_score'] for r in results if r['stats']['consistency_score'] > 0
        )
        avg_consistency = total_consistency_score / len(results) if results else 0
        
        return {
            'processing_summary': {
                'timestamp': datetime.now().isoformat(),
                'total_processed': self.stats['total_processed'],
                'successful': self.stats['successful'],
                'failed': self.stats['failed'],
                'success_rate': self.stats['successful'] / self.stats['total_processed'] if self.stats['total_processed'] > 0 else 0
            },
            'data_statistics': {
                'total_cards_processed': self.stats['total_cards'],
                'average_consistency_score': avg_consistency,
                'total_twin_ids_assigned': self.stats['total_twin_ids']
            },
            'configuration': {
                'input_dir': self.config.input_dir,
                'output_dir': self.config.output_dir,
                'confidence_threshold': self.config.confidence_threshold,
                'iou_threshold': self.config.iou_threshold,
                'enable_gpu': self.config.enable_gpu
            },
            'failed_frames': [f['image_path'] for f in failed_frames] if failed_frames else []
        }

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='calibration_gt最终处理器')
    parser.add_argument('--input_dir', default='data/calibration_gt/images', 
                       help='输入图像目录')
    parser.add_argument('--output_dir', default='output/calibration_gt_processed',
                       help='输出目录')
    parser.add_argument('--confidence_threshold', type=float, default=0.25,
                       help='置信度阈值')
    parser.add_argument('--iou_threshold', type=float, default=0.45,
                       help='IoU阈值')
    parser.add_argument('--disable_gpu', action='store_true',
                       help='禁用GPU')
    parser.add_argument('--batch_size', type=int, default=1,
                       help='批处理大小')
    
    args = parser.parse_args()
    
    # 创建配置
    config = ProcessingConfig(
        input_dir=args.input_dir,
        output_dir=args.output_dir,
        confidence_threshold=args.confidence_threshold,
        iou_threshold=args.iou_threshold,
        enable_gpu=not args.disable_gpu,
        batch_size=args.batch_size
    )
    
    # 创建处理器并执行
    processor = CalibrationGTFinalProcessor(config)
    result = processor.process_batch()
    
    # 输出结果
    if result.get('success', True):
        print(f"✅ 处理完成！")
        print(f"📊 成功率: {result['processing_summary']['success_rate']:.2%}")
        print(f"📁 输出目录: {config.output_dir}")
    else:
        print(f"❌ 处理失败: {result.get('error', '未知错误')}")
        sys.exit(1)

if __name__ == "__main__":
    main()
