#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试素材分析工具
分析各个数据集的统计信息，帮助更好地利用测试素材
"""

import os
import json
import cv2
import numpy as np
from pathlib import Path
from collections import defaultdict, Counter
from typing import Dict, List, Tuple, Any
import matplotlib.pyplot as plt
import seaborn as sns

class DatasetAnalyzer:
    """测试素材分析器"""
    
    def __init__(self, base_path: str = "legacy_assets/ceshi"):
        """
        初始化分析器
        
        Args:
            base_path: 测试素材根目录
        """
        self.base_path = Path(base_path)
        self.results = {}
        
    def analyze_all_datasets(self):
        """分析所有数据集"""
        print("🔍 开始分析所有测试素材...")
        
        # 分析各个数据集
        self.analyze_calibration_gt()
        self.analyze_zhuangtaiquyu()
        self.analyze_tupian()
        self.analyze_shipin()
        
        # 生成综合报告
        self.generate_summary_report()
        
        print("✅ 分析完成！")
        
    def analyze_calibration_gt(self):
        """分析calibration_gt数据集"""
        print("\n📊 分析calibration_gt数据集...")
        
        images_path = self.base_path / "calibration_gt" / "images"
        labels_path = self.base_path / "calibration_gt" / "labels"
        
        if not images_path.exists() or not labels_path.exists():
            print("❌ calibration_gt数据集不存在")
            return
        
        # 统计图片信息
        image_files = list(images_path.glob("*.jpg"))
        label_files = list(labels_path.glob("*.json"))
        
        # 分析标注信息
        label_stats = self._analyze_labels(label_files, "calibration_gt")
        
        # 分析图片信息
        image_stats = self._analyze_images(image_files[:10])  # 只分析前10张图片以节省时间
        
        self.results["calibration_gt"] = {
            "type": "完整视频训练集（AI推理+人工审核）",
            "image_count": len(image_files),
            "label_count": len(label_files),
            "coverage": len(label_files) / len(image_files) if image_files else 0,
            "label_stats": label_stats,
            "image_stats": image_stats,
            "features": [
                "连续帧序列",
                "AI推理+人工审核",
                "包含遮挡卡牌标注",
                "缺少状态区域字段",
                "缺少物理卡牌ID"
            ]
        }
        
        print(f"   📁 图片数量: {len(image_files)}")
        print(f"   📋 标注数量: {len(label_files)}")
        print(f"   📊 标注覆盖率: {len(label_files) / len(image_files) * 100:.1f}%")
        
    def analyze_zhuangtaiquyu(self):
        """分析zhuangtaiquyu数据集"""
        print("\n📊 分析zhuangtaiquyu数据集...")
        
        images_path = self.base_path / "zhuangtaiquyu" / "images" / "train"
        labels_path = self.base_path / "zhuangtaiquyu" / "labels" / "train"
        
        if not images_path.exists() or not labels_path.exists():
            print("❌ zhuangtaiquyu数据集不存在")
            return
        
        # 统计各个区域的数据
        region_stats = {}
        total_images = 0
        total_labels = 0
        all_label_files = []
        
        for region_dir in images_path.iterdir():
            if region_dir.is_dir():
                region_id = region_dir.name
                region_images = list(region_dir.glob("*.jpg"))
                region_labels_path = labels_path / region_id
                region_labels = list(region_labels_path.glob("*.json")) if region_labels_path.exists() else []
                
                region_stats[region_id] = {
                    "images": len(region_images),
                    "labels": len(region_labels),
                    "coverage": len(region_labels) / len(region_images) if region_images else 0
                }
                
                total_images += len(region_images)
                total_labels += len(region_labels)
                all_label_files.extend(region_labels)
        
        # 分析标注信息
        label_stats = self._analyze_labels(all_label_files[:50], "zhuangtaiquyu")  # 只分析前50个文件
        
        self.results["zhuangtaiquyu"] = {
            "type": "状态区域标注集（纯人工标注）",
            "total_images": total_images,
            "total_labels": total_labels,
            "region_count": len(region_stats),
            "region_stats": region_stats,
            "label_stats": label_stats,
            "features": [
                "纯人工标注",
                "按区域分组",
                "包含状态区域字段",
                "包含物理卡牌唯一ID",
                "状态区域准确率99%",
                "物理卡牌ID准确率90%"
            ]
        }
        
        print(f"   📁 总图片数量: {total_images}")
        print(f"   📋 总标注数量: {total_labels}")
        print(f"   🗂️  区域数量: {len(region_stats)}")
        
    def analyze_tupian(self):
        """分析tupian数据集"""
        print("\n📊 分析tupian数据集...")
        
        tupian_path = self.base_path / "tupian"
        
        if not tupian_path.exists():
            print("❌ tupian数据集不存在")
            return
        
        image_files = list(tupian_path.glob("*.jpg"))
        
        # 分析关键帧类型
        key_frame_types = self._classify_key_frames(image_files)
        
        # 分析图片信息
        image_stats = self._analyze_images(image_files)
        
        self.results["tupian"] = {
            "type": "关键帧图片（源视频抽帧）",
            "image_count": len(image_files),
            "key_frame_types": key_frame_types,
            "image_stats": image_stats,
            "features": [
                "关键游戏状态帧",
                "快速测试用",
                "与calibration_gt相同来源",
                "数量较少但代表性强"
            ]
        }
        
        print(f"   📁 图片数量: {len(image_files)}")
        print(f"   🎯 关键帧类型: {len(key_frame_types)}")
        
    def analyze_shipin(self):
        """分析shipin数据集"""
        print("\n📊 分析shipin数据集...")
        
        shipin_path = self.base_path / "shipin"
        
        if not shipin_path.exists():
            print("❌ shipin数据集不存在")
            return
        
        video_files = list(shipin_path.glob("*.mp4"))
        json_files = list(shipin_path.glob("*.json"))
        
        video_stats = {}
        for video_file in video_files:
            try:
                cap = cv2.VideoCapture(str(video_file))
                frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
                fps = cap.get(cv2.CAP_PROP_FPS)
                width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
                height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
                duration = frame_count / fps if fps > 0 else 0
                cap.release()
                
                video_stats[video_file.name] = {
                    "frame_count": frame_count,
                    "fps": fps,
                    "resolution": f"{width}x{height}",
                    "duration": duration
                }
            except Exception as e:
                print(f"   ⚠️ 无法分析视频 {video_file.name}: {e}")
        
        self.results["shipin"] = {
            "type": "源视频文件",
            "video_count": len(video_files),
            "json_count": len(json_files),
            "video_stats": video_stats,
            "features": [
                "原始游戏录制",
                "完整游戏流程",
                "实时处理测试用",
                "性能压力测试用"
            ]
        }
        
        print(f"   🎬 视频数量: {len(video_files)}")
        print(f"   📄 JSON数量: {len(json_files)}")
        
    def _analyze_labels(self, label_files: List[Path], dataset_type: str) -> Dict:
        """分析标注文件"""
        if not label_files:
            return {}
        
        label_counts = Counter()
        group_counts = Counter()
        region_counts = Counter()
        score_stats = []
        total_annotations = 0
        
        for label_file in label_files[:20]:  # 只分析前20个文件以节省时间
            try:
                with open(label_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                shapes = data.get('shapes', [])
                total_annotations += len(shapes)
                
                for shape in shapes:
                    label = shape.get('label', '')
                    group_id = shape.get('group_id', 0)
                    score = shape.get('score')
                    
                    # 统计标签
                    if dataset_type == "zhuangtaiquyu" and label:
                        # 提取物理卡牌ID和标签
                        if len(label) > 1:
                            physical_id = label[0]
                            card_label = label[1:]
                            label_counts[f"ID{physical_id}_{card_label}"] += 1
                    else:
                        label_counts[label] += 1
                    
                    # 统计组ID
                    group_counts[group_id] += 1
                    
                    # 统计区域
                    region_name = shape.get('region_name', 'unknown')
                    region_counts[region_name] += 1
                    
                    # 统计分数
                    if score is not None:
                        score_stats.append(score)
                        
            except Exception as e:
                print(f"   ⚠️ 无法解析标注文件 {label_file.name}: {e}")
        
        return {
            "total_annotations": total_annotations,
            "avg_annotations_per_file": total_annotations / len(label_files) if label_files else 0,
            "label_distribution": dict(label_counts.most_common(10)),
            "group_distribution": dict(group_counts),
            "region_distribution": dict(region_counts),
            "score_stats": {
                "count": len(score_stats),
                "mean": np.mean(score_stats) if score_stats else 0,
                "std": np.std(score_stats) if score_stats else 0,
                "min": np.min(score_stats) if score_stats else 0,
                "max": np.max(score_stats) if score_stats else 0
            } if score_stats else None
        }
    
    def _analyze_images(self, image_files: List[Path]) -> Dict:
        """分析图片信息"""
        if not image_files:
            return {}
        
        resolutions = []
        file_sizes = []
        
        for img_file in image_files[:5]:  # 只分析前5张图片
            try:
                img = cv2.imread(str(img_file))
                if img is not None:
                    h, w = img.shape[:2]
                    resolutions.append(f"{w}x{h}")
                
                file_size = img_file.stat().st_size / 1024  # KB
                file_sizes.append(file_size)
                
            except Exception as e:
                print(f"   ⚠️ 无法分析图片 {img_file.name}: {e}")
        
        return {
            "sample_count": len(image_files),
            "resolutions": list(set(resolutions)),
            "avg_file_size_kb": np.mean(file_sizes) if file_sizes else 0,
            "total_size_mb": sum(file_sizes) / 1024 if file_sizes else 0
        }
    
    def _classify_key_frames(self, image_files: List[Path]) -> Dict:
        """分类关键帧类型"""
        key_frame_types = {}
        
        for img_file in image_files:
            frame_num = img_file.stem.replace('frame_', '')
            
            if frame_num == "00000":
                key_frame_types["游戏开始"] = img_file.name
            elif frame_num in ["00025", "00026", "00027", "00028", "00029", "00030", "00031", "00032"]:
                key_frame_types["打鸟选择"] = img_file.name
            elif frame_num == "00247":
                key_frame_types["牌局进行中"] = img_file.name
            elif frame_num == "00371":
                key_frame_types["牌局结束"] = img_file.name
            else:
                key_frame_types.setdefault("其他关键帧", []).append(img_file.name)
        
        return key_frame_types
    
    def generate_summary_report(self):
        """生成综合报告"""
        print("\n" + "="*60)
        print("📋 测试素材综合分析报告")
        print("="*60)
        
        for dataset_name, data in self.results.items():
            print(f"\n🗂️  {dataset_name.upper()}")
            print(f"   类型: {data['type']}")
            
            if 'image_count' in data:
                print(f"   图片数量: {data['image_count']}")
            if 'total_images' in data:
                print(f"   总图片数量: {data['total_images']}")
            if 'video_count' in data:
                print(f"   视频数量: {data['video_count']}")
            
            print(f"   特点: {', '.join(data['features'][:3])}...")
        
        # 推荐测试策略
        print(f"\n🎯 推荐测试策略:")
        print(f"   1. 快速验证: 使用tupian（{self.results.get('tupian', {}).get('image_count', 0)}张关键帧）")
        print(f"   2. 完整测试: 使用calibration_gt（{self.results.get('calibration_gt', {}).get('image_count', 0)}张连续帧）")
        print(f"   3. 高级功能: 使用zhuangtaiquyu（{self.results.get('zhuangtaiquyu', {}).get('total_images', 0)}张状态标注）")
        print(f"   4. 性能测试: 使用shipin（{self.results.get('shipin', {}).get('video_count', 0)}个视频文件）")
        
    def save_report(self, output_path: str = "dataset_analysis_report.json"):
        """保存分析报告"""
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(self.results, f, ensure_ascii=False, indent=2, default=str)
        print(f"\n💾 分析报告已保存至: {output_path}")

def main():
    """主函数"""
    print("🔍 测试素材分析工具")
    print("="*40)
    
    # 检查基础路径
    base_path = "legacy_assets/ceshi"
    if not os.path.exists(base_path):
        print(f"❌ 测试素材目录不存在: {base_path}")
        print("请确保在项目根目录运行此脚本")
        return
    
    # 创建分析器并运行分析
    analyzer = DatasetAnalyzer(base_path)
    analyzer.analyze_all_datasets()
    
    # 保存报告
    analyzer.save_report()
    
    print("\n🎉 分析完成！")
    print("📖 详细信息请查看: docs/testing/测试素材详细介绍.md")

if __name__ == "__main__":
    main()
