# phz-ai-simple 硬件配置文档 (v1.1, 更新日期: 20250712)

## 当前硬件概述
- GPU: NVIDIA GeForce RTX 5060 (支持 CUDA 12.9, 显存 8GB GDDR7, 带宽 448 GB/s)
- CPU: Intel Core i9-13900HKES (14 核 14 线程，禁用超线程)
- 内存: 64GB DDR4
- 存储: M.2 NVMe SSD (PCIe 4.0 x8, 容量 1TB) – 用于程序运行及临时数据缓存; HDD (容量 4TB) – 长期存储训练数据和模型

## 环境验证
- NVIDIA 驱动: 576.52
- CUDA 支持: 已启用 (torch.cuda.is_available() 返回 True)
- PyTorch 版本: 2.9.0.dev20250711+cu128 (固定，不能变)
- 设备能力: CUDA Compute Capability (12, 0)
- GPU 使用情况: 训练时最高占用率约 40%，受限于 PCIe 4.0 固态硬盘瓶颈

## 资源分配建议
- 训练模式: 使用 12 线程 + 35GB 内存
- 实战模式: 使用 2 线程 + 8GB 内存

## 兼容性笔记
- RTX 5060新卡限制: PyTorch固定2.9.0.dev (无更高稳定版)；架构兼容（YOLO/MCTS测试OK，无需升级）。 
- 潜在问题: 如果CUDA不稳, fallback到CPU (加torch.device('cpu'))。
- 优化: 用torch.backends.cudnn.benchmark = True加速。

## 更新机制
- 换硬件时: 更新此文档并进行基准测试（如 YOLO 单帧<100ms）。
- 开发过程中请@此文档，避免 AI 假设错配置导致不一致行为。