"""
区域分配错误深度分析脚本

本脚本深入分析区域分配错误的具体模式和原因，
为进一步优化区域分类器提供精确的改进方向。

分析内容：
1. 错误模式统计和分布
2. 错误位置的空间分析
3. 特定区域的错误原因分析
4. 改进建议生成
"""

import json
import numpy as np
from pathlib import Path
from typing import Dict, List, Any, Tuple
from collections import Counter, defaultdict
import matplotlib.pyplot as plt

class RegionErrorAnalyzer:
    """区域错误分析器"""
    
    def __init__(self, validation_report_path: str):
        self.report_path = Path(validation_report_path)
        self.validation_data = self._load_validation_data()
        
    def _load_validation_data(self) -> Dict[str, Any]:
        """加载验证数据"""
        if not self.report_path.exists():
            raise FileNotFoundError(f"验证报告未找到: {self.report_path}")
        
        with open(self.report_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    
    def analyze_error_patterns(self) -> Dict[str, Any]:
        """分析错误模式"""
        print("🔍 分析区域分配错误模式...")
        
        all_region_errors = []
        
        # 收集所有区域错误
        for seq_result in self.validation_data["sequence_results"]:
            all_region_errors.extend(seq_result["region_errors"])
        
        print(f"总区域错误数: {len(all_region_errors)}")
        
        # 1. 错误模式统计
        error_patterns = Counter()
        for error in all_region_errors:
            pattern = f"{error['expected_region']} -> {error['actual_region']}"
            error_patterns[pattern] += 1
        
        print("\n📊 最常见的错误模式:")
        for pattern, count in error_patterns.most_common(10):
            print(f"  {pattern}: {count}次")
        
        # 2. 按卡牌类型分析错误
        card_errors = Counter()
        for error in all_region_errors:
            card_errors[error['card']] += 1
        
        print("\n🎯 错误最多的卡牌类型:")
        for card, count in card_errors.most_common(10):
            print(f"  {card}: {count}次")
        
        # 3. 按期望区域分析错误
        expected_region_errors = Counter()
        for error in all_region_errors:
            expected_region_errors[error['expected_region']] += 1
        
        print("\n🎯 哪些真实区域最容易被误判:")
        for region, count in expected_region_errors.most_common(10):
            print(f"  区域{region}: {count}次")
        
        # 4. 按预测区域分析错误
        actual_region_errors = Counter()
        for error in all_region_errors:
            actual_region_errors[error['actual_region']] += 1
        
        print("\n🎯 系统最容易误判为哪些区域:")
        for region, count in actual_region_errors.most_common(10):
            print(f"  区域{region}: {count}次")
        
        return {
            "total_errors": len(all_region_errors),
            "error_patterns": dict(error_patterns.most_common(20)),
            "card_errors": dict(card_errors.most_common(20)),
            "expected_region_errors": dict(expected_region_errors.most_common()),
            "actual_region_errors": dict(actual_region_errors.most_common())
        }
    
    def analyze_specific_error_causes(self) -> Dict[str, Any]:
        """分析特定错误的原因"""
        print("\n🔬 分析特定错误原因...")
        
        all_region_errors = []
        for seq_result in self.validation_data["sequence_results"]:
            all_region_errors.extend(seq_result["region_errors"])
        
        # 重点分析最常见的错误模式
        error_analysis = {}
        
        # 1. 分析 "区域X -> 区域0" 的错误（系统无法分类）
        unclassified_errors = [e for e in all_region_errors if e['actual_region'] == 0]
        print(f"\n❌ 无法分类的错误 (-> 区域0): {len(unclassified_errors)}次")
        
        if unclassified_errors:
            unclassified_by_expected = Counter(e['expected_region'] for e in unclassified_errors)
            print("  最常无法分类的真实区域:")
            for region, count in unclassified_by_expected.most_common(5):
                print(f"    区域{region}: {count}次")
            
            error_analysis["unclassified"] = {
                "count": len(unclassified_errors),
                "by_expected_region": dict(unclassified_by_expected.most_common()),
                "reason": "增强区域分类器无法识别这些位置的卡牌"
            }
        
        # 2. 分析区域混淆错误
        confusion_errors = [e for e in all_region_errors if e['actual_region'] != 0]
        print(f"\n🔄 区域混淆错误: {len(confusion_errors)}次")
        
        if confusion_errors:
            confusion_patterns = Counter()
            for error in confusion_errors:
                pattern = (error['expected_region'], error['actual_region'])
                confusion_patterns[pattern] += 1
            
            print("  最常见的区域混淆:")
            for (expected, actual), count in confusion_patterns.most_common(5):
                print(f"    区域{expected} -> 区域{actual}: {count}次")
            
            error_analysis["confusion"] = {
                "count": len(confusion_errors),
                "top_patterns": dict(confusion_patterns.most_common(10)),
                "reason": "区域边界重叠或特征相似导致混淆"
            }
        
        # 3. 分析高IoU但区域错误的情况
        high_iou_errors = [e for e in all_region_errors if e.get('iou', 0) > 0.9]
        print(f"\n🎯 高IoU但区域错误: {len(high_iou_errors)}次")
        
        if high_iou_errors:
            print("  这些错误说明卡牌检测位置准确，但区域分类有问题")
            error_analysis["high_iou_errors"] = {
                "count": len(high_iou_errors),
                "reason": "检测准确但区域分类算法需要改进"
            }
        
        return error_analysis
    
    def analyze_region_boundary_issues(self) -> Dict[str, Any]:
        """分析区域边界问题"""
        print("\n📏 分析区域边界问题...")
        
        # 加载区域分析结果
        region_analysis_path = Path("analysis/region_analysis_results.json")
        if not region_analysis_path.exists():
            print("  ⚠️ 未找到区域分析结果，跳过边界分析")
            return {}
        
        with open(region_analysis_path, 'r', encoding='utf-8') as f:
            region_data = json.load(f)
        
        region_stats = region_data.get("region_statistics", {})
        
        # 分析区域重叠情况
        overlaps = region_data.get("region_overlaps", [])
        
        print("🔍 主要区域重叠情况:")
        for overlap in overlaps[:5]:
            region1 = overlap["region1"]
            region2 = overlap["region2"]
            area = overlap["overlap_area"]
            print(f"  区域{region1} ↔ 区域{region2}: 重叠面积 {area:.4f}")
        
        # 分析边界模糊的区域
        boundary_issues = {}
        for region_id, stats in region_stats.items():
            if region_id == "null":
                continue
                
            # 计算区域的边界清晰度（标准差越大越模糊）
            x_clarity = 1 / (stats["x_std"] + 0.001)  # 避免除零
            y_clarity = 1 / (stats["y_std"] + 0.001)
            overall_clarity = (x_clarity + y_clarity) / 2
            
            boundary_issues[region_id] = {
                "clarity_score": overall_clarity,
                "x_std": stats["x_std"],
                "y_std": stats["y_std"],
                "card_count": stats["count"]
            }
        
        # 按清晰度排序
        sorted_clarity = sorted(boundary_issues.items(), key=lambda x: x[1]["clarity_score"])
        
        print("\n📊 区域边界清晰度排名（越低越模糊）:")
        for region_id, info in sorted_clarity[:5]:
            print(f"  区域{region_id}: 清晰度 {info['clarity_score']:.2f} "
                  f"(X标准差: {info['x_std']:.3f}, Y标准差: {info['y_std']:.3f})")
        
        return {
            "region_overlaps": overlaps,
            "boundary_clarity": boundary_issues,
            "most_ambiguous_regions": [item[0] for item in sorted_clarity[:3]]
        }
    
    def generate_improvement_recommendations(self, error_analysis: Dict, boundary_analysis: Dict) -> List[str]:
        """生成改进建议"""
        print("\n💡 生成改进建议...")
        
        recommendations = []
        
        # 基于无法分类错误的建议
        if "unclassified" in error_analysis:
            unclassified_count = error_analysis["unclassified"]["count"]
            total_errors = error_analysis.get("total_errors", 1)
            unclassified_ratio = unclassified_count / total_errors
            
            if unclassified_ratio > 0.5:
                recommendations.append(
                    f"🔧 高优先级：{unclassified_ratio:.1%}的错误是无法分类问题，"
                    "建议扩展增强区域分类器的覆盖范围，添加更多区域规则"
                )
        
        # 基于区域混淆的建议
        if "confusion" in error_analysis:
            top_confusion = list(error_analysis["confusion"]["top_patterns"].items())[0]
            pattern, count = top_confusion
            expected, actual = eval(pattern) if isinstance(pattern, str) else pattern
            
            recommendations.append(
                f"🎯 中优先级：区域{expected}和区域{actual}混淆最严重({count}次)，"
                "建议优化这两个区域的边界定义和分类特征"
            )
        
        # 基于边界模糊的建议
        if "most_ambiguous_regions" in boundary_analysis:
            ambiguous_regions = boundary_analysis["most_ambiguous_regions"]
            recommendations.append(
                f"📏 低优先级：区域{ambiguous_regions}边界最模糊，"
                "建议收集更多数据或使用机器学习方法优化边界"
            )
        
        # 基于高IoU错误的建议
        if "high_iou_errors" in error_analysis:
            high_iou_count = error_analysis["high_iou_errors"]["count"]
            if high_iou_count > 10:
                recommendations.append(
                    f"🔍 技术建议：{high_iou_count}个高IoU错误说明检测准确但分类有误，"
                    "建议引入更多特征（如卡牌大小、邻近关系）来改进分类"
                )
        
        return recommendations
    
    def run_complete_analysis(self) -> Dict[str, Any]:
        """运行完整的错误分析"""
        print("🚀 区域分配错误深度分析")
        print("=" * 50)
        
        # 1. 错误模式分析
        error_patterns = self.analyze_error_patterns()
        
        # 2. 特定错误原因分析
        error_causes = self.analyze_specific_error_causes()
        
        # 3. 区域边界问题分析
        boundary_issues = self.analyze_region_boundary_issues()
        
        # 4. 生成改进建议
        recommendations = self.generate_improvement_recommendations(error_causes, boundary_issues)
        
        print("\n💡 改进建议:")
        for i, rec in enumerate(recommendations, 1):
            print(f"{i}. {rec}")
        
        # 保存分析结果
        analysis_result = {
            "analysis_timestamp": "2025-01-17 08:00:00",
            "error_patterns": error_patterns,
            "error_causes": error_causes,
            "boundary_issues": boundary_issues,
            "recommendations": recommendations
        }
        
        output_path = "analysis/region_error_analysis_results.json"
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(analysis_result, f, ensure_ascii=False, indent=2)
        
        print(f"\n💾 分析结果已保存: {output_path}")
        
        return analysis_result

def main():
    """主分析函数"""
    # 使用最新的验证报告
    report_path = "tests/enhanced_system_validation_20250717_074756.json"
    
    if not Path(report_path).exists():
        print(f"❌ 验证报告未找到: {report_path}")
        return False
    
    analyzer = RegionErrorAnalyzer(report_path)
    results = analyzer.run_complete_analysis()
    
    print("\n🎉 区域错误分析完成！")
    return True

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
