import json
import os
import sys
from pprint import pprint

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from src.core.state_builder import StateBuilder
from src.core.paohuzi_env import PaohuziEnv

def test_state_builder():
    """测试状态转换模块"""
    print("===== 测试状态转换模块 =====")
    
    # 创建状态转换模块
    state_builder = StateBuilder()
    
    # 模拟YOLO检测结果
    yolo_detections = {
        'cards': [
            {'id': '1_二', 'group_id': 1, 'pos': [100, 100, 50, 70], 'conf': 0.95},
            {'id': '2_三', 'group_id': 1, 'pos': [160, 100, 50, 70], 'conf': 0.93},
            {'id': '3_四', 'group_id': 1, 'pos': [220, 100, 50, 70], 'conf': 0.91},
            {'id': '4_五', 'group_id': 1, 'pos': [280, 100, 50, 70], 'conf': 0.94},
            {'id': '5_六', 'group_id': 1, 'pos': [340, 100, 50, 70], 'conf': 0.92},
            
            {'id': '6_七', 'group_id': 5, 'pos': [100, 200, 50, 70], 'conf': 0.90},
            {'id': '7_八', 'group_id': 5, 'pos': [160, 200, 50, 70], 'conf': 0.89},
            
            {'id': '8_九', 'group_id': 6, 'pos': [100, 300, 50, 70], 'conf': 0.88},
            {'id': '9_九_暗', 'group_id': 6, 'pos': [160, 300, 50, 70], 'conf': 0.87},
            {'id': '10_九_暗', 'group_id': 6, 'pos': [220, 300, 50, 70], 'conf': 0.86},
            
            {'id': '11_十', 'group_id': 9, 'pos': [100, 400, 50, 70], 'conf': 0.85},
            {'id': '12_壹', 'group_id': 9, 'pos': [160, 400, 50, 70], 'conf': 0.84},
            
            {'id': '13_贰', 'group_id': 16, 'pos': [100, 500, 50, 70], 'conf': 0.83},
            {'id': '14_贰', 'group_id': 16, 'pos': [160, 500, 50, 70], 'conf': 0.82},
            {'id': '15_贰', 'group_id': 16, 'pos': [220, 500, 50, 70], 'conf': 0.81},
        ]
    }
    
    # 转换为RLCard状态
    rlcard_state = state_builder.yolo_to_rlcard_state(yolo_detections)
    
    # 打印RLCard状态
    print("RLCard状态:")
    pprint(rlcard_state)
    
    # 转换为显示格式
    display_state = state_builder.rlcard_to_display_format(rlcard_state)
    
    # 打印显示格式
    print("\n显示格式:")
    pprint(display_state)

def test_paohuzi_env():
    """测试跑胡子游戏环境"""
    print("\n===== 测试跑胡子游戏环境 =====")
    
    # 创建环境
    env = PaohuziEnv()
    
    # 重置环境
    state = env.reset()
    
    # 打印初始状态
    print("初始状态:")
    pprint(state)
    
    # 执行几个随机动作
    for i in range(5):
        action = i % 5  # 简单循环动作
        next_state, reward, done, info = env.step(action)
        
        print(f"\n执行动作 {action}:")
        print(f"奖励: {reward}, 游戏结束: {done}")
        print("下一状态:")
        if 'hand' in next_state:
            print(f"手牌数量: {len(next_state['hand'])}")
        
        if done:
            print("游戏结束!")
            break

def test_integration():
    """测试集成"""
    print("\n===== 测试集成 =====")
    
    # 创建状态转换模块
    state_builder = StateBuilder()
    
    # 创建环境
    env = PaohuziEnv()
    
    # 模拟YOLO检测结果
    yolo_detections = {
        'cards': [
            {'id': '1_二', 'group_id': 1, 'pos': [100, 100, 50, 70], 'conf': 0.95},
            {'id': '2_三', 'group_id': 1, 'pos': [160, 100, 50, 70], 'conf': 0.93},
            {'id': '3_四', 'group_id': 1, 'pos': [220, 100, 50, 70], 'conf': 0.91},
            {'id': '4_五', 'group_id': 1, 'pos': [280, 100, 50, 70], 'conf': 0.94},
            {'id': '5_六', 'group_id': 1, 'pos': [340, 100, 50, 70], 'conf': 0.92},
        ]
    }
    
    # 转换为RLCard状态
    rlcard_state = state_builder.yolo_to_rlcard_state(yolo_detections)
    
    # 打印RLCard状态
    print("从YOLO检测转换的RLCard状态:")
    pprint(rlcard_state)
    
    # 重置环境
    env_state = env.reset()
    
    # 打印环境状态
    print("\n环境初始状态:")
    if 'hand' in env_state:
        print(f"手牌数量: {len(env_state['hand'])}")

if __name__ == "__main__":
    test_state_builder()
    test_paohuzi_env()
    test_integration() 