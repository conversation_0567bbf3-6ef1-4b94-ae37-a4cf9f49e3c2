"""
调试继承机制问题
"""

import sys
sys.path.append('src')

from modules.simple_inheritor import SimpleInheritor
import logging

# 配置日志
logging.basicConfig(level=logging.DEBUG, format='%(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_inheritance_debug():
    """调试继承机制"""
    print("🔍 调试继承机制")
    
    # 创建继承器
    inheritor = SimpleInheritor(iou_threshold=0.3)
    
    # 模拟第1帧数据
    frame1_data = [
        {
            'label': '二', 
            'bbox': [100, 100, 150, 150], 
            'confidence': 0.9, 
            'group_id': 1,
            'twin_id': '1二',  # 模拟已分配的ID
            'is_virtual': False
        },
        {
            'label': '三', 
            'bbox': [200, 100, 250, 150], 
            'confidence': 0.8, 
            'group_id': 1,
            'twin_id': '1三',  # 模拟已分配的ID
            'is_virtual': False
        }
    ]
    
    print(f"\n📋 第1帧：{len(frame1_data)}张卡牌")
    result1 = inheritor.process_inheritance(frame1_data)
    
    print(f"✅ 第1帧处理完成")
    print(f"   继承: {len(result1.inherited_cards)}张")
    print(f"   新增: {len(result1.new_cards)}张")
    print(f"   前一帧记录: {len(inheritor.previous_frame_cards)}张")
    
    # 模拟第2帧数据（相同位置的卡牌应该继承ID）
    frame2_data = [
        {
            'label': '二', 
            'bbox': [105, 105, 155, 155],  # 位置略有变化
            'confidence': 0.9, 
            'group_id': 1
        },
        {
            'label': '三', 
            'bbox': [205, 105, 255, 155],  # 位置略有变化
            'confidence': 0.8, 
            'group_id': 1
        },
        {
            'label': '五', 
            'bbox': [400, 100, 450, 150],  # 新卡牌
            'confidence': 0.9, 
            'group_id': 1
        }
    ]
    
    print(f"\n📋 第2帧：{len(frame2_data)}张卡牌")
    
    # 手动测试匹配逻辑
    print("\n🔍 手动测试匹配逻辑:")
    for i, current_card in enumerate(frame2_data):
        print(f"\n   当前卡牌 {i}: {current_card['label']} at {current_card['bbox']}")
        
        best_idx, best_score = inheritor._find_best_match(current_card, set())
        print(f"   最佳匹配: idx={best_idx}, score={best_score:.3f}")
        
        if best_idx is not None:
            prev_card = inheritor.previous_frame_cards[best_idx]
            print(f"   匹配到: {prev_card['label']} (ID: {prev_card['twin_id']}) at {prev_card['bbox']}")
            
            # 详细分析匹配分数
            score = inheritor._calculate_match_score(current_card, prev_card)
            print(f"   详细分数: {score:.3f}")
            
            # 分解分数
            label_match = current_card.get('label') == prev_card.get('label')
            group_match = current_card.get('group_id') == prev_card.get('group_id')
            
            current_bbox = current_card.get('bbox', [])
            prev_bbox = prev_card.get('bbox', [])
            iou = 0.0
            if len(current_bbox) == 4 and len(prev_bbox) == 4:
                iou = inheritor._calculate_iou(current_bbox, prev_bbox)
            
            print(f"     标签匹配: {label_match} (+0.4)")
            print(f"     区域匹配: {group_match} (+0.3)")
            print(f"     IoU: {iou:.3f} (+{0.3 * iou:.3f})")
        else:
            print("   无匹配")
    
    # 执行继承处理
    result2 = inheritor.process_inheritance(frame2_data)
    
    print(f"\n✅ 第2帧处理完成")
    print(f"   继承: {len(result2.inherited_cards)}张")
    print(f"   新增: {len(result2.new_cards)}张")
    
    # 显示继承结果
    if result2.inherited_cards:
        print("\n🎯 继承的卡牌:")
        for card in result2.inherited_cards:
            print(f"   ID: {card['twin_id']} (标签: {card['label']})")
    
    if result2.new_cards:
        print("\n🆕 新增的卡牌:")
        for card in result2.new_cards:
            print(f"   标签: {card['label']} (需要分配ID)")
    
    # 统计信息
    stats = result2.statistics
    inheritance_rate = stats.get('inheritance_rate', 0)
    print(f"\n📊 继承率: {inheritance_rate:.1%}")

if __name__ == "__main__":
    test_inheritance_debug()
