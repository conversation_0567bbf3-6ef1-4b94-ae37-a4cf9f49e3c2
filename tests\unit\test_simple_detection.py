"""
简单检测功能单元测试
重构自: simple_test.py
"""
import pytest
import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

class TestSimpleDetection:
    """简单检测功能测试"""
    
    def test_module_import(self):
        """测试模块导入"""
        try:
            from src.core.digital_twin_v2 import create_digital_twin_system, CardDetection
            assert True, "模块导入成功"
        except ImportError as e:
            pytest.fail(f"模块导入失败: {e}")
    
    def test_system_creation(self):
        """测试系统创建"""
        try:
            from src.core.digital_twin_v2 import create_digital_twin_system
            dt_system = create_digital_twin_system()
            assert dt_system is not None, "系统创建成功"
        except Exception as e:
            pytest.fail(f"系统创建失败: {e}")
    
    def test_dual_format_method_exists(self):
        """测试双轨输出方法是否存在"""
        try:
            from src.core.digital_twin_v2 import create_digital_twin_system
            dt_system = create_digital_twin_system()
            
            assert hasattr(dt_system, 'export_synchronized_dual_format'), \
                "双轨输出方法应该存在"
                
        except Exception as e:
            pytest.fail(f"测试失败: {e}")
    
    def test_basic_functionality(self):
        """测试基本功能"""
        try:
            from src.core.digital_twin_v2 import create_digital_twin_system, CardDetection
            
            # 创建系统
            dt_system = create_digital_twin_system()
            
            # 创建测试检测数据
            test_detections = [
                CardDetection("二", [100, 100, 150, 150], 0.95, 1, "手牌_观战方", "spectator")
            ]
            
            # 处理帧数据
            result = dt_system.process_frame(test_detections)
            
            # 验证结果
            assert "digital_twin_cards" in result, "结果应包含数字孪生卡牌"
            assert len(result["digital_twin_cards"]) > 0, "应该有数字孪生卡牌"
            
        except Exception as e:
            pytest.fail(f"基本功能测试失败: {e}")

if __name__ == "__main__":
    # 直接运行测试 (兼容原始脚本的使用方式)
    print("1. 开始导入模块...")
    try:
        from src.core.digital_twin_v2 import create_digital_twin_system, CardDetection
        print("2. 模块导入成功")
        
        print("3. 创建数字孪生系统...")
        dt_system = create_digital_twin_system()
        print("4. 系统创建成功")
        
        print("5. 检查双轨输出方法...")
        if hasattr(dt_system, 'export_synchronized_dual_format'):
            print("6. ✅ 双轨输出方法存在")
        else:
            print("6. ❌ 双轨输出方法不存在")
            
        print("7. 测试完成")
        
    except Exception as e:
        print(f"错误: {e}")
        import traceback
        traceback.print_exc()
