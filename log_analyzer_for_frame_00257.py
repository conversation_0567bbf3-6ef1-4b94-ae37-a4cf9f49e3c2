#!/usr/bin/env python3
"""
专门分析frame_00257.jpg错误的日志分析器

从现有的处理日志中提取关键信息，分析"2叁"→"2伍"错误的处理流程
"""

import os
import re
import json
from pathlib import Path
from typing import Dict, List, Any, Optional
from datetime import datetime
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('log_analysis_results.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class LogAnalyzerForFrame00257:
    """Frame_00257.jpg日志分析器"""
    
    def __init__(self):
        self.target_frames = ["frame_00256", "frame_00257"]
        self.analysis_results = {}
        
    def analyze_logs(self):
        """分析所有相关日志"""
        logger.info("🔍 开始分析frame_00257.jpg相关日志...")
        
        # 1. 查找所有可能的日志文件
        log_files = self._find_log_files()
        
        # 2. 提取目标帧的处理日志
        frame_logs = self._extract_frame_logs(log_files)
        
        # 3. 分析继承和流转过程
        inheritance_analysis = self._analyze_inheritance_process(frame_logs)
        
        # 4. 分析ID分配过程
        id_assignment_analysis = self._analyze_id_assignment(frame_logs)
        
        # 5. 分析模块协调过程
        module_coordination_analysis = self._analyze_module_coordination(frame_logs)
        
        # 6. 生成分析报告
        self._generate_log_analysis_report({
            "frame_logs": frame_logs,
            "inheritance_analysis": inheritance_analysis,
            "id_assignment_analysis": id_assignment_analysis,
            "module_coordination_analysis": module_coordination_analysis
        })
        
    def _find_log_files(self) -> List[Path]:
        """查找所有可能的日志文件"""
        logger.info("📁 查找日志文件...")
        
        # 可能的日志文件位置和模式
        search_patterns = [
            "*.log",
            "**/*.log",
            "logs/*.log",
            "output/**/*.log",
            "calibration_gt_final_processor.log",
            "phase2_integrator.log",
            "simple_inheritor.log",
            "region_transitioner.log",
            "basic_id_assigner.log"
        ]
        
        log_files = []
        search_dirs = [Path("."), Path("output"), Path("logs")]
        
        for search_dir in search_dirs:
            if search_dir.exists():
                for pattern in search_patterns:
                    found_files = list(search_dir.glob(pattern))
                    log_files.extend(found_files)
                    
        # 去重
        log_files = list(set(log_files))
        
        logger.info(f"📁 找到 {len(log_files)} 个日志文件:")
        for log_file in log_files:
            logger.info(f"  - {log_file}")
            
        return log_files
        
    def _extract_frame_logs(self, log_files: List[Path]) -> Dict[str, List[str]]:
        """提取目标帧的相关日志"""
        logger.info("📋 提取目标帧日志...")
        
        frame_logs = {frame: [] for frame in self.target_frames}
        
        for log_file in log_files:
            try:
                with open(log_file, 'r', encoding='utf-8', errors='ignore') as f:
                    lines = f.readlines()
                    
                logger.info(f"📋 分析日志文件: {log_file} ({len(lines)} 行)")
                
                for line_num, line in enumerate(lines, 1):
                    line = line.strip()
                    
                    # 检查是否包含目标帧
                    for frame_name in self.target_frames:
                        if frame_name in line:
                            frame_logs[frame_name].append({
                                "file": str(log_file),
                                "line_num": line_num,
                                "content": line,
                                "timestamp": self._extract_timestamp(line)
                            })
                            
            except Exception as e:
                logger.warning(f"⚠️ 读取日志文件失败 {log_file}: {e}")
                
        # 输出统计
        for frame_name, logs in frame_logs.items():
            logger.info(f"📋 {frame_name}: 找到 {len(logs)} 条相关日志")
            
        return frame_logs
        
    def _extract_timestamp(self, line: str) -> Optional[str]:
        """从日志行中提取时间戳"""
        # 常见的时间戳格式
        timestamp_patterns = [
            r'\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}',
            r'\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}',
            r'\d{2}:\d{2}:\d{2}'
        ]
        
        for pattern in timestamp_patterns:
            match = re.search(pattern, line)
            if match:
                return match.group()
                
        return None
        
    def _analyze_inheritance_process(self, frame_logs: Dict[str, List[str]]) -> Dict[str, Any]:
        """分析继承过程"""
        logger.info("🔄 分析继承过程...")
        
        inheritance_keywords = [
            "继承", "inheritance", "inherit",
            "跨区域", "cross_region", 
            "previous_frame_mapping",
            "SimpleInheritor",
            "叁", "伍", "三", "五"
        ]
        
        inheritance_logs = []
        
        for frame_name, logs in frame_logs.items():
            for log_entry in logs:
                content = log_entry["content"].lower()
                
                # 检查是否包含继承相关关键词
                for keyword in inheritance_keywords:
                    if keyword.lower() in content:
                        inheritance_logs.append({
                            "frame": frame_name,
                            "keyword": keyword,
                            "log": log_entry
                        })
                        break
                        
        # 分析继承流程
        analysis = {
            "total_inheritance_logs": len(inheritance_logs),
            "inheritance_by_frame": {},
            "key_findings": [],
            "potential_issues": []
        }
        
        for frame_name in self.target_frames:
            frame_inheritance_logs = [log for log in inheritance_logs if log["frame"] == frame_name]
            analysis["inheritance_by_frame"][frame_name] = len(frame_inheritance_logs)
            
            # 查找关键信息
            for log_entry in frame_inheritance_logs:
                content = log_entry["log"]["content"]
                
                # 查找"叁"和"伍"相关的处理
                if "叁" in content or "三" in content:
                    analysis["key_findings"].append(f"{frame_name}: 发现'叁'处理 - {content}")
                    
                if "伍" in content or "五" in content:
                    analysis["key_findings"].append(f"{frame_name}: 发现'伍'处理 - {content}")
                    
                # 查找错误指示
                if any(word in content.lower() for word in ["error", "错误", "失败", "failed"]):
                    analysis["potential_issues"].append(f"{frame_name}: 潜在问题 - {content}")
                    
        return analysis
        
    def _analyze_id_assignment(self, frame_logs: Dict[str, List[str]]) -> Dict[str, Any]:
        """分析ID分配过程"""
        logger.info("🏷️ 分析ID分配过程...")
        
        id_keywords = [
            "twin_id", "数字孪生", "ID分配", "id_assigner",
            "BasicIdAssigner", "分配", "assign",
            "2叁", "2伍", "2三", "2五"
        ]
        
        id_logs = []
        
        for frame_name, logs in frame_logs.items():
            for log_entry in logs:
                content = log_entry["content"]
                
                for keyword in id_keywords:
                    if keyword in content:
                        id_logs.append({
                            "frame": frame_name,
                            "keyword": keyword,
                            "log": log_entry
                        })
                        break
                        
        analysis = {
            "total_id_logs": len(id_logs),
            "id_assignments_found": [],
            "id_conflicts": [],
            "assignment_sequence": []
        }
        
        # 查找具体的ID分配
        for log_entry in id_logs:
            content = log_entry["log"]["content"]
            
            # 查找ID分配模式
            id_patterns = [
                r'twin_id[\'\":\s]*[\'\"]*([^\'\"]*)[\'\"]*',
                r'ID[=:]\s*[\'\"]*([^\'\"]*)[\'\"]*',
                r'分配.*?([0-9]+[叁伍三五])',
                r'assign.*?([0-9]+[叁伍三五])'
            ]
            
            for pattern in id_patterns:
                matches = re.findall(pattern, content, re.IGNORECASE)
                for match in matches:
                    analysis["id_assignments_found"].append({
                        "frame": log_entry["frame"],
                        "assigned_id": match,
                        "context": content
                    })
                    
        return analysis
        
    def _analyze_module_coordination(self, frame_logs: Dict[str, List[str]]) -> Dict[str, Any]:
        """分析模块协调过程"""
        logger.info("🔗 分析模块协调过程...")
        
        module_keywords = [
            "Phase2Integrator", "SimpleInheritor", "RegionTransitioner",
            "BasicIdAssigner", "DarkCardProcessor", "OcclusionCompensator",
            "process_detections", "模块", "module"
        ]
        
        module_logs = []
        
        for frame_name, logs in frame_logs.items():
            for log_entry in logs:
                content = log_entry["content"]
                
                for keyword in module_keywords:
                    if keyword in content:
                        module_logs.append({
                            "frame": frame_name,
                            "module": keyword,
                            "log": log_entry
                        })
                        break
                        
        # 分析模块处理顺序
        analysis = {
            "total_module_logs": len(module_logs),
            "processing_sequence": [],
            "module_interactions": {},
            "coordination_issues": []
        }
        
        # 按时间戳排序（如果有的话）
        timestamped_logs = [log for log in module_logs if log["log"].get("timestamp")]
        timestamped_logs.sort(key=lambda x: x["log"]["timestamp"])
        
        analysis["processing_sequence"] = timestamped_logs
        
        return analysis
        
    def _generate_log_analysis_report(self, analysis_data: Dict[str, Any]):
        """生成日志分析报告"""
        logger.info("📋 生成日志分析报告...")
        
        report = {
            "analysis_time": datetime.now().isoformat(),
            "target_frames": self.target_frames,
            "analysis_summary": {
                "total_logs_analyzed": sum(len(logs) for logs in analysis_data["frame_logs"].values()),
                "inheritance_logs": analysis_data["inheritance_analysis"]["total_inheritance_logs"],
                "id_assignment_logs": analysis_data["id_assignment_analysis"]["total_id_logs"],
                "module_coordination_logs": analysis_data["module_coordination_analysis"]["total_module_logs"]
            },
            "detailed_analysis": analysis_data,
            "key_findings": self._extract_key_findings(analysis_data),
            "recommended_actions": self._recommend_actions(analysis_data)
        }
        
        # 保存报告
        report_file = "frame_00257_log_analysis_report.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
            
        logger.info(f"📋 日志分析报告已保存: {report_file}")
        
        # 输出关键发现
        self._print_key_findings(report["key_findings"])
        
    def _extract_key_findings(self, analysis_data: Dict[str, Any]) -> List[str]:
        """提取关键发现"""
        findings = []
        
        # 从继承分析中提取
        inheritance_findings = analysis_data["inheritance_analysis"]["key_findings"]
        findings.extend(inheritance_findings)
        
        # 从ID分配分析中提取
        id_assignments = analysis_data["id_assignment_analysis"]["id_assignments_found"]
        for assignment in id_assignments:
            if "叁" in assignment["assigned_id"] or "伍" in assignment["assigned_id"]:
                findings.append(f"关键ID分配: {assignment['frame']} - {assignment['assigned_id']}")
                
        # 从模块协调分析中提取
        coordination_issues = analysis_data["module_coordination_analysis"]["coordination_issues"]
        findings.extend(coordination_issues)
        
        return findings
        
    def _recommend_actions(self, analysis_data: Dict[str, Any]) -> List[str]:
        """推荐行动方案"""
        actions = [
            "增加更详细的调试日志，特别是7→9流转过程",
            "在SimpleInheritor中添加跨区域继承的详细日志",
            "在BasicIdAssigner中记录ID分配决策过程",
            "添加模块间数据传递的验证日志",
            "实施端到端的测试用例验证7→9流转"
        ]
        
        # 基于分析结果添加特定建议
        inheritance_analysis = analysis_data["inheritance_analysis"]
        if inheritance_analysis["total_inheritance_logs"] == 0:
            actions.append("⚠️ 未找到继承相关日志，需要启用继承模块的详细日志")
            
        id_analysis = analysis_data["id_assignment_analysis"]
        if len(id_analysis["id_assignments_found"]) == 0:
            actions.append("⚠️ 未找到ID分配日志，需要启用ID分配器的详细日志")
            
        return actions
        
    def _print_key_findings(self, findings: List[str]):
        """打印关键发现"""
        logger.info("🎯 关键发现:")
        if findings:
            for i, finding in enumerate(findings, 1):
                logger.info(f"  {i}. {finding}")
        else:
            logger.warning("⚠️ 未发现关键信息，可能需要更详细的日志")

def main():
    """主函数"""
    print("📋 Frame_00257.jpg 日志分析工具")
    print("=" * 50)
    
    analyzer = LogAnalyzerForFrame00257()
    
    try:
        analyzer.analyze_logs()
        print("\n✅ 日志分析完成！请查看生成的报告文件。")
        
    except Exception as e:
        logger.error(f"日志分析过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
