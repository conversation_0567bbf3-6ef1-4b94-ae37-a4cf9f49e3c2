#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
增强状态构建器 - 集成记忆机制的状态构建

基于原有StateBuilder，集成记忆机制，实现：
1. 基于记忆的状态连续性验证
2. 跨帧状态变化合理性检查
3. 与数字孪生系统的协同工作
4. 游戏规则约束的状态验证
"""

import numpy as np
import json
import time
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, field
from enum import Enum
from collections import defaultdict

from .state_builder import StateBuilder, Card, CardState, ActionType


@dataclass
class EnhancedGameState:
    """增强游戏状态"""
    frame_id: str
    timestamp: float
    session_id: str
    
    # 基础状态
    cards: List[Dict]
    ui_elements: List[Dict]
    game_phase: str
    
    # 记忆增强信息
    recovered_cards: List[Dict] = field(default_factory=list)
    state_changes: List[Dict] = field(default_factory=list)
    validation_results: Dict = field(default_factory=dict)
    
    # 数字孪生信息
    digital_twins: Dict[str, Dict] = field(default_factory=dict)
    id_mappings: Dict[str, str] = field(default_factory=dict)
    
    # 统计信息
    statistics: Dict = field(default_factory=dict)


class EnhancedStateBuilder:
    """增强状态构建器 - 集成记忆机制"""
    
    def __init__(self, config_path: Optional[str] = None):
        """
        初始化增强状态构建器
        
        Args:
            config_path: 配置文件路径
        """
        # 初始化基础状态构建器
        self.base_builder = StateBuilder(config_path)
        
        # 状态历史管理
        self.state_history = []
        self.max_history = 10
        
        # 数字孪生管理
        self.digital_twin_tracker = DigitalTwinTracker()
        
        # 状态变化验证器
        self.state_validator = StateChangeValidator()
        
        # 游戏规则引擎
        self.rule_engine = GameRuleEngine()
        
        print("🏗️ 增强状态构建器初始化完成")
    
    def build_enhanced_state(self, 
                           detections: List[Dict],
                           frame_id: str,
                           timestamp: float,
                           session_id: str,
                           memory_info: Optional[Dict] = None) -> EnhancedGameState:
        """
        构建增强游戏状态
        
        Args:
            detections: 检测结果（已经过记忆机制处理）
            frame_id: 帧ID
            timestamp: 时间戳
            session_id: 会话ID
            memory_info: 记忆机制信息
            
        Returns:
            增强游戏状态
        """
        start_time = time.time()
        
        try:
            # 1. 基础状态构建
            base_state = self.base_builder.build_state(detections)
            
            # 2. 分离卡牌和UI元素
            cards, ui_elements = self._separate_detections(detections)
            
            # 3. 数字孪生处理
            digital_twins, id_mappings = self.digital_twin_tracker.process_cards(
                cards, self.state_history
            )
            
            # 4. 识别恢复的卡牌
            recovered_cards = [d for d in detections if d.get('recovered', False)]
            
            # 5. 状态变化分析
            state_changes = self._analyze_state_changes(cards, recovered_cards)
            
            # 6. 状态验证
            validation_results = self.state_validator.validate_state_change(
                cards, self.state_history, self.rule_engine
            )
            
            # 7. 游戏阶段识别
            game_phase = self._identify_game_phase(cards, ui_elements, base_state)
            
            # 8. 构建增强状态
            enhanced_state = EnhancedGameState(
                frame_id=frame_id,
                timestamp=timestamp,
                session_id=session_id,
                cards=cards,
                ui_elements=ui_elements,
                game_phase=game_phase,
                recovered_cards=recovered_cards,
                state_changes=state_changes,
                validation_results=validation_results,
                digital_twins=digital_twins,
                id_mappings=id_mappings,
                statistics={
                    'total_cards': len(cards),
                    'recovered_cards': len(recovered_cards),
                    'ui_elements': len(ui_elements),
                    'processing_time': time.time() - start_time,
                    'validation_passed': validation_results.get('passed', True)
                }
            )
            
            # 9. 更新历史
            self._update_state_history(enhanced_state)
            
            return enhanced_state
            
        except Exception as e:
            print(f"❌ 增强状态构建失败: {e}")
            # 返回基础状态
            return self._create_fallback_state(detections, frame_id, timestamp, session_id)
    
    def _separate_detections(self, detections: List[Dict]) -> Tuple[List[Dict], List[Dict]]:
        """分离卡牌和UI元素"""
        valid_card_labels = {
            '一', '二', '三', '四', '五', '六', '七', '八', '九', '十',
            '壹', '贰', '叁', '肆', '伍', '陆', '柒', '捌', '玖', '拾', '暗'
        }
        
        cards = []
        ui_elements = []
        
        for detection in detections:
            label = detection.get('label', '')
            if label in valid_card_labels:
                cards.append(detection)
            else:
                ui_elements.append(detection)
        
        return cards, ui_elements
    
    def _analyze_state_changes(self, current_cards: List[Dict], recovered_cards: List[Dict]) -> List[Dict]:
        """分析状态变化"""
        changes = []
        
        if not self.state_history:
            return changes
        
        last_state = self.state_history[-1]
        last_cards = last_state.cards
        
        # 分析新增卡牌
        new_cards = self._find_new_cards(current_cards, last_cards)
        for card in new_cards:
            changes.append({
                'type': 'card_added',
                'card': card,
                'timestamp': time.time()
            })
        
        # 分析消失卡牌
        missing_cards = self._find_missing_cards(current_cards, last_cards)
        for card in missing_cards:
            changes.append({
                'type': 'card_missing',
                'card': card,
                'timestamp': time.time()
            })
        
        # 分析恢复卡牌
        for card in recovered_cards:
            changes.append({
                'type': 'card_recovered',
                'card': card,
                'timestamp': time.time(),
                'recovery_source': card.get('recovery_source', 'unknown')
            })
        
        return changes
    
    def _find_new_cards(self, current_cards: List[Dict], last_cards: List[Dict]) -> List[Dict]:
        """寻找新增卡牌"""
        new_cards = []
        
        for current_card in current_cards:
            if not self._find_matching_card(current_card, last_cards):
                new_cards.append(current_card)
        
        return new_cards
    
    def _find_missing_cards(self, current_cards: List[Dict], last_cards: List[Dict]) -> List[Dict]:
        """寻找消失卡牌"""
        missing_cards = []
        
        for last_card in last_cards:
            if not self._find_matching_card(last_card, current_cards):
                missing_cards.append(last_card)
        
        return missing_cards
    
    def _find_matching_card(self, target_card: Dict, card_list: List[Dict]) -> Optional[Dict]:
        """在卡牌列表中寻找匹配卡牌"""
        target_bbox = target_card.get('bbox', [])
        if len(target_bbox) != 4:
            return None
        
        for card in card_list:
            card_bbox = card.get('bbox', [])
            if len(card_bbox) != 4:
                continue
            
            # 计算IoU
            iou = self._calculate_iou(target_bbox, card_bbox)
            if iou > 0.5:  # IoU阈值
                return card
        
        return None
    
    def _calculate_iou(self, bbox1: List[float], bbox2: List[float]) -> float:
        """计算IoU"""
        try:
            x1, y1, w1, h1 = bbox1
            x2, y2, w2, h2 = bbox2
            
            # 转换为 [x1, y1, x2, y2] 格式
            box1 = [x1, y1, x1 + w1, y1 + h1]
            box2 = [x2, y2, x2 + w2, y2 + h2]
            
            # 计算交集
            x_left = max(box1[0], box2[0])
            y_top = max(box1[1], box2[1])
            x_right = min(box1[2], box2[2])
            y_bottom = min(box1[3], box2[3])
            
            if x_right < x_left or y_bottom < y_top:
                return 0.0
            
            intersection = (x_right - x_left) * (y_bottom - y_top)
            
            # 计算并集
            area1 = w1 * h1
            area2 = w2 * h2
            union = area1 + area2 - intersection
            
            return intersection / union if union > 0 else 0.0
        
        except (ValueError, IndexError, ZeroDivisionError):
            return 0.0
    
    def _identify_game_phase(self, cards: List[Dict], ui_elements: List[Dict], base_state: Dict) -> str:
        """识别游戏阶段"""
        ui_labels = [ui.get('label', '') for ui in ui_elements]
        
        # 检查游戏结束
        end_keywords = ['牌局结束', '你赢了', '你输了', '荒庄']
        if any(keyword in ui_labels for keyword in end_keywords):
            return "GAME_END"
        
        # 检查游戏开始
        start_keywords = ['已准备', '开始游戏']
        if any(keyword in ui_labels for keyword in start_keywords):
            return "GAME_START"
        
        # 检查动作阶段
        action_keywords = ['吃', '碰', '胡', '过']
        if any(keyword in ui_labels for keyword in action_keywords):
            return "ACTION_PHASE"
        
        # 根据卡牌数量判断
        card_count = len(cards)
        if card_count == 0:
            return "WAITING"
        elif card_count > 40:
            return "DEALING"
        else:
            return "PLAYING"
    
    def _update_state_history(self, state: EnhancedGameState) -> None:
        """更新状态历史"""
        self.state_history.append(state)
        
        # 保持历史长度限制
        if len(self.state_history) > self.max_history:
            self.state_history.pop(0)
    
    def _create_fallback_state(self, detections: List[Dict], frame_id: str, 
                              timestamp: float, session_id: str) -> EnhancedGameState:
        """创建后备状态"""
        cards, ui_elements = self._separate_detections(detections)
        
        return EnhancedGameState(
            frame_id=frame_id,
            timestamp=timestamp,
            session_id=session_id,
            cards=cards,
            ui_elements=ui_elements,
            game_phase="UNKNOWN",
            statistics={
                'total_cards': len(cards),
                'ui_elements': len(ui_elements),
                'fallback_mode': True
            }
        )
    
    def get_state_history(self) -> List[EnhancedGameState]:
        """获取状态历史"""
        return self.state_history.copy()
    
    def clear_history(self) -> None:
        """清空状态历史"""
        self.state_history.clear()
        self.digital_twin_tracker.reset()
        print("🔄 状态历史已清空")


class DigitalTwinTracker:
    """数字孪生追踪器"""
    
    def __init__(self):
        self.id_counter = 0
        self.active_twins = {}
        self.id_mappings = {}
    
    def process_cards(self, cards: List[Dict], state_history: List[EnhancedGameState]) -> Tuple[Dict, Dict]:
        """处理卡牌的数字孪生"""
        digital_twins = {}
        id_mappings = {}
        
        for card in cards:
            twin_id = self._assign_twin_id(card, state_history)
            digital_twins[twin_id] = {
                'id': twin_id,
                'label': card.get('label', ''),
                'bbox': card.get('bbox', []),
                'confidence': card.get('confidence', 0),
                'first_seen': time.time(),
                'last_seen': time.time(),
                'stable_frames': 1
            }
            id_mappings[f"card_{len(id_mappings)}"] = twin_id
        
        return digital_twins, id_mappings
    
    def _assign_twin_id(self, card: Dict, state_history: List[EnhancedGameState]) -> str:
        """分配数字孪生ID"""
        # 简单的ID分配策略
        self.id_counter += 1
        return f"twin_{self.id_counter}_{card.get('label', 'unknown')}"
    
    def reset(self) -> None:
        """重置追踪器"""
        self.id_counter = 0
        self.active_twins.clear()
        self.id_mappings.clear()


class StateChangeValidator:
    """状态变化验证器"""
    
    def validate_state_change(self, current_cards: List[Dict], 
                            state_history: List[EnhancedGameState],
                            rule_engine: 'GameRuleEngine') -> Dict:
        """验证状态变化"""
        if not state_history:
            return {'passed': True, 'reason': 'no_history'}
        
        # 基础验证
        basic_validation = self._validate_basic_constraints(current_cards)
        if not basic_validation['passed']:
            return basic_validation
        
        # 规则验证
        rule_validation = rule_engine.validate_game_rules(current_cards, state_history)
        
        return {
            'passed': basic_validation['passed'] and rule_validation['passed'],
            'basic_validation': basic_validation,
            'rule_validation': rule_validation
        }
    
    def _validate_basic_constraints(self, cards: List[Dict]) -> Dict:
        """验证基础约束"""
        # 检查卡牌数量合理性
        if len(cards) > 80:  # 跑胡子最多80张牌
            return {'passed': False, 'reason': 'too_many_cards'}
        
        # 检查标签有效性
        valid_labels = {
            '一', '二', '三', '四', '五', '六', '七', '八', '九', '十',
            '壹', '贰', '叁', '肆', '伍', '陆', '柒', '捌', '玖', '拾', '暗'
        }
        
        for card in cards:
            if card.get('label', '') not in valid_labels:
                return {'passed': False, 'reason': 'invalid_label', 'card': card}
        
        return {'passed': True, 'reason': 'all_constraints_satisfied'}


class GameRuleEngine:
    """游戏规则引擎"""
    
    def validate_game_rules(self, current_cards: List[Dict], 
                          state_history: List[EnhancedGameState]) -> Dict:
        """验证游戏规则"""
        # 简化的规则验证
        return {'passed': True, 'reason': 'rules_satisfied'}
