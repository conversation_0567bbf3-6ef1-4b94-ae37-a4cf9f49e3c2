"""
测试数字孪生系统V3.0的物理ID永久性原则
验证物理ID一经分配永不消失的核心设计原则
"""

import sys
import logging
sys.path.insert(0, '.')

from src.core.digital_twin_v3 import create_digital_twin_system

# 设置详细日志
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')

def test_physical_id_permanence():
    """测试物理ID永久性原则"""
    print("🧪 测试物理ID永久性原则")
    print("-" * 50)
    
    dt = create_digital_twin_system()
    
    # 第一帧：建立基础ID
    frame1 = [
        {'label': '二', 'bbox': [100, 100, 150, 200], 'confidence': 0.9, 'group_id': 1},
        {'label': '二', 'bbox': [200, 100, 250, 200], 'confidence': 0.8, 'group_id': 1},
        {'label': '三', 'bbox': [300, 100, 350, 200], 'confidence': 0.9, 'group_id': 1},
        {'label': '四', 'bbox': [400, 100, 450, 200], 'confidence': 0.8, 'group_id': 1},
    ]
    
    result1 = dt.process_frame(frame1, frame_id=1)
    cards1 = result1['digital_twin_cards']
    
    print("第一帧ID分配:")
    physical_ids_frame1 = []
    for card in cards1:
        if not card.is_virtual:
            print(f"  {card.twin_id} - {card.label} - bbox: {card.bbox}")
            physical_ids_frame1.append(card.twin_id)
    
    # 第二帧：2_二被遮挡（消失）
    frame2 = [
        {'label': '二', 'bbox': [105, 105, 155, 205], 'confidence': 0.9, 'group_id': 1},  # 1_二
        {'label': '三', 'bbox': [305, 105, 355, 205], 'confidence': 0.9, 'group_id': 1},  # 1_三
        {'label': '四', 'bbox': [405, 105, 455, 205], 'confidence': 0.8, 'group_id': 1},  # 1_四
        # 2_二被遮挡，不在检测结果中
    ]
    
    result2 = dt.process_frame(frame2, frame_id=2)
    cards2 = result2['digital_twin_cards']
    
    print("\n第二帧处理（2_二被遮挡）:")
    physical_ids_frame2 = []
    compensated_count = 0
    for card in cards2:
        if not card.is_virtual:
            status = "补偿" if hasattr(card, 'is_compensated') and card.is_compensated else "检测"
            print(f"  {card.twin_id} - {card.label} - bbox: {card.bbox} ({status})")
            physical_ids_frame2.append(card.twin_id)
            if hasattr(card, 'is_compensated') and card.is_compensated:
                compensated_count += 1
    
    # 验证物理ID永久性
    ids_preserved = set(physical_ids_frame1) == set(physical_ids_frame2)
    print(f"\n物理ID永久性验证:")
    print(f"  第一帧ID: {sorted(physical_ids_frame1)}")
    print(f"  第二帧ID: {sorted(physical_ids_frame2)}")
    print(f"  ID完全保持: {'✅' if ids_preserved else '❌'}")
    print(f"  遮挡补偿数量: {compensated_count}")
    
    # 第三帧：2_二重新出现，添加新卡牌
    frame3 = [
        {'label': '二', 'bbox': [110, 110, 160, 210], 'confidence': 0.9, 'group_id': 1},  # 1_二
        {'label': '二', 'bbox': [205, 105, 255, 205], 'confidence': 0.8, 'group_id': 1},  # 2_二重新出现
        {'label': '三', 'bbox': [310, 110, 360, 210], 'confidence': 0.9, 'group_id': 1},  # 1_三
        {'label': '四', 'bbox': [410, 110, 460, 210], 'confidence': 0.8, 'group_id': 1},  # 1_四
        {'label': '五', 'bbox': [500, 100, 550, 200], 'confidence': 0.9, 'group_id': 1},  # 新卡牌
    ]
    
    result3 = dt.process_frame(frame3, frame_id=3)
    cards3 = result3['digital_twin_cards']
    
    print("\n第三帧处理（2_二重新出现，新增五）:")
    physical_ids_frame3 = []
    inherited_count = 0
    new_count = 0
    for card in cards3:
        if not card.is_virtual:
            if card.twin_id in physical_ids_frame1:
                status = "继承"
                inherited_count += 1
            else:
                status = "新增"
                new_count += 1
            print(f"  {card.twin_id} - {card.label} - bbox: {card.bbox} ({status})")
            physical_ids_frame3.append(card.twin_id)
    
    print(f"\n第三帧统计:")
    print(f"  继承ID数量: {inherited_count}")
    print(f"  新增ID数量: {new_count}")
    print(f"  继承率: {result3['statistics']['inheritance_rate']:.1f}%")
    
    # 验证新卡牌分配的正确性
    expected_new_id = "1_五"  # 五是新类型，应该分配1_五
    actual_new_ids = [card.twin_id for card in cards3 if card.label == '五']
    new_id_correct = len(actual_new_ids) == 1 and actual_new_ids[0] == expected_new_id
    
    print(f"\n新卡牌ID分配验证:")
    print(f"  期望新ID: {expected_new_id}")
    print(f"  实际新ID: {actual_new_ids}")
    print(f"  分配正确: {'✅' if new_id_correct else '❌'}")
    
    return ids_preserved and new_id_correct

def test_occlusion_compensation_mechanism():
    """测试遮挡补偿机制"""
    print("\n🧪 测试遮挡补偿机制")
    print("-" * 50)
    
    dt = create_digital_twin_system()
    
    # 第一帧：建立基础ID
    frame1 = [
        {'label': '二', 'bbox': [100, 100, 150, 200], 'confidence': 0.9, 'group_id': 1},
        {'label': '三', 'bbox': [200, 100, 250, 200], 'confidence': 0.8, 'group_id': 1},
    ]
    
    result1 = dt.process_frame(frame1, frame_id=1)
    
    # 第二帧：所有卡牌都被遮挡
    frame2 = []  # 空帧，所有卡牌被遮挡
    
    result2 = dt.process_frame(frame2, frame_id=2)
    cards2 = result2['digital_twin_cards']
    
    print("遮挡补偿测试:")
    compensated_ids = []
    for card in cards2:
        if not card.is_virtual:
            is_compensated = hasattr(card, 'is_compensated') and card.is_compensated
            print(f"  {card.twin_id} - {card.label} - 补偿: {'是' if is_compensated else '否'}")
            if is_compensated:
                compensated_ids.append(card.twin_id)
    
    # 验证所有物理ID都被补偿
    expected_compensated = ['1_二', '1_三']
    compensation_complete = set(compensated_ids) == set(expected_compensated)
    
    print(f"\n补偿机制验证:")
    print(f"  期望补偿ID: {sorted(expected_compensated)}")
    print(f"  实际补偿ID: {sorted(compensated_ids)}")
    print(f"  补偿完整: {'✅' if compensation_complete else '❌'}")
    
    return compensation_complete

def main():
    """运行所有测试"""
    print("🚀 数字孪生系统V3.0物理ID永久性原则测试")
    print("=" * 60)
    
    tests = [
        test_physical_id_permanence,
        test_occlusion_compensation_mechanism
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"❌ 测试失败: {e}")
            results.append(False)
    
    print("\n" + "=" * 60)
    print("📊 测试总结:")
    success_count = sum(results)
    total_count = len(results)
    
    test_names = ["物理ID永久性原则", "遮挡补偿机制"]
    
    for i, (name, result) in enumerate(zip(test_names, results)):
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  测试{i+1} ({name}): {status}")
    
    print(f"\n🎯 总体结果: {success_count}/{total_count} 测试通过")
    
    if success_count == total_count:
        print("🎉 物理ID永久性原则完全符合设计要求！")
    else:
        print("⚠️ 系统需要进一步调整以符合设计原则。")

if __name__ == "__main__":
    main()
