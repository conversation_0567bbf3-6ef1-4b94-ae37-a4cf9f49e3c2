"""
主流程演示 - 展示如何使用数字孪生主控器
这是未来主流程的标准使用方式

演示内容：
1. 初始化数字孪生主控器
2. 处理YOLO检测结果
3. 策略切换演示
4. 性能监控
"""

import sys
import os
from pathlib import Path
from typing import List, Dict, Any

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.core.digital_twin_controller import (
    create_digital_twin_controller,
    create_default_controller,
    create_basic_controller,
    create_complete_controller,
    DigitalTwinConfig,
    ProcessingStrategy
)

def simulate_yolo_detections() -> List[Dict[str, Any]]:
    """模拟YOLO检测结果"""
    return [
        {
            'label': '二',
            'bbox': [100, 100, 150, 150],
            'confidence': 0.95,
            'group_id': 1,
            'region_name': '手牌_观战方',
            'owner': 'spectator'
        },
        {
            'label': '七',
            'bbox': [200, 100, 250, 150],
            'confidence': 0.92,
            'group_id': 1,
            'region_name': '手牌_观战方',
            'owner': 'spectator'
        },
        {
            'label': '暗',
            'bbox': [300, 100, 350, 150],
            'confidence': 0.88,
            'group_id': 6,
            'region_name': '吃碰区_观战方',
            'owner': 'spectator'
        }
    ]

def demo_basic_usage():
    """演示基本使用方式"""
    print("🎯 演示1：基本使用方式")
    print("=" * 50)
    
    # 创建默认主控器（使用完整功能）
    controller = create_default_controller()
    
    # 获取系统状态
    status = controller.get_system_status()
    print(f"当前策略: {status['current_strategy']}")
    print(f"可用策略: {status['available_strategies']}")
    
    # 模拟检测数据
    detections = simulate_yolo_detections()
    print(f"输入检测数据: {len(detections)} 张卡牌")
    
    # 处理数据
    result = controller.process_frame(detections)
    
    # 输出结果
    print(f"处理结果: {'成功' if result.success else '失败'}")
    print(f"处理卡牌数: {len(result.processed_cards)}")
    print(f"处理时间: {result.processing_time:.3f}s")
    print(f"使用策略: {result.strategy_used}")
    
    if result.processed_cards:
        print("数字孪生ID分配结果:")
        for card in result.processed_cards:
            twin_id = card.get('twin_id', 'N/A')
            label = card.get('label', 'N/A')
            is_virtual = card.get('is_virtual', False)
            print(f"  - {label} → {twin_id} {'(虚拟)' if is_virtual else '(物理)'}")
    
    print()

def demo_strategy_switching():
    """演示策略切换"""
    print("🔄 演示2：策略切换")
    print("=" * 50)
    
    # 创建主控器
    controller = create_complete_controller()
    detections = simulate_yolo_detections()
    
    # 使用完整功能处理
    print("使用完整功能策略:")
    result1 = controller.process_frame(detections)
    print(f"  策略: {result1.strategy_used}")
    print(f"  处理时间: {result1.processing_time:.3f}s")
    print(f"  卡牌数: {len(result1.processed_cards)}")
    
    # 切换到基础功能
    controller.switch_strategy(ProcessingStrategy.PHASE1_BASIC)
    print("\n切换到基础功能策略:")
    result2 = controller.process_frame(detections)
    print(f"  策略: {result2.strategy_used}")
    print(f"  处理时间: {result2.processing_time:.3f}s")
    print(f"  卡牌数: {len(result2.processed_cards)}")
    
    # 切换回完整功能
    controller.switch_strategy(ProcessingStrategy.PHASE2_COMPLETE)
    print("\n切换回完整功能策略:")
    result3 = controller.process_frame(detections)
    print(f"  策略: {result3.strategy_used}")
    print(f"  处理时间: {result3.processing_time:.3f}s")
    print(f"  卡牌数: {len(result3.processed_cards)}")
    
    print()

def demo_performance_monitoring():
    """演示性能监控"""
    print("📊 演示3：性能监控")
    print("=" * 50)
    
    controller = create_default_controller()
    detections = simulate_yolo_detections()
    
    # 处理多帧数据
    for i in range(5):
        result = controller.process_frame(detections)
        print(f"处理第{i+1}帧: {'成功' if result.success else '失败'}, "
              f"耗时: {result.processing_time:.3f}s")
    
    # 获取性能统计
    stats = controller.get_performance_stats()
    print(f"\n性能统计:")
    print(f"  总处理帧数: {stats['total_frames_processed']}")
    print(f"  总处理卡牌数: {stats['total_cards_processed']}")
    print(f"  平均处理时间: {stats['average_processing_time']:.3f}s")
    print(f"  错误次数: {stats['error_count']}")
    print(f"  策略使用统计: {stats['strategy_usage']}")
    
    print()

def demo_custom_configuration():
    """演示自定义配置"""
    print("⚙️ 演示4：自定义配置")
    print("=" * 50)
    
    # 创建自定义配置
    custom_config = DigitalTwinConfig(
        strategy=ProcessingStrategy.PHASE2_COMPLETE,
        enable_logging=True,
        log_level="DEBUG",
        performance_monitoring=True,
        enable_inheritance=True,
        enable_region_transition=True,
        enable_dark_card_processing=True,
        enable_occlusion_compensation=False,  # 关闭遮挡补偿
        max_cards_per_frame=30,
        dual_output_enabled=True
    )
    
    # 使用自定义配置创建主控器
    controller = create_digital_twin_controller(custom_config)
    
    # 获取系统状态
    status = controller.get_system_status()
    print("自定义配置:")
    for key, value in status['config'].items():
        print(f"  {key}: {value}")
    
    # 处理数据
    detections = simulate_yolo_detections()
    result = controller.process_frame(detections)
    
    print(f"\n处理结果:")
    print(f"  成功: {result.success}")
    print(f"  策略: {result.strategy_used}")
    print(f"  处理时间: {result.processing_time:.3f}s")
    
    print()

def demo_future_main_pipeline():
    """演示未来主流程的标准使用方式"""
    print("🚀 演示5：未来主流程标准使用方式")
    print("=" * 50)
    
    # 这就是未来主流程中的标准调用方式
    # 只需要一行代码创建主控器
    dt_controller = create_complete_controller()
    
    print("主流程初始化完成")
    print(f"数字孪生系统状态: {dt_controller.get_system_status()['current_strategy']}")
    
    # 模拟主流程中的处理循环
    print("\n模拟主流程处理:")
    for frame_id in range(3):
        print(f"\n处理第{frame_id+1}帧:")
        
        # 1. 获取YOLO检测结果（这里用模拟数据）
        yolo_detections = simulate_yolo_detections()
        print(f"  YOLO检测到 {len(yolo_detections)} 张卡牌")
        
        # 2. 数字孪生处理（统一入口）
        dt_result = dt_controller.process_frame(yolo_detections)
        
        # 3. 输出处理结果
        if dt_result.success:
            print(f"  ✅ 数字孪生处理成功")
            print(f"  📊 分配ID: {len(dt_result.processed_cards)} 张")
            print(f"  ⏱️ 处理时间: {dt_result.processing_time:.3f}s")
            
            # 4. 后续可以将结果传递给决策模块
            # decision_result = decision_engine.process(dt_result.processed_cards)
            
        else:
            print(f"  ❌ 数字孪生处理失败: {dt_result.validation_errors}")
    
    # 5. 获取整体性能统计
    final_stats = dt_controller.get_performance_stats()
    print(f"\n整体性能统计:")
    print(f"  处理帧数: {final_stats['total_frames_processed']}")
    print(f"  平均耗时: {final_stats['average_processing_time']:.3f}s")
    print(f"  成功率: {(final_stats['total_frames_processed'] - final_stats['error_count']) / final_stats['total_frames_processed'] * 100:.1f}%")

def main():
    """主函数"""
    print("🎮 数字孪生主控器演示程序")
    print("=" * 60)
    print("展示如何使用统一主控器进行数字孪生ID分配")
    print("这是calibration_gt_final_processor.py和未来主流程的标准使用方式")
    print("=" * 60)
    print()
    
    try:
        # 运行所有演示
        demo_basic_usage()
        demo_strategy_switching()
        demo_performance_monitoring()
        demo_custom_configuration()
        demo_future_main_pipeline()
        
        print("🎉 所有演示完成！")
        print("\n💡 关键要点:")
        print("1. calibration_gt_final_processor.py 现在使用 create_complete_controller()")
        print("2. 未来主流程只需要调用 dt_controller.process_frame(detections)")
        print("3. 支持策略切换，便于不同场景的优化")
        print("4. 统一的性能监控和错误处理")
        print("5. 配置集中管理，易于维护和扩展")
        
    except Exception as e:
        print(f"❌ 演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
