"""
检查当前项目实现的区域分配逻辑

验证内容：
1. 当前数字孪生系统是否只对21个有效卡牌类别分配区域
2. 是否会错误地给非卡牌类别（如"吃"、"碰"、"胡"、"打鸟选择"等）分配区域
3. 检查区域分配的过滤逻辑是否正确

测试策略：
- 使用包含各种类别的测试数据
- 验证系统的区域分配行为
- 确保只有21个有效卡牌类别被分配区域
"""

import sys
import os
import json
from pathlib import Path
from typing import List, Dict, Any, Tuple
from collections import Counter

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.core.digital_twin_v2 import (
    DigitalTwinCoordinator,
    CardDetection,
    create_digital_twin_system
)

class RegionAssignmentChecker:
    """区域分配逻辑检查器"""
    
    def __init__(self):
        self.dt_system = create_digital_twin_system()
        
        # 定义21个有效卡牌类别
        self.valid_card_categories = {
            "一", "二", "三", "四", "五", "六", "七", "八", "九", "十", "暗",
            "壹", "贰", "叁", "肆", "伍", "陆", "柒", "捌", "玖", "拾"
        }
        
        # 定义不应该分配区域的类别
        self.non_card_categories = {
            "吃", "碰", "胡", "过", "听", "杠", "摸", "出",
            "打鸟选择", "已准备", "选择", "确定", "取消", 
            "等待", "思考", "计时", "分数", "结算", "开始",
            "暂停", "继续", "退出", "设置", "帮助"
        }
    
    def is_valid_card_label(self, label: str) -> bool:
        """判断是否是有效的卡牌标签"""
        if not label:
            return False
        
        # 处理带数字前缀的标签（如"1八"、"2四"）
        import re
        match = re.match(r'^(\d+)(.+)$', label)
        if match:
            card_name = match.group(2)
        else:
            card_name = label
        
        return card_name in self.valid_card_categories
    
    def create_test_detections(self) -> List[CardDetection]:
        """创建包含各种类别的测试检测数据"""
        test_detections = []
        
        # 1. 有效卡牌类别测试
        valid_cards = [
            ("一", [100, 100, 150, 150]),
            ("二", [200, 100, 250, 150]),
            ("三", [300, 100, 350, 150]),
            ("暗", [400, 100, 450, 150]),
            ("1八", [100, 200, 150, 250]),  # 带数字前缀
            ("2四", [200, 200, 250, 250]),
        ]
        
        for label, bbox in valid_cards:
            detection = CardDetection(
                label=label,
                bbox=bbox,
                confidence=0.9,
                group_id=0,  # 让系统自己分配
                region_name="unknown",
                owner="test"
            )
            test_detections.append(detection)
        
        # 2. 非卡牌类别测试
        non_cards = [
            ("吃", [100, 300, 150, 350]),
            ("碰", [200, 300, 250, 350]),
            ("胡", [300, 300, 350, 350]),
            ("过", [400, 300, 450, 350]),
            ("打鸟选择", [100, 400, 200, 450]),
            ("已准备", [250, 400, 350, 450]),
            ("确定", [400, 400, 450, 450]),
        ]
        
        for label, bbox in non_cards:
            detection = CardDetection(
                label=label,
                bbox=bbox,
                confidence=0.9,
                group_id=0,  # 让系统自己分配
                region_name="unknown",
                owner="test"
            )
            test_detections.append(detection)
        
        return test_detections
    
    def test_region_assignment_logic(self) -> Dict[str, Any]:
        """测试区域分配逻辑"""
        print("🧪 测试当前项目的区域分配逻辑...")
        
        # 重置系统
        self.dt_system.reset_session()
        
        # 创建测试数据
        test_detections = self.create_test_detections()
        
        print(f"📊 测试数据包含:")
        valid_count = sum(1 for d in test_detections if self.is_valid_card_label(d.label))
        non_card_count = len(test_detections) - valid_count
        print(f"  有效卡牌: {valid_count}个")
        print(f"  非卡牌类别: {non_card_count}个")
        
        # 处理测试数据
        try:
            result = self.dt_system.process_frame(test_detections)
            digital_twin_cards = result["digital_twin_cards"]
            
            print(f"\n📋 系统处理结果:")
            print(f"  输入检测数: {len(test_detections)}")
            print(f"  输出数字孪生卡牌数: {len(digital_twin_cards)}")
            
        except Exception as e:
            print(f"❌ 系统处理失败: {e}")
            return {"error": "processing_failed"}
        
        # 分析结果
        analysis_results = {
            "total_input": len(test_detections),
            "total_output": len(digital_twin_cards),
            "valid_cards_assigned": [],
            "non_cards_assigned": [],
            "valid_cards_not_assigned": [],
            "non_cards_not_assigned": [],
            "assignment_errors": []
        }
        
        # 检查每个输入检测的处理结果
        for detection in test_detections:
            is_valid_card = self.is_valid_card_label(detection.label)
            
            # 查找对应的输出
            matching_output = None
            for dt_card in digital_twin_cards:
                if dt_card.label == detection.label:
                    # 检查位置是否匹配（简单的IoU检查）
                    if self._bbox_match(detection.bbox, dt_card.bbox):
                        matching_output = dt_card
                        break
            
            if matching_output:
                # 有输出
                if is_valid_card:
                    analysis_results["valid_cards_assigned"].append({
                        "label": detection.label,
                        "group_id": matching_output.group_id,
                        "region_name": matching_output.region_name
                    })
                else:
                    # 非卡牌类别被分配了区域 - 这是错误
                    analysis_results["non_cards_assigned"].append({
                        "label": detection.label,
                        "group_id": matching_output.group_id,
                        "region_name": matching_output.region_name
                    })
                    analysis_results["assignment_errors"].append(
                        f"非卡牌类别'{detection.label}'被错误分配到区域{matching_output.group_id}"
                    )
            else:
                # 没有输出
                if is_valid_card:
                    analysis_results["valid_cards_not_assigned"].append(detection.label)
                else:
                    analysis_results["non_cards_not_assigned"].append(detection.label)
        
        return analysis_results
    
    def _bbox_match(self, bbox1: List[float], bbox2: List[float], threshold: float = 0.5) -> bool:
        """简单的边界框匹配检查"""
        # 计算IoU
        x1_1, y1_1, x2_1, y2_1 = bbox1
        x1_2, y1_2, x2_2, y2_2 = bbox2
        
        # 计算交集
        x1_inter = max(x1_1, x1_2)
        y1_inter = max(y1_1, y1_2)
        x2_inter = min(x2_1, x2_2)
        y2_inter = min(y2_1, y2_2)
        
        if x2_inter <= x1_inter or y2_inter <= y1_inter:
            return False
        
        inter_area = (x2_inter - x1_inter) * (y2_inter - y1_inter)
        
        # 计算并集
        area1 = (x2_1 - x1_1) * (y2_1 - y1_1)
        area2 = (x2_2 - x1_2) * (y2_2 - y1_2)
        union_area = area1 + area2 - inter_area
        
        iou = inter_area / union_area if union_area > 0 else 0
        return iou > threshold
    
    def check_real_data_assignment(self) -> Dict[str, Any]:
        """检查真实数据的区域分配"""
        print("\n🔍 检查真实数据的区域分配...")
        
        # 检查calibration_gt数据
        calibration_gt_path = Path("legacy_assets/ceshi/calibration_gt/labels")
        
        if not calibration_gt_path.exists():
            print("⚠️ calibration_gt数据集未找到")
            return {"error": "dataset_not_found"}
        
        real_data_results = {
            "files_checked": 0,
            "total_detections": 0,
            "valid_cards_processed": 0,
            "non_cards_processed": 0,
            "assignment_errors": [],
            "category_statistics": Counter()
        }
        
        json_files = list(calibration_gt_path.glob("*.json"))[:5]  # 检查前5个文件
        
        for json_file in json_files:
            try:
                with open(json_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                # 构建检测数据
                detections = []
                for shape in data.get("shapes", []):
                    if len(shape.get("points", [])) >= 4:
                        points = shape["points"]
                        x1, y1 = points[0]
                        x2, y2 = points[2]
                        
                        label = shape.get("label", "")
                        
                        detection = CardDetection(
                            label=label,
                            bbox=[x1, y1, x2, y2],
                            confidence=1.0,
                            group_id=0,  # 让系统自己分配
                            region_name="unknown",
                            owner="test"
                        )
                        detections.append(detection)
                        
                        # 统计类别
                        is_valid_card = self.is_valid_card_label(label)
                        real_data_results["category_statistics"][f"{'valid' if is_valid_card else 'non_card'}_{label}"] += 1
                
                if not detections:
                    continue
                
                # 重置系统并处理
                self.dt_system.reset_session()
                result = self.dt_system.process_frame(detections)
                digital_twin_cards = result["digital_twin_cards"]
                
                # 分析结果
                for detection in detections:
                    is_valid_card = self.is_valid_card_label(detection.label)
                    real_data_results["total_detections"] += 1
                    
                    # 查找匹配的输出
                    matching_output = None
                    for dt_card in digital_twin_cards:
                        if dt_card.label == detection.label and self._bbox_match(detection.bbox, dt_card.bbox):
                            matching_output = dt_card
                            break
                    
                    if matching_output:
                        if is_valid_card:
                            real_data_results["valid_cards_processed"] += 1
                        else:
                            real_data_results["non_cards_processed"] += 1
                            real_data_results["assignment_errors"].append({
                                "file": json_file.name,
                                "label": detection.label,
                                "group_id": matching_output.group_id
                            })
                
                real_data_results["files_checked"] += 1
                
            except Exception as e:
                print(f"    ❌ 处理文件{json_file.name}时出错: {e}")
        
        return real_data_results
    
    def run_complete_check(self) -> Dict[str, Any]:
        """运行完整的区域分配逻辑检查"""
        print("🔍 检查当前项目的区域分配逻辑")
        print("=" * 50)
        
        # 1. 测试区域分配逻辑
        test_results = self.test_region_assignment_logic()
        
        if "error" in test_results:
            return test_results
        
        # 2. 检查真实数据
        real_data_results = self.check_real_data_assignment()
        
        # 3. 综合分析
        comprehensive_analysis = self._analyze_results(test_results, real_data_results)
        
        # 保存结果
        results = {
            "check_timestamp": "2025-01-17 09:20:00",
            "test_results": test_results,
            "real_data_results": real_data_results,
            "comprehensive_analysis": comprehensive_analysis
        }
        
        output_path = "analysis/region_assignment_check_results.json"
        os.makedirs("analysis", exist_ok=True)
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        
        print(f"\n💾 检查结果已保存: {output_path}")
        
        return results
    
    def _analyze_results(self, test_results: Dict, real_data_results: Dict) -> Dict[str, Any]:
        """分析检查结果"""
        print("\n📊 综合分析结果...")
        
        analysis = {
            "logic_correctness": True,
            "issues_found": [],
            "recommendations": []
        }
        
        # 分析测试结果
        if test_results.get("assignment_errors"):
            analysis["logic_correctness"] = False
            analysis["issues_found"].extend(test_results["assignment_errors"])
            print("❌ 测试发现问题:")
            for error in test_results["assignment_errors"]:
                print(f"    {error}")
        
        non_cards_assigned = len(test_results.get("non_cards_assigned", []))
        if non_cards_assigned > 0:
            analysis["logic_correctness"] = False
            analysis["issues_found"].append(f"测试中{non_cards_assigned}个非卡牌类别被错误分配区域")
        
        # 分析真实数据结果
        if "error" not in real_data_results:
            real_errors = len(real_data_results.get("assignment_errors", []))
            if real_errors > 0:
                analysis["logic_correctness"] = False
                analysis["issues_found"].append(f"真实数据中{real_errors}个非卡牌类别被错误分配区域")
                print("❌ 真实数据发现问题:")
                for error in real_data_results["assignment_errors"][:5]:
                    print(f"    {error['file']} - {error['label']} -> 区域{error['group_id']}")
        
        # 生成建议
        if analysis["logic_correctness"]:
            analysis["recommendations"].append("✅ 区域分配逻辑正确，只对21个有效卡牌类别分配区域")
            print("✅ 区域分配逻辑检查通过！")
        else:
            analysis["recommendations"].extend([
                "🔧 需要在数字孪生系统中添加卡牌类别过滤逻辑",
                "📋 建议在CardDetection处理前过滤非卡牌类别",
                "🧪 增加单元测试确保只处理有效卡牌类别"
            ])
            print("⚠️ 发现区域分配逻辑问题，需要修正")
        
        return analysis

def main():
    """主检查函数"""
    checker = RegionAssignmentChecker()
    results = checker.run_complete_check()
    
    if "error" in results:
        print(f"❌ 检查失败: {results['error']}")
        return False
    
    # 判断检查结果
    analysis = results.get("comprehensive_analysis", {})
    logic_correct = analysis.get("logic_correctness", False)
    
    if logic_correct:
        print("\n🎉 区域分配逻辑检查通过！系统只对21个有效卡牌类别分配区域")
        return True
    else:
        print("\n⚠️ 发现区域分配逻辑问题，需要修正")
        issues = analysis.get("issues_found", [])
        for issue in issues:
            print(f"  - {issue}")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
