#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
跑胡子状态转换测试
功能：
1. 加载或生成YOLO检测结果
2. 使用StateBuilder将检测结果转换为RLCard状态
3. 可视化状态表示
4. 可选：与预期状态进行比较
"""

import os
import sys
import json
import cv2
import numpy as np
import argparse
from pathlib import Path
from tqdm import tqdm

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from detect import CardDetector
from state_builder import StateBuilder, format_detections_for_state_builder

def visualize_state(image, state, output_path=None):
    """
    可视化状态表示
    
    Args:
        image: 输入图像
        state: RLCard状态
        output_path: 输出路径
    """
    # 复制图像，避免修改原图
    vis_img = image.copy()
    
    # 获取图像尺寸
    h, w = vis_img.shape[:2]
    
    # 添加黑色背景区域
    info_height = 300
    info_img = np.zeros((info_height, w, 3), dtype=np.uint8)
    vis_img = np.vstack([vis_img, info_img])
    
    # 定义颜色
    colors = {
        'hand': (0, 255, 0),        # 绿色
        'discard_pile': (0, 0, 255), # 红色
        'combo_cards': (255, 0, 0),  # 蓝色
        'opponent_discard_pile': (255, 255, 0), # 青色
        'opponent_combo_cards': (255, 0, 255),  # 紫色
    }
    
    # 绘制状态信息
    y_offset = h + 30
    font = cv2.FONT_HERSHEY_SIMPLEX
    font_scale = 0.6
    thickness = 1
    line_height = 25
    
    # 绘制手牌
    cv2.putText(vis_img, f"手牌: {state.get('hand', [])}", (10, y_offset), font, font_scale, colors['hand'], thickness)
    y_offset += line_height
    
    # 绘制弃牌堆
    cv2.putText(vis_img, f"弃牌堆: {state.get('discard_pile', [])}", (10, y_offset), font, font_scale, colors['discard_pile'], thickness)
    y_offset += line_height
    
    # 绘制组合牌
    cv2.putText(vis_img, f"组合牌: {state.get('combo_cards', [])}", (10, y_offset), font, font_scale, colors['combo_cards'], thickness)
    y_offset += line_height
    
    # 绘制对手弃牌
    cv2.putText(vis_img, f"对手弃牌: {state.get('opponent_discard_pile', [])}", (10, y_offset), font, font_scale, colors['opponent_discard_pile'], thickness)
    y_offset += line_height
    
    # 绘制对手组合牌
    cv2.putText(vis_img, f"对手组合牌: {state.get('opponent_combo_cards', [])}", (10, y_offset), font, font_scale, colors['opponent_combo_cards'], thickness)
    y_offset += line_height
    
    # 绘制最后动作
    cv2.putText(vis_img, f"最后动作: {state.get('last_action', 'None')}", (10, y_offset), font, font_scale, (255, 255, 255), thickness)
    y_offset += line_height
    
    # 绘制当前玩家
    current_player = "我方" if state.get('current_player', 0) == 0 else "对手"
    cv2.putText(vis_img, f"当前玩家: {current_player}", (10, y_offset), font, font_scale, (255, 255, 255), thickness)
    y_offset += line_height
    
    # 绘制合法动作
    cv2.putText(vis_img, f"合法动作: {state.get('legal_actions', [])}", (10, y_offset), font, font_scale, (255, 255, 255), thickness)
    
    # 保存或显示图像
    if output_path:
        cv2.imwrite(output_path, vis_img)
    
    return vis_img

def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='跑胡子状态转换测试')
    parser.add_argument('--images-dir', type=str, default='ceshi/calibration_gt/images', help='图像目录')
    parser.add_argument('--detections-dir', type=str, default='', help='检测结果目录，为空则实时检测')
    parser.add_argument('--model-path', type=str, default='models/best.pt', help='模型路径，仅在实时检测时使用')
    parser.add_argument('--config-path', type=str, default='src/config.json', help='配置文件路径')
    parser.add_argument('--output-dir', type=str, default='output/state_conversion_test', help='输出目录')
    parser.add_argument('--max-images', type=int, default=10, help='最大处理图像数量，0表示处理所有图像')
    parser.add_argument('--visualize', action='store_true', help='可视化结果')
    parser.add_argument('--save-json', action='store_true', help='保存JSON结果')
    return parser.parse_args()

def main():
    """主函数"""
    args = parse_args()
    
    # 创建输出目录
    os.makedirs(args.output_dir, exist_ok=True)
    
    # 初始化状态转换器
    state_builder = StateBuilder(args.config_path)
    
    # 初始化检测器（如果需要实时检测）
    detector = None
    if not args.detections_dir:
        detector = CardDetector(model_path=args.model_path)
    
    # 获取图像文件列表
    image_files = sorted([f for f in os.listdir(args.images_dir) if f.endswith(('.jpg', '.png', '.jpeg'))])
    
    # 如果设置了最大图像数量
    if args.max_images > 0:
        image_files = image_files[:args.max_images]
    
    # 处理每张图像
    for image_file in tqdm(image_files, desc="处理图像"):
        # 构建文件路径
        image_path = os.path.join(args.images_dir, image_file)
        
        # 加载图像
        image = cv2.imread(image_path)
        if image is None:
            print(f"警告：无法加载图像 {image_path}")
            continue
        
        # 获取检测结果
        if args.detections_dir:
            # 从文件加载检测结果
            detection_file = os.path.join(args.detections_dir, os.path.splitext(image_file)[0] + '.json')
            if not os.path.exists(detection_file):
                print(f"警告：未找到检测结果文件 {detection_file}")
                continue
                
            with open(detection_file, 'r', encoding='utf-8') as f:
                detections = json.load(f)
        else:
            # 实时检测
            if detector is None:
                print(f"警告：检测器未初始化")
                continue
                
            detections = detector.detect_image(image)
            detections = format_detections_for_state_builder(detections, image.shape)
        
        # 转换为RLCard状态
        rlcard_state = state_builder.yolo_to_rlcard_state(detections)
        
        # 可视化状态
        if args.visualize:
            output_image_path = os.path.join(args.output_dir, f"state_{os.path.splitext(image_file)[0]}.jpg")
            vis_img = visualize_state(image, rlcard_state, output_image_path)
        
        # 保存状态到JSON
        if args.save_json:
            output_json_path = os.path.join(args.output_dir, f"state_{os.path.splitext(image_file)[0]}.json")
            with open(output_json_path, 'w', encoding='utf-8') as f:
                # 将元组转换为列表以便JSON序列化
                serializable_state = {}
                for key, value in rlcard_state.items():
                    if isinstance(value, list):
                        serializable_state[key] = [list(item) if isinstance(item, tuple) else item for item in value]
                    else:
                        serializable_state[key] = value
                        
                json.dump(serializable_state, f, ensure_ascii=False, indent=2)
        
        # 转换为显示格式并打印
        display_state = state_builder.rlcard_to_display_format(rlcard_state)
        print(f"\n图像: {image_file}")
        for key, value in display_state.items():
            print(f"{key}: {value}")
        print("-" * 50)

if __name__ == "__main__":
    # 测试参数
    image_dir = "ceshi/calibration_gt/images"
    model_path = "D:/phz-ai-simple/best.pt"  # 使用绝对路径
    num_images = 20
    output_dir = "output/state_conversion"
    
    # 运行测试
    test_state_conversion(image_dir, model_path, num_images, output_dir) 