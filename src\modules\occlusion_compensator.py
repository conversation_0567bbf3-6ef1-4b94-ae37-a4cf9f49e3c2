"""
模块6：遮挡补偿器 (OcclusionCompensator)
只做一件事：补偿被遮挡的卡牌

核心原则：
1. 检测消失的卡牌：识别前一帧存在但当前帧消失的卡牌
2. 智能补偿策略：创建虚拟卡牌维持ID连续性
3. 过度补偿控制：避免创建过多虚拟卡牌
4. 80张总量控制：确保不超过物理牌总数
"""

from typing import Dict, List, Any, Tuple, Optional, Set
from dataclasses import dataclass
import logging

logger = logging.getLogger(__name__)

@dataclass
class CompensationResult:
    """遮挡补偿结果"""
    compensated_cards: List[Dict[str, Any]]
    statistics: Dict[str, Any]

class OcclusionCompensator:
    """遮挡补偿器 - 只负责补偿被遮挡的卡牌"""
    
    def __init__(self):
        # 前一帧的卡牌记录：{twin_id: 卡牌数据}
        self.previous_frame_cards: Dict[str, Dict[str, Any]] = {}
        
        # 补偿历史：{twin_id: 补偿次数}
        self.compensation_history: Dict[str, int] = {}
        
        # 补偿配置
        self.compensation_config = {
            "max_compensation_per_card": 3,    # 每张牌最多补偿3次
            "max_total_cards": 80,             # 总卡牌数限制
            "no_compensation_regions": {3, 5, 10, 11, 12},  # 🔧 修复：区域3(抓牌区)不需要补偿，因为会流转到区域1
            "compensation_regions": {1, 2, 4, 6, 7, 8, 9},  # 🔧 修复：移除区域3，需要补偿的区域
            "stable_regions": {6, 14, 15, 16} # 吃碰区稳定，优先补偿
        }

        # 区域流转关系映射 - 根据GAME_RULES.md
        self.region_flow_map = {
            # 观战方流转路径
            1: [2, 6, 14, 15],      # 手牌 → 调整、吃碰区、赢方区域
            2: [1, 4],              # 调整 → 手牌、打牌
            3: [1, 5, 6, 16],       # 🔧 修复：抓牌 → 手牌、弃牌、吃碰区
            4: [5, 6, 16],          # 打牌 → 弃牌、吃碰区

            # 对战方流转路径
            7: [6, 16, 9],          # 抓牌 → 观战方吃碰区、对战方吃碰区、弃牌
            8: [6, 16, 9],          # 打牌 → 观战方吃碰区、对战方吃碰区、弃牌
            9: [11],                # 弃牌 → 最终弃牌

            # 吃碰区流转路径
            6: [14, 15],            # 观战方吃碰区 → 赢方区域
            16: [14, 15],           # 对战方吃碰区 → 赢方区域
        }
        
        # 补偿统计
        self.compensation_stats = {
            "total_frames": 0,
            "total_missing_detected": 0,
            "total_compensated": 0,
            "total_skipped": 0,
            "compensation_by_region": {},
            "compensation_by_type": {}
        }
        
        logger.info("遮挡补偿器初始化完成")
    
    def process_compensation(self, current_cards: List[Dict[str, Any]]) -> CompensationResult:
        """处理遮挡补偿逻辑"""
        self.compensation_stats["total_frames"] += 1
        
        logger.info(f"开始处理遮挡补偿，当前帧{len(current_cards)}张卡牌")
        
        # 建立当前帧ID映射
        current_ids = {card.get('twin_id'): card for card in current_cards if card.get('twin_id')}
        
        # 检测消失的卡牌
        missing_cards = self._detect_missing_cards(current_ids)
        self.compensation_stats["total_missing_detected"] += len(missing_cards)
        
        # 执行补偿
        compensated_cards = []
        for missing_id, missing_card in missing_cards.items():
            if self._should_compensate(missing_id, missing_card, current_cards):
                compensated_card = self._create_compensation_card(missing_id, missing_card)
                compensated_cards.append(compensated_card)
                self.compensation_stats["total_compensated"] += 1
                
                logger.debug(f"补偿卡牌: {missing_id} 在区域{missing_card['group_id']}")
            else:
                self.compensation_stats["total_skipped"] += 1
                logger.debug(f"跳过补偿: {missing_id} (不满足补偿条件)")
        
        # 合并当前卡牌和补偿卡牌
        all_cards = current_cards + compensated_cards
        
        # 更新前一帧记录
        self._update_previous_frame(all_cards)
        
        # 生成统计信息
        statistics = self._generate_statistics(len(missing_cards), len(compensated_cards))
        
        logger.info(f"遮挡补偿完成: 检测到{len(missing_cards)}张消失, 补偿{len(compensated_cards)}张")
        
        return CompensationResult(
            compensated_cards=compensated_cards,
            statistics=statistics
        )
    
    def _detect_missing_cards(self, current_ids: Dict[str, Dict[str, Any]]) -> Dict[str, Dict[str, Any]]:
        """检测消失的卡牌"""
        missing_cards = {}
        
        for prev_id, prev_card in self.previous_frame_cards.items():
            if prev_id not in current_ids:
                # 这张牌在当前帧消失了
                missing_cards[prev_id] = prev_card
                logger.debug(f"检测到消失卡牌: {prev_id}")
        
        return missing_cards
    
    def _should_compensate(self, missing_id: str, missing_card: Dict[str, Any],
                          current_cards: List[Dict[str, Any]]) -> bool:
        """判断是否应该补偿"""
        missing_region = missing_card.get('group_id')

        # 检查1：区域是否需要补偿
        if missing_region in self.compensation_config["no_compensation_regions"]:
            logger.debug(f"跳过补偿: 区域{missing_region}不需要补偿")
            return False

        if missing_region not in self.compensation_config["compensation_regions"]:
            logger.debug(f"跳过补偿: 区域{missing_region}不在补偿范围内")
            return False

        # 检查2：流转搜索 - 核心逻辑
        if self._check_card_in_flow_regions(missing_id, missing_region, current_cards):
            logger.debug(f"跳过补偿: {missing_id} 已流转到其他区域")
            return False

        # 检查3：补偿次数限制
        if missing_id in self.compensation_history:
            if self.compensation_history[missing_id] >= self.compensation_config["max_compensation_per_card"]:
                logger.debug(f"跳过补偿: {missing_id} 已达最大补偿次数")
                return False

        # 检查4：总卡牌数限制
        total_cards = len(current_cards) + len([id for id in self.compensation_history.keys()])
        if total_cards >= self.compensation_config["max_total_cards"]:
            logger.debug(f"跳过补偿: 总卡牌数已达上限 ({total_cards})")
            return False

        # 检查5：虚拟卡牌不补偿
        if missing_card.get('is_virtual', False):
            logger.debug(f"跳过补偿: {missing_id} 是虚拟卡牌")
            return False

        # 检查6：避免过度补偿同一类型
        card_type = self._extract_card_type(missing_id)
        if card_type:
            type_compensation_count = self.compensation_stats["compensation_by_type"].get(card_type, 0)
            if type_compensation_count >= 4:  # 每种牌最多补偿4张
                logger.debug(f"跳过补偿: {card_type}类型已补偿{type_compensation_count}张")
                return False

        # 检查7：稳定区域优先补偿
        if missing_region in self.compensation_config["stable_regions"]:
            logger.debug(f"优先补偿: {missing_id} 在稳定区域{missing_region}")
            return True

        return True

    def _check_card_in_flow_regions(self, missing_id: str, missing_region: int,
                                   current_cards: List[Dict[str, Any]]) -> bool:
        """检查卡牌是否已流转到其他区域"""
        # 获取该区域的流转目标区域
        flow_regions = self.region_flow_map.get(missing_region, [])

        if not flow_regions:
            logger.debug(f"区域{missing_region}没有定义流转路径")
            return False

        # 在流转目标区域中搜索相同的数字孪生ID
        for card in current_cards:
            card_id = self._extract_card_id(card)
            card_region = card.get('group_id')

            if card_id == missing_id and card_region in flow_regions:
                logger.info(f"✅ 发现流转: {missing_id} 从区域{missing_region} → 区域{card_region}")
                return True

        # 也检查基础ID匹配（处理暗牌转明牌等情况）
        base_missing_id = self._extract_base_id(missing_id)
        if base_missing_id and base_missing_id != missing_id:
            for card in current_cards:
                card_id = self._extract_card_id(card)
                base_card_id = self._extract_base_id(card_id)
                card_region = card.get('group_id')

                if base_card_id == base_missing_id and card_region in flow_regions:
                    logger.info(f"✅ 发现基础ID流转: {missing_id}({base_missing_id}) 从区域{missing_region} → 区域{card_region}")
                    return True

        logger.debug(f"未在流转区域{flow_regions}中找到{missing_id}")
        return False

    def _extract_card_id(self, card: Dict[str, Any]) -> str:
        """提取卡牌的数字孪生ID"""
        # 尝试多种可能的ID字段
        if 'twin_id' in card and card['twin_id']:
            return card['twin_id']
        elif 'digital_twin_id' in card and card['digital_twin_id']:
            return card['digital_twin_id']
        elif 'attributes' in card and isinstance(card['attributes'], dict):
            return card['attributes'].get('digital_twin_id', '')
        return ''

    def _extract_base_id(self, twin_id: str) -> str:
        """提取基础ID（去除暗牌标记等）"""
        if not twin_id:
            return ''

        # 处理暗牌：1二暗 → 1二
        if twin_id.endswith('暗'):
            return twin_id[:-1]

        # 处理虚拟牌：虚拟二 → 二（但这种情况下基础ID匹配意义不大）
        if twin_id.startswith('虚拟'):
            return twin_id[2:]

        # 其他情况直接返回
        return twin_id

    def _create_compensation_card(self, missing_id: str, missing_card: Dict[str, Any]) -> Dict[str, Any]:
        """创建补偿卡牌"""
        compensation_card = missing_card.copy()
        
        # 标记为补偿卡牌
        compensation_card['is_compensated'] = True
        compensation_card['compensation_reason'] = '遮挡补偿'
        compensation_card['original_twin_id'] = missing_id
        
        # 更新补偿历史
        if missing_id not in self.compensation_history:
            self.compensation_history[missing_id] = 0
        self.compensation_history[missing_id] += 1
        
        # 更新统计
        region_id = missing_card.get('group_id')
        if region_id not in self.compensation_stats["compensation_by_region"]:
            self.compensation_stats["compensation_by_region"][region_id] = 0
        self.compensation_stats["compensation_by_region"][region_id] += 1
        
        card_type = self._extract_card_type(missing_id)
        if card_type:
            if card_type not in self.compensation_stats["compensation_by_type"]:
                self.compensation_stats["compensation_by_type"][card_type] = 0
            self.compensation_stats["compensation_by_type"][card_type] += 1
        
        logger.debug(f"创建补偿卡牌: {missing_id} (第{self.compensation_history[missing_id]}次补偿)")
        
        return compensation_card
    
    def _extract_card_type(self, twin_id: str) -> Optional[str]:
        """提取卡牌类型"""
        if not twin_id:
            return None
        
        # 去掉数字和区域信息，提取牌面
        card_type = ""
        for char in twin_id:
            if not char.isdigit():
                card_type += char
        
        # 去掉暗字和区域数字
        card_type = card_type.replace('暗', '').rstrip('0123456789')
        
        return card_type if card_type else None
    
    def _update_previous_frame(self, all_cards: List[Dict[str, Any]]):
        """更新前一帧记录"""
        self.previous_frame_cards.clear()
        
        for card in all_cards:
            twin_id = card.get('twin_id')
            if twin_id:
                self.previous_frame_cards[twin_id] = card
        
        logger.debug(f"更新前一帧记录: {len(self.previous_frame_cards)}张卡牌")
    
    def _generate_statistics(self, missing_count: int, compensated_count: int) -> Dict[str, Any]:
        """生成统计信息"""
        compensation_rate = compensated_count / missing_count if missing_count > 0 else 0
        
        # 计算总体补偿率
        total_compensation_rate = (
            self.compensation_stats["total_compensated"] / 
            self.compensation_stats["total_missing_detected"]
            if self.compensation_stats["total_missing_detected"] > 0 else 0
        )
        
        return {
            "current_frame": {
                "missing_detected": missing_count,
                "compensated": compensated_count,
                "skipped": missing_count - compensated_count,
                "compensation_rate": compensation_rate
            },
            "overall": {
                "total_frames": self.compensation_stats["total_frames"],
                "total_missing": self.compensation_stats["total_missing_detected"],
                "total_compensated": self.compensation_stats["total_compensated"],
                "total_skipped": self.compensation_stats["total_skipped"],
                "overall_compensation_rate": total_compensation_rate
            },
            "compensation_by_region": self.compensation_stats["compensation_by_region"].copy(),
            "compensation_by_type": self.compensation_stats["compensation_by_type"].copy(),
            "active_compensations": len(self.compensation_history),
            "compensation_config": self.compensation_config.copy()
        }
    
    def reset_compensation_history(self):
        """重置补偿历史（用于新局开始）"""
        self.previous_frame_cards.clear()
        self.compensation_history.clear()
        self.compensation_stats = {
            "total_frames": 0,
            "total_missing_detected": 0,
            "total_compensated": 0,
            "total_skipped": 0,
            "compensation_by_region": {},
            "compensation_by_type": {}
        }
        logger.info("遮挡补偿历史已重置")
    
    def get_compensation_rate(self) -> float:
        """获取当前补偿率"""
        if self.compensation_stats["total_missing_detected"] == 0:
            return 0.0
        return (
            self.compensation_stats["total_compensated"] / 
            self.compensation_stats["total_missing_detected"]
        )
    
    def get_compensation_summary(self) -> Dict[str, Any]:
        """获取补偿摘要"""
        return {
            "total_compensations": len(self.compensation_history),
            "compensation_rate": self.get_compensation_rate(),
            "most_compensated_regions": sorted(
                self.compensation_stats["compensation_by_region"].items(),
                key=lambda x: x[1], reverse=True
            )[:5],
            "most_compensated_types": sorted(
                self.compensation_stats["compensation_by_type"].items(),
                key=lambda x: x[1], reverse=True
            )[:5]
        }

def create_occlusion_compensator():
    """创建遮挡补偿器"""
    return OcclusionCompensator()
