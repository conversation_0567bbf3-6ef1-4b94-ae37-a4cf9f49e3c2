# 代码架构映射图

## 🏗️ 系统架构层次

### 第一层：核心引擎层 (src/core/)
```
核心推理引擎
├── 模型加载器 → 负责YOLOv8模型初始化
├── 推理处理器 → 执行目标检测推理
├── 后处理器 → NMS、置信度过滤
└── 结果格式化器 → 输出标准化
```

### 第二层：业务逻辑层 (src/)
```
业务处理模块
├── 双轨输出系统 → 多格式同步输出
├── 记忆机制 → 跨帧状态保持
├── 状态区域管理 → 游戏区域识别
└── 配置管理 → 参数和设置
```

### 第三层：工具和验证层 (tools/, tests/)
```
支持工具集
├── 数据处理工具 → 标注转换、清洗
├── 模型分析工具 → 性能评估、对比
├── 验证测试套件 → 端到端测试
└── 监控和诊断 → 性能监控
```

## 🔄 数据流向图

### 主要数据流
```
输入图像 → 预处理 → 模型推理 → 后处理 → 双轨输出
    ↓           ↓         ↓         ↓         ↓
  标准化    → 特征提取 → 检测结果 → 过滤筛选 → 格式化输出
```

### 记忆机制流
```
当前帧结果 → 记忆更新 → 状态融合 → 输出增强
     ↑           ↓         ↓         ↓
  历史状态 ← 记忆存储 ← 状态管理 ← 结果验证
```

## 📦 关键模块依赖关系

### 核心依赖
- **YOLOv8**: 主要检测模型
- **OpenCV**: 图像处理
- **NumPy**: 数值计算
- **PyTorch**: 深度学习框架

### 模块间依赖
```
main.py
├── core/inference_engine.py
├── core/model_loader.py
├── utils/data_processor.py
└── config/settings.py
```

## 🎯 关键文件功能说明

### 核心文件
- `src/main.py`: 主入口，协调各模块
- `src/core/`: 核心算法实现
- `models/yolov8l.pt`: 主要检测模型

### 配置文件
- `requirements.txt`: Python依赖
- `.cursorrules`: AI助手规则
- `card_size_baseline.json`: 基准配置

### 测试文件
- `tests/test_*.py`: 单元测试
- `*_verification.py`: 验证脚本
- `comprehensive_*.py`: 综合测试

## 🔧 开发工作流

### 1. 新功能开发
```
需求分析 → 设计方案 → 编码实现 → 单元测试 → 集成测试 → 性能验证
```

### 2. 模型优化
```
性能分析 → 瓶颈识别 → 优化实现 → 基准测试 → 回归验证
```

### 3. 数据处理
```
数据收集 → 质量检查 → 格式转换 → 标注验证 → 数据集更新
```

## 🚀 AI助手使用建议

### 分析代码时使用
```
@Files src/main.py 分析主程序流程
@Folders src/core 查看核心算法实现
@Files tests/test_*.py 检查测试覆盖情况
```

### 优化性能时使用
```
@Files tools/performance_*.py 分析性能瓶颈
@Folders models 检查模型状态
@Files output/analysis 查看分析结果
```

### 调试问题时使用
```
@Files *.log 查看日志信息
@Folders tests 运行相关测试
@Files tools/debug_*.py 使用调试工具
```

## 📈 项目演进历史

### 主要版本节点
1. **v1.0**: 基础YOLOv8检测
2. **v2.0**: 双轨输出系统
3. **v3.0**: 记忆机制集成
4. **v4.0**: 性能优化和稳定性提升

### 技术债务清单
- [ ] 代码重构：模块化改进
- [ ] 测试覆盖：提升到90%+
- [ ] 文档完善：API文档补充
- [ ] 性能优化：推理速度提升
