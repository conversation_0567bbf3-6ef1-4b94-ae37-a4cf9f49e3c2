#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
智能上下文生成器 - 为AI助手生成最佳上下文

功能：
1. 根据任务类型推荐相关文件
2. 生成智能的@Files和@Folders建议
3. 创建任务特定的上下文模板
4. 提供代码理解路径建议
"""

import json
import re
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

class TaskType(Enum):
    """任务类型枚举"""
    UNDERSTAND_PROJECT = "understand_project"
    DEBUG_ISSUE = "debug_issue"
    ADD_FEATURE = "add_feature"
    OPTIMIZE_PERFORMANCE = "optimize_performance"
    FIX_BUG = "fix_bug"
    WRITE_TEST = "write_test"
    REFACTOR_CODE = "refactor_code"
    ANALYZE_DATA = "analyze_data"

@dataclass
class ContextSuggestion:
    """上下文建议"""
    task_type: TaskType
    priority_files: List[str]
    supporting_files: List[str]
    folders_to_explore: List[str]
    context_commands: List[str]
    explanation: str

class ContextGenerator:
    """智能上下文生成器"""
    
    def __init__(self, project_index_path: str = "project_index.json"):
        self.project_index = self._load_project_index(project_index_path)
        self.context_templates = self._initialize_templates()
    
    def _load_project_index(self, index_path: str) -> Dict[str, Any]:
        """加载项目索引"""
        try:
            with open(index_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            print(f"项目索引文件未找到: {index_path}")
            print("请先运行 project_indexer.py 生成项目索引")
            return {}
    
    def _initialize_templates(self) -> Dict[TaskType, Dict[str, Any]]:
        """初始化上下文模板"""
        return {
            TaskType.UNDERSTAND_PROJECT: {
                "priority_patterns": ["main.py", "README.md", "ARCHITECTURE.md"],
                "folder_patterns": ["src/core", "docs"],
                "keywords": ["architecture", "overview", "guide"]
            },
            TaskType.DEBUG_ISSUE: {
                "priority_patterns": ["*.log", "test_*.py", "*_test.py"],
                "folder_patterns": ["tests", "logs", "output"],
                "keywords": ["error", "exception", "debug", "log"]
            },
            TaskType.ADD_FEATURE: {
                "priority_patterns": ["*detector*.py", "*processor*.py"],
                "folder_patterns": ["src/core", "src/utils"],
                "keywords": ["interface", "base", "factory"]
            },
            TaskType.OPTIMIZE_PERFORMANCE: {
                "priority_patterns": ["*performance*.py", "*benchmark*.py"],
                "folder_patterns": ["tools/analysis", "tests/performance"],
                "keywords": ["performance", "benchmark", "optimization"]
            },
            TaskType.FIX_BUG: {
                "priority_patterns": ["*verification*.py", "*validation*.py"],
                "folder_patterns": ["tests", "tools/fixes"],
                "keywords": ["fix", "bug", "issue", "error"]
            },
            TaskType.WRITE_TEST: {
                "priority_patterns": ["test_*.py", "*_test.py"],
                "folder_patterns": ["tests", "src/core"],
                "keywords": ["test", "validation", "verification"]
            },
            TaskType.REFACTOR_CODE: {
                "priority_patterns": ["*.py"],
                "folder_patterns": ["src"],
                "keywords": ["refactor", "structure", "architecture"]
            },
            TaskType.ANALYZE_DATA: {
                "priority_patterns": ["*analysis*.py", "*analyzer*.py"],
                "folder_patterns": ["tools/analysis", "data"],
                "keywords": ["analysis", "statistics", "report"]
            }
        }
    
    def _match_files_by_pattern(self, pattern: str) -> List[str]:
        """根据模式匹配文件"""
        if not self.project_index or 'files' not in self.project_index:
            return []
        
        matched_files = []
        for file_info in self.project_index['files']:
            file_path = file_info['path']
            
            # 简单的通配符匹配
            if pattern.startswith('*') and pattern.endswith('*'):
                keyword = pattern[1:-1]
                if keyword in file_path:
                    matched_files.append(file_path)
            elif pattern.startswith('*'):
                suffix = pattern[1:]
                if file_path.endswith(suffix):
                    matched_files.append(file_path)
            elif pattern.endswith('*'):
                prefix = pattern[:-1]
                if file_path.startswith(prefix):
                    matched_files.append(file_path)
            else:
                if pattern in file_path:
                    matched_files.append(file_path)
        
        return matched_files
    
    def _get_core_files(self) -> List[str]:
        """获取核心文件"""
        if not self.project_index or 'ai_suggestions' not in self.project_index:
            return []
        
        suggestions = self.project_index['ai_suggestions']
        core_files = []
        
        # 添加核心模块
        core_files.extend(suggestions.get('core_modules', []))
        
        # 添加入口点
        core_files.extend(suggestions.get('entry_points', []))
        
        # 添加推荐的上下文文件
        core_files.extend(suggestions.get('recommended_context_files', []))
        
        return list(set(core_files))  # 去重
    
    def _get_related_files(self, target_file: str) -> List[str]:
        """获取相关文件"""
        if not self.project_index or 'module_graph' not in self.project_index:
            return []
        
        related_files = []
        module_graph = self.project_index['module_graph']
        
        # 查找依赖此文件的模块
        target_module = target_file.replace('/', '.').replace('.py', '')
        
        for module, dependencies in module_graph.items():
            if target_module in dependencies:
                related_files.append(module.replace('.', '/') + '.py')
        
        # 查找此文件依赖的模块
        if target_module in module_graph:
            for dep in module_graph[target_module]:
                related_files.append(dep.replace('.', '/') + '.py')
        
        return related_files
    
    def generate_context_suggestion(self, 
                                  task_type: TaskType, 
                                  specific_files: Optional[List[str]] = None,
                                  keywords: Optional[List[str]] = None) -> ContextSuggestion:
        """生成上下文建议"""
        
        template = self.context_templates.get(task_type, {})
        priority_files = []
        supporting_files = []
        folders_to_explore = []
        
        # 1. 根据模板匹配文件
        for pattern in template.get('priority_patterns', []):
            matched = self._match_files_by_pattern(pattern)
            priority_files.extend(matched[:3])  # 限制数量
        
        # 2. 添加核心文件
        if task_type == TaskType.UNDERSTAND_PROJECT:
            priority_files.extend(self._get_core_files()[:5])
        
        # 3. 添加特定文件的相关文件
        if specific_files:
            for file in specific_files:
                priority_files.append(file)
                supporting_files.extend(self._get_related_files(file))
        
        # 4. 根据关键词搜索
        if keywords:
            for keyword in keywords:
                matched = self._match_files_by_pattern(f"*{keyword}*")
                supporting_files.extend(matched[:3])
        
        # 5. 添加文件夹建议
        folders_to_explore = template.get('folder_patterns', [])
        
        # 6. 去重和排序
        priority_files = list(dict.fromkeys(priority_files))[:5]  # 保持顺序去重
        supporting_files = list(dict.fromkeys(supporting_files))[:10]
        
        # 7. 生成上下文命令
        context_commands = self._generate_context_commands(
            priority_files, folders_to_explore
        )
        
        # 8. 生成解释
        explanation = self._generate_explanation(task_type, priority_files, folders_to_explore)
        
        return ContextSuggestion(
            task_type=task_type,
            priority_files=priority_files,
            supporting_files=supporting_files,
            folders_to_explore=folders_to_explore,
            context_commands=context_commands,
            explanation=explanation
        )
    
    def _generate_context_commands(self, files: List[str], folders: List[str]) -> List[str]:
        """生成上下文命令"""
        commands = []
        
        # 文件命令
        for file in files[:3]:  # 限制最重要的3个文件
            commands.append(f"@Files {file}")
        
        # 文件夹命令
        for folder in folders[:2]:  # 限制最重要的2个文件夹
            commands.append(f"@Folders {folder}")
        
        return commands
    
    def _generate_explanation(self, task_type: TaskType, files: List[str], folders: List[str]) -> str:
        """生成解释说明"""
        explanations = {
            TaskType.UNDERSTAND_PROJECT: "这些文件和文件夹包含了项目的核心架构和主要功能，是理解项目的最佳起点。",
            TaskType.DEBUG_ISSUE: "这些文件包含了测试、日志和验证相关的代码，有助于诊断和解决问题。",
            TaskType.ADD_FEATURE: "这些核心模块和工具文件是添加新功能时需要了解和可能修改的关键代码。",
            TaskType.OPTIMIZE_PERFORMANCE: "这些文件包含了性能分析和基准测试相关的代码，有助于性能优化。",
            TaskType.FIX_BUG: "这些验证和修复相关的文件有助于定位和修复bug。",
            TaskType.WRITE_TEST: "这些测试文件和核心代码有助于编写新的测试用例。",
            TaskType.REFACTOR_CODE: "这些源代码文件是重构时需要分析和修改的主要目标。",
            TaskType.ANALYZE_DATA: "这些分析工具和数据文件有助于进行数据分析任务。"
        }
        
        base_explanation = explanations.get(task_type, "这些文件和文件夹与您的任务相关。")
        
        if files:
            base_explanation += f" 重点关注 {', '.join(files[:2])} 等关键文件。"
        
        return base_explanation
    
    def get_quick_start_context(self) -> List[str]:
        """获取快速开始的上下文命令"""
        commands = [
            "@Files docs/AI_PROJECT_GUIDE.md 快速了解项目概况",
            "@Files docs/CODE_ARCHITECTURE_MAP.md 理解代码架构",
            "@Files src/main.py 查看主程序入口"
        ]
        
        if self.project_index and 'ai_suggestions' in self.project_index:
            core_modules = self.project_index['ai_suggestions'].get('core_modules', [])
            if core_modules:
                commands.append(f"@Files {core_modules[0]} 查看核心模块")
        
        return commands

def main():
    """主函数 - 交互式上下文生成"""
    generator = ContextGenerator()
    
    print("🤖 智能上下文生成器")
    print("=" * 50)
    
    # 显示任务类型选项
    print("请选择任务类型:")
    for i, task_type in enumerate(TaskType, 1):
        print(f"{i}. {task_type.value}")
    
    try:
        choice = int(input("\n请输入选择 (1-8): ")) - 1
        task_types = list(TaskType)
        
        if 0 <= choice < len(task_types):
            selected_task = task_types[choice]
            
            # 生成建议
            suggestion = generator.generate_context_suggestion(selected_task)
            
            print(f"\n📋 {selected_task.value} 的上下文建议:")
            print("=" * 50)
            print(f"说明: {suggestion.explanation}")
            print(f"\n🎯 推荐的上下文命令:")
            for cmd in suggestion.context_commands:
                print(f"  {cmd}")
            
            print(f"\n📁 重点文件:")
            for file in suggestion.priority_files:
                print(f"  - {file}")
            
            if suggestion.supporting_files:
                print(f"\n📄 支持文件:")
                for file in suggestion.supporting_files[:5]:
                    print(f"  - {file}")
        
        else:
            print("无效选择")
    
    except (ValueError, KeyboardInterrupt):
        print("\n程序退出")

if __name__ == "__main__":
    main()
