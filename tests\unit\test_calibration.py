#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
跑胡子卡牌检测校准测试
功能：
1. 加载标注数据集
2. 运行检测模型
3. 比较检测结果与标注结果
4. 计算准确率、召回率、F1分数等指标
5. 可视化比较结果
"""

import os
import cv2
import json
import numpy as np
from tqdm import tqdm
from detect import CardDetector
from state_builder import format_detections_for_state_builder

def load_ground_truth(json_path):
    """
    加载标注的真实数据
    
    Args:
        json_path: JSON文件路径
        
    Returns:
        解析后的真实数据
    """
    with open(json_path, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    # 提取标注数据
    annotations = []
    for shape in data.get('shapes', []):
        label = shape.get('label', '')
        points = shape.get('points', [])
        
        if len(points) == 2:  # 确保是矩形（有两个点：左上和右下）
            x1, y1 = points[0]
            x2, y2 = points[1]
            
            # 计算宽高
            w = x2 - x1
            h = y2 - y1
            
            annotations.append({
                'label': label,
                'bbox': [x1, y1, w, h]
            })
    
    return annotations

def calculate_iou(box1, box2):
    """
    计算两个边界框的IoU
    
    Args:
        box1: 第一个边界框 [x, y, w, h]
        box2: 第二个边界框 [x, y, w, h]
        
    Returns:
        IoU值
    """
    # 转换为 [x1, y1, x2, y2] 格式
    x1_1, y1_1, w1, h1 = box1
    x1_2, y1_2, w2, h2 = box2
    
    x2_1, y2_1 = x1_1 + w1, y1_1 + h1
    x2_2, y2_2 = x1_2 + w2, y1_2 + h2
    
    # 计算交集区域
    x1_i = max(x1_1, x1_2)
    y1_i = max(y1_1, y1_2)
    x2_i = min(x2_1, x2_2)
    y2_i = min(y2_1, y2_2)
    
    # 检查是否有交集
    if x2_i <= x1_i or y2_i <= y1_i:
        return 0.0
    
    # 计算交集面积
    intersection = (x2_i - x1_i) * (y2_i - y1_i)
    
    # 计算并集面积
    area1 = w1 * h1
    area2 = w2 * h2
    union = area1 + area2 - intersection
    
    # 计算IoU
    iou = intersection / union if union > 0 else 0
    
    return iou

def evaluate_detections(detections, ground_truth, iou_threshold=0.5):
    """
    评估检测结果
    
    Args:
        detections: 检测结果列表
        ground_truth: 真实标注列表
        iou_threshold: IoU阈值
        
    Returns:
        评估指标
    """
    # 按类别分组
    gt_by_class = {}
    for gt in ground_truth:
        label = gt['label']
        if label not in gt_by_class:
            gt_by_class[label] = []
        gt_by_class[label].append(gt)
    
    det_by_class = {}
    for det in detections:
        label = det['label']
        if label not in det_by_class:
            det_by_class[label] = []
        det_by_class[label].append(det)
    
    # 计算每个类别的指标
    metrics = {}
    all_classes = set(list(gt_by_class.keys()) + list(det_by_class.keys()))
    
    for cls in all_classes:
        gt_boxes = gt_by_class.get(cls, [])
        det_boxes = det_by_class.get(cls, [])
        
        # 初始化匹配状态
        gt_matched = [False] * len(gt_boxes)
        det_matched = [False] * len(det_boxes)
        
        # 计算所有可能的匹配
        for i, det in enumerate(det_boxes):
            best_iou = 0
            best_gt_idx = -1
            
            for j, gt in enumerate(gt_boxes):
                if gt_matched[j]:
                    continue
                
                iou = calculate_iou(det['bbox'], gt['bbox'])
                if iou > best_iou:
                    best_iou = iou
                    best_gt_idx = j
            
            # 如果找到匹配且IoU大于阈值
            if best_gt_idx >= 0 and best_iou >= iou_threshold:
                gt_matched[best_gt_idx] = True
                det_matched[i] = True
        
        # 计算指标
        tp = sum(det_matched)
        fp = len(det_boxes) - tp
        fn = len(gt_boxes) - sum(gt_matched)
        
        precision = tp / (tp + fp) if (tp + fp) > 0 else 0
        recall = tp / (tp + fn) if (tp + fn) > 0 else 0
        f1 = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0
        
        metrics[cls] = {
            'precision': precision,
            'recall': recall,
            'f1': f1,
            'tp': tp,
            'fp': fp,
            'fn': fn
        }
    
    # 计算总体指标
    total_tp = sum(m['tp'] for m in metrics.values())
    total_fp = sum(m['fp'] for m in metrics.values())
    total_fn = sum(m['fn'] for m in metrics.values())
    
    total_precision = total_tp / (total_tp + total_fp) if (total_tp + total_fp) > 0 else 0
    total_recall = total_tp / (total_tp + total_fn) if (total_tp + total_fn) > 0 else 0
    total_f1 = 2 * total_precision * total_recall / (total_precision + total_recall) if (total_precision + total_recall) > 0 else 0
    
    metrics['overall'] = {
        'precision': total_precision,
        'recall': total_recall,
        'f1': total_f1,
        'tp': total_tp,
        'fp': total_fp,
        'fn': total_fn
    }
    
    return metrics

def visualize_evaluation(image, detections, ground_truth, metrics, output_path=None):
    """
    可视化评估结果
    
    Args:
        image: 输入图像
        detections: 检测结果
        ground_truth: 真实标注
        metrics: 评估指标
        output_path: 输出路径
        
    Returns:
        可视化后的图像
    """
    # 复制图像
    vis_img = image.copy()
    
    # 绘制真实标注（绿色）
    for gt in ground_truth:
        x, y, w, h = gt['bbox']
        cv2.rectangle(vis_img, (int(x), int(y)), (int(x + w), int(y + h)), (0, 255, 0), 2)
        cv2.putText(vis_img, gt['label'], (int(x), int(y) - 5), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 2)
    
    # 绘制检测结果（红色）
    for det in detections:
        x, y, w, h = det['bbox']
        cv2.rectangle(vis_img, (int(x), int(y)), (int(x + w), int(y + h)), (0, 0, 255), 2)
        label = f"{det['label']} {det['confidence']:.2f}"
        cv2.putText(vis_img, label, (int(x), int(y + h) + 15), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 255), 2)
    
    # 绘制指标
    overall = metrics['overall']
    text = f"Precision: {overall['precision']:.2f}, Recall: {overall['recall']:.2f}, F1: {overall['f1']:.2f}"
    cv2.putText(vis_img, text, (10, 30), 
               cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
    
    # 保存或返回图像
    if output_path:
        cv2.imwrite(output_path, vis_img)
    
    return vis_img

def test_calibration(image_dir, label_dir, model_path, num_images=50, output_dir='output'):
    """
    校准测试
    
    Args:
        image_dir: 图像目录
        label_dir: 标签目录
        model_path: 模型路径
        num_images: 要处理的图像数量
        output_dir: 输出目录
    """
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    # 初始化检测器
    detector = CardDetector(model_path)
    
    # 获取图像文件列表
    image_files = [f for f in os.listdir(image_dir) if f.endswith(('.jpg', '.jpeg', '.png'))]
    image_files = sorted(image_files)[:num_images]
    
    all_metrics = {}
    
    for img_file in tqdm(image_files, desc="处理图像"):
        # 构建文件路径
        img_path = os.path.join(image_dir, img_file)
        label_path = os.path.join(label_dir, os.path.splitext(img_file)[0] + '.json')
        
        # 检查标签文件是否存在
        if not os.path.exists(label_path):
            print(f"警告：找不到标签文件 {label_path}")
            continue
        
        # 读取图像和标签
        image = cv2.imread(img_path)
        ground_truth = load_ground_truth(label_path)
        
        if image is None:
            print(f"警告：无法读取图像 {img_path}")
            continue
        
        # 检测卡牌
        detections = detector.detect_image(image)
        
        # 格式化检测结果
        formatted_detections = format_detections_for_state_builder(detections, image.shape)
        
        # 评估检测结果
        metrics = evaluate_detections(detections, ground_truth)
        all_metrics[img_file] = metrics
        
        # 可视化结果
        output_path = os.path.join(output_dir, f"eval_{img_file}")
        visualize_evaluation(image, detections, ground_truth, metrics, output_path)
    
    # 计算所有图像的平均指标
    avg_metrics = {
        'precision': 0,
        'recall': 0,
        'f1': 0
    }
    
    for metrics in all_metrics.values():
        avg_metrics['precision'] += metrics['overall']['precision']
        avg_metrics['recall'] += metrics['overall']['recall']
        avg_metrics['f1'] += metrics['overall']['f1']
    
    num_files = len(all_metrics)
    if num_files > 0:
        avg_metrics['precision'] /= num_files
        avg_metrics['recall'] /= num_files
        avg_metrics['f1'] /= num_files
    
    # 保存指标
    with open(os.path.join(output_dir, 'calibration_metrics.json'), 'w') as f:
        json.dump({
            'per_image': all_metrics,
            'average': avg_metrics
        }, f, indent=2)
    
    print(f"校准测试完成，结果保存到 {output_dir}")
    print(f"平均指标: Precision={avg_metrics['precision']:.4f}, Recall={avg_metrics['recall']:.4f}, F1={avg_metrics['f1']:.4f}")

if __name__ == "__main__":
    # 测试参数
    image_dir = "ceshi/calibration_gt/images"
    label_dir = "ceshi/calibration_gt/labels"
    model_path = "best.pt"  # 使用训练好的模型
    num_images = 50
    output_dir = "output/calibration"
    
    # 运行测试
    test_calibration(image_dir, label_dir, model_path, num_images, output_dir) 