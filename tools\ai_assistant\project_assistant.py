#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
项目理解助手 - 一键生成项目理解报告

功能：
1. 自动分析项目结构和复杂度
2. 生成AI友好的项目摘要
3. 创建智能导航建议
4. 提供最佳实践建议
"""

import json
import os
from pathlib import Path
from typing import Dict, List, Any
from datetime import datetime
import subprocess
import sys

class ProjectAssistant:
    """项目理解助手"""
    
    def __init__(self, project_root: str = "."):
        self.project_root = Path(project_root)
        self.output_dir = self.project_root / "docs" / "ai_assistant"
        self.output_dir.mkdir(parents=True, exist_ok=True)
    
    def run_project_indexer(self) -> Dict[str, Any]:
        """运行项目索引器"""
        print("[Analyzing project structure...]")

        indexer_path = self.project_root / "tools" / "ai_assistant" / "project_indexer.py"
        output_path = self.output_dir / "project_index.json"

        try:
            # 设置环境变量以支持UTF-8
            env = os.environ.copy()
            env['PYTHONIOENCODING'] = 'utf-8'

            # 运行项目索引器
            result = subprocess.run([
                sys.executable, str(indexer_path),
                "--root", str(self.project_root),
                "--output", str(output_path)
            ], capture_output=True, text=True, cwd=self.project_root, env=env)

            if result.returncode == 0:
                print("[Project index generated successfully]")
                with open(output_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
            else:
                print(f"[Project index generation failed]: {result.stderr}")
                return {}

        except Exception as e:
            print(f"[Failed to run project indexer]: {e}")
            return {}
    
    def generate_ai_summary(self, project_index: Dict[str, Any]) -> str:
        """生成AI友好的项目摘要"""
        if not project_index:
            return "项目索引不可用，无法生成摘要。"
        
        ai_suggestions = project_index.get('ai_suggestions', {})
        
        summary = f"""# 🤖 AI项目理解摘要

## 📊 项目概览
- **项目名称**: {project_index.get('project_name', 'Unknown')}
- **生成时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
- **总文件数**: {project_index.get('total_files', 0)}
- **代码行数**: {project_index.get('total_lines', 0):,}
- **测试覆盖率**: {ai_suggestions.get('test_coverage_estimate', 'N/A')}

## 🎯 核心模块 (AI优先理解)
"""
        
        core_modules = ai_suggestions.get('core_modules', [])
        for i, module in enumerate(core_modules[:5], 1):
            summary += f"{i}. `{module}` - 核心功能模块\n"
        
        summary += f"""
## 🚀 快速上手路径

### 第一步：理解项目架构
```
@Files docs/AI_PROJECT_GUIDE.md 项目概览
@Files docs/CODE_ARCHITECTURE_MAP.md 架构理解
@Files README.md 基础信息
```

### 第二步：分析核心代码
```
@Files {core_modules[0] if core_modules else 'src/main.py'} 主要入口
@Folders src/core 核心算法
@Folders src/utils 工具函数
```

### 第三步：查看测试和验证
```
@Folders tests 测试套件
@Folders tools 分析工具
@Folders output 输出结果
```

## 📁 重要文件导航

### 🔧 入口点
"""
        
        entry_points = ai_suggestions.get('entry_points', [])
        for entry in entry_points:
            summary += f"- `{entry}` - 程序入口\n"
        
        summary += "\n### ⚙️ 配置文件\n"
        config_files = ai_suggestions.get('configuration_files', [])
        for config in config_files[:3]:
            summary += f"- `{config}` - 配置文件\n"
        
        summary += "\n### 📚 文档文件\n"
        doc_files = ai_suggestions.get('documentation_files', [])
        for doc in doc_files[:3]:
            summary += f"- `{doc}` - 文档文件\n"
        
        summary += f"""
## 🔍 复杂度分析

### 最复杂的文件 (需要重点关注)
"""
        
        complex_files = ai_suggestions.get('most_complex_files', [])
        for i, file in enumerate(complex_files[:3], 1):
            summary += f"{i}. `{file}` - 高复杂度文件\n"
        
        summary += f"""
## 🎯 AI助手使用建议

### 理解项目时
- 先阅读 `docs/AI_PROJECT_GUIDE.md` 获取项目概览
- 使用 `@Folders src/core` 了解核心功能
- 查看 `@Files src/main.py` 理解程序流程

### 开发新功能时
- 参考核心模块的设计模式
- 查看相关测试文件了解预期行为
- 使用工具目录下的分析脚本

### 调试问题时
- 查看 `@Folders tests` 中的验证脚本
- 使用 `@Folders output` 查看最新结果
- 参考 `@Folders tools` 中的调试工具

## 📈 项目健康度指标
- **模块化程度**: {'高' if len(core_modules) > 3 else '中等' if len(core_modules) > 1 else '低'}
- **测试完整性**: {'良好' if 'test_coverage_estimate' in ai_suggestions and float(ai_suggestions['test_coverage_estimate'].rstrip('%')) > 70 else '需改进'}
- **文档完整性**: {'良好' if len(doc_files) > 3 else '需改进'}
- **代码组织**: {'良好' if len(config_files) > 0 else '需改进'}

## 🔗 相关资源
- [项目索引文件](./project_index.json) - 完整的项目结构数据
- [智能上下文生成器](../../tools/ai_assistant/context_generator.py) - 任务特定的上下文建议
- [代码重构计划](../CODE_REFACTORING_PLAN.md) - 代码优化建议
"""
        
        return summary
    
    def generate_context_cheatsheet(self) -> str:
        """生成上下文使用速查表"""
        cheatsheet = """# 🚀 Cursor Context 使用速查表

## 📋 常用Context命令模板

### 🎯 项目理解
```bash
# 快速了解项目
@Files docs/AI_PROJECT_GUIDE.md
@Files docs/CODE_ARCHITECTURE_MAP.md
@Files README.md

# 分析核心架构
@Folders src/core
@Files src/main.py
@Folders src/utils
```

### 🔧 开发调试
```bash
# 查看测试状态
@Folders tests
@Files *_verification.py
@Files *_test.py

# 性能分析
@Files tools/performance_*.py
@Folders output/analysis
@Files analysis/*.json
```

### 🐛 问题诊断
```bash
# 查看日志和错误
@Files *.log
@Folders output
@Files tools/debug_*.py

# 验证和修复
@Files *_validation.py
@Files tools/fixes/*.py
@Folders tests/integration
```

### 📊 数据分析
```bash
# 数据处理
@Folders data
@Files tools/data_*.py
@Files *_analyzer.py

# 结果分析
@Folders output/analysis
@Files analysis/*.md
@Files *_report.json
```

## 🎨 高级Context技巧

### 1. 组合使用
```bash
@Files src/main.py @Files src/core/detect.py 
分析主程序和检测模块的关系
```

### 2. 特定功能分析
```bash
@Files src/core/digital_twin_v2.py @Files tests/test_digital_twin_v2.py
理解数字孪生系统的实现和测试
```

### 3. 配置和文档
```bash
@Files .cursorrules @Files docs/AI_PROJECT_GUIDE.md
了解AI助手配置和项目指南
```

## 📚 任务特定模板

### 添加新功能
1. `@Files docs/CODE_ARCHITECTURE_MAP.md` - 理解架构
2. `@Folders src/core` - 查看核心模块
3. `@Files src/core/interfaces.py` - 了解接口规范
4. `@Folders tests` - 参考测试模式

### 性能优化
1. `@Files tools/performance_*.py` - 性能分析工具
2. `@Files analysis/performance_*.json` - 基准数据
3. `@Folders models` - 模型文件
4. `@Files tools/enhanced_model_validator.py` - 模型验证

### 修复Bug
1. `@Files *_verification.py` - 验证脚本
2. `@Folders tests` - 测试套件
3. `@Files tools/fixes/*.py` - 修复工具
4. `@Files *.log` - 日志文件

## 💡 最佳实践

### DO ✅
- 先查看项目指南和架构文档
- 使用组合Context获取完整信息
- 关注核心模块和接口定义
- 参考测试文件理解预期行为

### DON'T ❌
- 不要一次加载过多文件
- 不要忽略配置和文档文件
- 不要跳过测试和验证步骤
- 不要忘记查看最新的分析结果

## 🔍 快速诊断命令

```bash
# 项目健康检查
@Files docs/AI_PROJECT_GUIDE.md 项目概览
@Folders tests 测试状态
@Folders output 最新结果

# 代码质量检查
@Files docs/CODE_REFACTORING_PLAN.md 重构建议
@Files tools/analysis/*.py 分析工具
@Files analysis/*.md 分析报告

# 性能状态检查
@Files tools/test_yolov8l_performance.py 性能测试
@Files analysis/performance_*.json 性能数据
@Files models/*.pt 模型文件
```
"""
        return cheatsheet
    
    def save_reports(self, project_index: Dict[str, Any]):
        """保存所有报告"""
        try:
            print("📝 正在生成AI助手报告...")
        except UnicodeEncodeError:
            print("[正在生成AI助手报告...]")

        # 生成项目摘要
        summary = self.generate_ai_summary(project_index)
        summary_path = self.output_dir / "AI_PROJECT_SUMMARY.md"
        with open(summary_path, 'w', encoding='utf-8') as f:
            f.write(summary)
        try:
            print(f"✅ 项目摘要已保存: {summary_path}")
        except UnicodeEncodeError:
            print(f"[项目摘要已保存]: {summary_path}")

        # 生成上下文速查表
        cheatsheet = self.generate_context_cheatsheet()
        cheatsheet_path = self.output_dir / "CONTEXT_CHEATSHEET.md"
        with open(cheatsheet_path, 'w', encoding='utf-8') as f:
            f.write(cheatsheet)
        try:
            print(f"✅ 上下文速查表已保存: {cheatsheet_path}")
        except UnicodeEncodeError:
            print(f"[上下文速查表已保存]: {cheatsheet_path}")

        # 保存项目索引
        if project_index:
            index_path = self.output_dir / "project_index.json"
            with open(index_path, 'w', encoding='utf-8') as f:
                json.dump(project_index, f, indent=2, ensure_ascii=False)
            try:
                print(f"✅ 项目索引已保存: {index_path}")
            except UnicodeEncodeError:
                print(f"[项目索引已保存]: {index_path}")
    
    def run_full_analysis(self):
        """运行完整的项目分析"""
        try:
            print("🚀 开始完整的项目分析...")
            print("=" * 50)
        except UnicodeEncodeError:
            print("[开始完整的项目分析...]")
            print("=" * 50)

        # 1. 运行项目索引器
        project_index = self.run_project_indexer()

        # 2. 生成和保存报告
        self.save_reports(project_index)

        print("\n" + "=" * 50)
        try:
            print("🎉 项目分析完成！")
            print(f"📁 报告保存在: {self.output_dir}")
            print("\n📋 生成的文件:")
            print(f"  - AI_PROJECT_SUMMARY.md - AI项目理解摘要")
            print(f"  - CONTEXT_CHEATSHEET.md - Context使用速查表")
            print(f"  - project_index.json - 完整项目索引")

            print(f"\n🤖 建议的下一步:")
            print(f"  1. 阅读 {self.output_dir}/AI_PROJECT_SUMMARY.md")
            print(f"  2. 使用 {self.output_dir}/CONTEXT_CHEATSHEET.md 中的命令")
            print(f"  3. 在Cursor中尝试推荐的@Files和@Folders命令")
        except UnicodeEncodeError:
            print("[项目分析完成!]")
            print(f"报告保存在: {self.output_dir}")
            print("\n生成的文件:")
            print(f"  - AI_PROJECT_SUMMARY.md - AI项目理解摘要")
            print(f"  - CONTEXT_CHEATSHEET.md - Context使用速查表")
            print(f"  - project_index.json - 完整项目索引")

            print(f"\n建议的下一步:")
            print(f"  1. 阅读 {self.output_dir}/AI_PROJECT_SUMMARY.md")
            print(f"  2. 使用 {self.output_dir}/CONTEXT_CHEATSHEET.md 中的命令")
            print(f"  3. 在Cursor中尝试推荐的@Files和@Folders命令")

def main():
    """主函数"""
    import argparse
    import sys

    # 设置UTF-8编码输出
    if sys.platform == 'win32':
        try:
            import codecs
            sys.stdout = codecs.getwriter('utf-8')(sys.stdout.detach())
            sys.stderr = codecs.getwriter('utf-8')(sys.stderr.detach())
        except:
            # 如果设置失败，继续执行
            pass

    parser = argparse.ArgumentParser(description='项目理解助手')
    parser.add_argument('--root', default='.', help='项目根目录')

    args = parser.parse_args()

    assistant = ProjectAssistant(args.root)
    assistant.run_full_analysis()

if __name__ == "__main__":
    main()
