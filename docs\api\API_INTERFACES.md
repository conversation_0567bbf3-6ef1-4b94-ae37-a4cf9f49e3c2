# 模块接口规范

## 视觉层接口
```python
def detect_cards(image: np.ndarray) -> List[CardDetection]:
    """
    输入：图像数组
    输出：卡牌检测结果列表

    模型信息（2025-07-17更新）:
    - 模型: YOLOv8l ONNX
    - 路径: data/processed/train/weights/best.onnx
    - 参数: conf_threshold=0.25, iou_threshold=0.45
    - 性能: F1=97.7%, 精确率=98.1%, 召回率=97.2%
    """
    pass
```

## 状态层接口
```python
def build_game_state(detections: List[CardDetection]) -> GameState:
    """
    输入：检测结果
    输出：游戏状态对象
    """
    pass

def process_frame(detections: List[CardDetection]) -> DigitalTwinResult:
    """
    数字孪生系统V2.0核心接口
    输入：检测结果列表
    输出：数字孪生处理结果

    功能特性（2025-07-18更新）:
    - 物理约束管理
    - 帧间继承机制
    - 多帧共识验证
    - 记忆机制集成
    """
    pass

def export_synchronized_dual_format(result: DigitalTwinResult, width: int, height: int, image_name: str) -> DualFormatResult:
    """
    同步双轨输出接口
    输入：数字孪生结果、图像尺寸、图像名称
    输出：同步双轨格式结果

    输出格式（2025-07-18完成）:
    - RLCard格式：AI决策用，包含完整数字孪生信息
    - AnyLabeling格式：人工审核用，与zhuangtaiquyu训练集100%兼容
    - 一致性验证：100%同步，零信息丢失
    """
    pass
```

## 决策层接口
```python
def make_decision(state: GameState) -> Decision:
    """
    输入：游戏状态
    输出：决策建议
    """
    pass
```