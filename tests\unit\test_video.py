#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
跑胡子AI视频处理测试
功能：
1. 加载测试视频
2. 逐帧处理：检测 -> 状态转换 -> 决策
3. 生成可视化结果视频
4. 测量性能指标
"""

import os
import sys
import json
import cv2
import numpy as np
import argparse
import time
from pathlib import Path
from tqdm import tqdm

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from detect import CardDetector
from state_builder import StateBuilder, format_detections_for_state_builder
from decision import DecisionMaker

def visualize_frame(image, detections, state, decision, fps, detection_time, decision_time):
    """
    可视化单帧结果
    
    Args:
        image: 输入图像
        detections: 检测结果
        state: RLCard状态
        decision: 决策结果
        fps: 帧率
        detection_time: 检测时间(ms)
        decision_time: 决策时间(ms)
        
    Returns:
        可视化后的图像
    """
    # 复制图像，避免修改原图
    vis_img = image.copy()
    
    # 获取图像尺寸
    h, w = vis_img.shape[:2]
    
    # 绘制检测框
    for det in detections:
        x, y, width, height = det['bbox']
        label = det['label']
        conf = det['confidence']
        
        # 确定颜色（根据类别）
        color = (0, 255, 0)  # 默认绿色
        
        # 绘制边界框
        cv2.rectangle(vis_img, (int(x), int(y)), (int(x + width), int(y + height)), color, 2)
        
        # 绘制标签和置信度
        text = f"{label} {conf:.2f}"
        cv2.putText(vis_img, text, (int(x), int(y) - 5), cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 2)
    
    # 添加黑色背景区域用于显示状态和决策信息
    info_height = 150
    info_img = np.zeros((info_height, w, 3), dtype=np.uint8)
    vis_img = np.vstack([vis_img, info_img])
    
    # 绘制状态和决策信息
    y_offset = h + 30
    font = cv2.FONT_HERSHEY_SIMPLEX
    font_scale = 0.6
    thickness = 1
    line_height = 25
    
    # 绘制性能信息
    cv2.putText(vis_img, f"FPS: {fps:.2f} | 检测时间: {detection_time:.2f}ms | 决策时间: {decision_time:.2f}ms", 
                (10, y_offset), font, font_scale, (0, 255, 255), thickness)
    y_offset += line_height * 2
    
    # 绘制手牌信息（简化显示）
    hand_cards = state.get('hand', [])
    hand_text = f"手牌: {hand_cards[:5]}{'...' if len(hand_cards) > 5 else ''}"
    cv2.putText(vis_img, hand_text, (10, y_offset), font, font_scale, (0, 255, 0), thickness)
    y_offset += line_height
    
    # 绘制决策信息
    cv2.putText(vis_img, f"推荐动作: {decision.get('推荐动作', 'None')} | 置信度: {decision.get('置信度', '0%')} | 胜率: {decision.get('胜率', '0%')}", 
                (10, y_offset), font, font_scale, (0, 255, 255), thickness)
    
    return vis_img

def format_detections_for_state_builder(detections):
    """
    将检测结果格式化为StateBuilder可接受的格式
    
    Args:
        detections: 检测结果列表
        
    Returns:
        格式化后的检测结果
    """
    cards = []
    for det in detections:
        # 构造卡牌ID（简单示例，实际应根据项目需求调整）
        card_id = f"1_{det['label']}"  # 示例：1_二，表示"二"的第一张牌
        
        # 确定group_id（简单示例，实际应根据位置或其他规则确定）
        x, y, w, h = det['bbox']
        
        # 简单规则：根据y坐标确定区域
        # 这里只是示例，实际项目中应该有更复杂的规则
        if y < 100:
            group_id = 6  # 吃碰区_观战方
        elif y < 200:
            group_id = 1  # 手牌_观战方
        elif y < 250:
            group_id = 5  # 弃牌_观战方
        else:
            group_id = 9  # 弃牌_对战方
        
        cards.append({
            'id': card_id,
            'label': det['label'],
            'bbox': det['bbox'],
            'group_id': group_id,
            'confidence': det['confidence']
        })
    
    return {'cards': cards}

def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='跑胡子AI视频处理测试')
    parser.add_argument('--video-path', type=str, default='ceshi/shipin/video.mp4', help='视频文件路径')
    parser.add_argument('--model-path', type=str, default='models/best.pt', help='模型路径')
    parser.add_argument('--config-path', type=str, default='src/config.json', help='配置文件路径')
    parser.add_argument('--output-path', type=str, default='output/processed_video.mp4', help='输出视频路径')
    parser.add_argument('--conf-threshold', type=float, default=0.25, help='置信度阈值')
    parser.add_argument('--iou-threshold', type=float, default=0.45, help='IOU阈值')
    parser.add_argument('--max-frames', type=int, default=0, help='最大处理帧数，0表示处理所有帧')
    parser.add_argument('--frame-step', type=int, default=1, help='帧步长，每隔多少帧处理一次')
    parser.add_argument('--save-json', action='store_true', help='保存JSON结果')
    return parser.parse_args()

def main():
    """主函数"""
    args = parse_args()
    
    # 检查视频文件是否存在
    if not os.path.exists(args.video_path):
        print(f"错误：视频文件不存在 {args.video_path}")
        return
    
    # 创建输出目录
    output_dir = os.path.dirname(args.output_path)
    os.makedirs(output_dir, exist_ok=True)
    
    # 初始化组件
    detector = CardDetector(
        model_path=args.model_path,
        conf_threshold=args.conf_threshold,
        iou_threshold=args.iou_threshold
    )
    state_builder = StateBuilder(args.config_path)
    decision_maker = DecisionMaker()
    
    # 打开视频文件
    cap = cv2.VideoCapture(args.video_path)
    if not cap.isOpened():
        print(f"错误：无法打开视频文件 {args.video_path}")
        return
    
    # 获取视频信息
    fps = int(cap.get(cv2.CAP_PROP_FPS))
    width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    
    # 计算输出视频尺寸（考虑添加的信息区域）
    output_height = height + 150  # 增加150像素用于显示信息
    
    # 创建视频写入器
    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
    out = cv2.VideoWriter(args.output_path, fourcc, fps, (width, output_height))
    
    # 性能统计
    total_detection_time = 0
    total_state_time = 0
    total_decision_time = 0
    total_time = 0
    processed_frames = 0
    
    # 处理视频帧
    frame_idx = 0
    
    # 创建进度条
    pbar = tqdm(total=total_frames if args.max_frames == 0 else min(total_frames, args.max_frames))
    
    # 处理每一帧
    while True:
        # 读取帧
        ret, frame = cap.read()
        if not ret:
            break
        
        # 更新进度条
        pbar.update(1)
        
        # 如果设置了最大帧数，检查是否达到
        if args.max_frames > 0 and frame_idx >= args.max_frames:
            break
        
        # 如果设置了帧步长，检查是否需要处理当前帧
        if frame_idx % args.frame_step != 0:
            frame_idx += 1
            continue
        
        # 开始计时
        start_time = time.time()
        
        # 1. 检测阶段
        detection_start = time.time()
        detections = detector.detect_image(frame)
        detection_end = time.time()
        detection_time = (detection_end - detection_start) * 1000  # 转换为毫秒
        
        # 2. 状态转换阶段
        state_start = time.time()
        formatted_detections = format_detections_for_state_builder(detections)
        rlcard_state = state_builder.yolo_to_rlcard_state(formatted_detections)
        state_end = time.time()
        state_time = (state_end - state_start) * 1000  # 转换为毫秒
        
        # 3. 决策阶段
        decision_start = time.time()
        action, confidence = decision_maker.make_decision(rlcard_state)
        win_rate = decision_maker.get_win_rate(rlcard_state)
        decision_result = decision_maker.format_decision(action, confidence, win_rate)
        decision_end = time.time()
        decision_time = (decision_end - decision_start) * 1000  # 转换为毫秒
        
        # 计算总时间和FPS
        end_time = time.time()
        process_time = end_time - start_time
        frame_fps = 1.0 / process_time if process_time > 0 else 0
        
        # 更新统计信息
        total_detection_time += detection_time
        total_state_time += state_time
        total_decision_time += decision_time
        total_time += process_time
        processed_frames += 1
        
        # 可视化结果
        vis_frame = visualize_frame(
            frame, detections, rlcard_state, decision_result,
            frame_fps, detection_time, decision_time
        )
        
        # 写入输出视频
        out.write(vis_frame)
        
        # 保存结果到JSON
        if args.save_json:
            # 将结果转换为可序列化格式
            serializable_state = {}
            for key, value in rlcard_state.items():
                if isinstance(value, list):
                    serializable_state[key] = [list(item) if isinstance(item, tuple) else item for item in value]
                else:
                    serializable_state[key] = value
                    
            result = {
                'frame_idx': frame_idx,
                'detections': detections,
                'state': serializable_state,
                'decision': decision_result,
                'performance': {
                    'detection_time_ms': detection_time,
                    'state_time_ms': state_time,
                    'decision_time_ms': decision_time,
                    'total_time_ms': process_time * 1000,
                    'fps': frame_fps
                }
            }
            
            # 保存到JSON文件
            json_dir = os.path.join(output_dir, 'frames_json')
            os.makedirs(json_dir, exist_ok=True)
            json_path = os.path.join(json_dir, f"frame_{frame_idx:05d}.json")
            with open(json_path, 'w', encoding='utf-8') as f:
                json.dump(result, f, ensure_ascii=False, indent=2)
        
        # 更新帧索引
        frame_idx += 1
    
    # 关闭进度条
    pbar.close()
    
    # 释放资源
    cap.release()
    out.release()
    
    # 输出性能统计
    if processed_frames > 0:
        avg_detection_time = total_detection_time / processed_frames
        avg_state_time = total_state_time / processed_frames
        avg_decision_time = total_decision_time / processed_frames
        avg_total_time = total_time / processed_frames
        avg_fps = processed_frames / total_time if total_time > 0 else 0
        
        print("\n性能统计:")
        print(f"处理帧数: {processed_frames}/{total_frames}")
        print(f"平均检测时间: {avg_detection_time:.2f}ms")
        print(f"平均状态转换时间: {avg_state_time:.2f}ms")
        print(f"平均决策时间: {avg_decision_time:.2f}ms")
        print(f"平均总处理时间: {avg_total_time * 1000:.2f}ms")
        print(f"平均FPS: {avg_fps:.2f}")
        print(f"原始视频FPS: {fps}")
        print(f"实时系数: {avg_fps/fps:.2f}x")  # >1表示可以实时处理
        
        # 保存性能统计到JSON
        if args.save_json:
            performance_stats = {
                'processed_frames': processed_frames,
                'total_frames': total_frames,
                'avg_detection_time_ms': avg_detection_time,
                'avg_state_time_ms': avg_state_time,
                'avg_decision_time_ms': avg_decision_time,
                'avg_total_time_ms': avg_total_time * 1000,
                'avg_fps': avg_fps,
                'video_fps': fps,
                'realtime_factor': avg_fps/fps
            }
            
            with open(os.path.join(output_dir, 'video_performance_stats.json'), 'w', encoding='utf-8') as f:
                json.dump(performance_stats, f, ensure_ascii=False, indent=2)
    
    print(f"\n处理完成，输出视频保存至: {args.output_path}")

if __name__ == "__main__":
    # 测试参数
    video_path = "ceshi/shipin/video.mp4"  # 替换为实际视频路径
    model_path = "D:/phz-ai-simple/best.pt"  # 使用绝对路径
    output_path = "output/processed_video.mp4"
    max_frames = 300
    frame_step = 3
    
    # 运行测试
    test_video(video_path, model_path, output_path, max_frames, frame_step) 