#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
修复版标注对比分析器

对比原始标注、修复前标注和修复后标注的差异，
评估修复效果。
"""

import os
import json
import numpy as np
from typing import Dict, List, Any, Tuple
from collections import defaultdict, Counter


class FixedAnnotationComparator:
    """修复版标注对比分析器"""
    
    def __init__(self):
        """初始化对比器"""
        self.original_path = "legacy_assets/ceshi/calibration_gt/labels"
        self.enhanced_path = "legacy_assets/ceshi/calibration_gt_enhanced/labels"
        self.fixed_path = "legacy_assets/ceshi/calibration_gt_enhanced_fixed/labels"
        
        print(f"📊 修复版标注对比分析器初始化")
        print(f"   - 原始标注: {self.original_path}")
        print(f"   - 修复前: {self.enhanced_path}")
        print(f"   - 修复后: {self.fixed_path}")
    
    def compare_all_versions(self, sample_size: int = 50) -> Dict[str, Any]:
        """对比所有版本的标注"""
        print(f"🔍 开始三版本对比分析 (样本: {sample_size})...")
        
        # 获取共同文件
        original_files = set(f for f in os.listdir(self.original_path) if f.endswith('.json'))
        enhanced_files = set(f for f in os.listdir(self.enhanced_path) if f.endswith('.json'))
        fixed_files = set(f for f in os.listdir(self.fixed_path) if f.endswith('.json'))
        
        common_files = list(original_files & enhanced_files & fixed_files)[:sample_size]
        
        print(f"📋 分析 {len(common_files)} 个文件...")
        
        results = {
            'original_vs_enhanced': {'matches': 0, 'region_errors': 0, 'label_errors': 0},
            'original_vs_fixed': {'matches': 0, 'region_errors': 0, 'label_errors': 0},
            'enhanced_vs_fixed': {'matches': 0, 'region_errors': 0, 'label_errors': 0},
            'detailed_results': []
        }
        
        # 分析每个文件
        for i, filename in enumerate(common_files):
            try:
                file_result = self._compare_single_file(filename)
                results['detailed_results'].append(file_result)
                
                # 累计统计
                for comparison in ['original_vs_enhanced', 'original_vs_fixed', 'enhanced_vs_fixed']:
                    results[comparison]['matches'] += file_result[comparison]['matches']
                    results[comparison]['region_errors'] += file_result[comparison]['region_errors']
                    results[comparison]['label_errors'] += file_result[comparison]['label_errors']
                
                if (i + 1) % 10 == 0:
                    progress = (i + 1) / len(common_files) * 100
                    print(f"   进度: {i+1}/{len(common_files)} ({progress:.1f}%)")
                    
            except Exception as e:
                print(f"❌ 对比失败: {filename} - {e}")
        
        # 生成对比报告
        report = self._generate_comparison_report(results, len(common_files))
        
        print(f"✅ 三版本对比分析完成")
        return report
    
    def _compare_single_file(self, filename: str) -> Dict[str, Any]:
        """对比单个文件的三个版本"""
        # 读取三个版本的标注
        original_data = self._load_json(os.path.join(self.original_path, filename))
        enhanced_data = self._load_json(os.path.join(self.enhanced_path, filename))
        fixed_data = self._load_json(os.path.join(self.fixed_path, filename))
        
        # 提取检测结果
        original_detections = self._extract_detections(original_data)
        enhanced_detections = self._extract_detections(enhanced_data)
        fixed_detections = self._extract_detections(fixed_data)
        
        # 进行三组对比
        original_vs_enhanced = self._compare_detections(original_detections, enhanced_detections)
        original_vs_fixed = self._compare_detections(original_detections, fixed_detections)
        enhanced_vs_fixed = self._compare_detections(enhanced_detections, fixed_detections)
        
        return {
            'filename': filename,
            'counts': {
                'original': len(original_detections),
                'enhanced': len(enhanced_detections),
                'fixed': len(fixed_detections)
            },
            'original_vs_enhanced': original_vs_enhanced,
            'original_vs_fixed': original_vs_fixed,
            'enhanced_vs_fixed': enhanced_vs_fixed
        }
    
    def _load_json(self, filepath: str) -> Dict[str, Any]:
        """加载JSON文件"""
        with open(filepath, 'r', encoding='utf-8') as f:
            return json.load(f)
    
    def _extract_detections(self, data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """提取检测信息"""
        detections = []
        
        for shape in data.get('shapes', []):
            points = shape.get('points', [])
            if len(points) == 4:
                # 转换为[x, y, w, h]格式
                xs = [p[0] for p in points]
                ys = [p[1] for p in points]
                x, y = min(xs), min(ys)
                w, h = max(xs) - x, max(ys) - y
                
                detection = {
                    'label': shape.get('label', ''),
                    'bbox': [x, y, w, h],
                    'confidence': shape.get('score', 1.0),
                    'region_name': shape.get('region_name', ''),
                    'owner': shape.get('owner', ''),
                    'center': [x + w/2, y + h/2]
                }
                detections.append(detection)
        
        return detections
    
    def _compare_detections(self, detections1: List[Dict], detections2: List[Dict]) -> Dict[str, Any]:
        """对比两组检测结果"""
        matches = 0
        region_errors = 0
        label_errors = 0
        
        # 匹配检测结果
        matched_pairs = []
        used_indices = set()
        
        for det1 in detections1:
            best_match = None
            best_iou = 0
            best_idx = -1
            
            for i, det2 in enumerate(detections2):
                if i in used_indices:
                    continue
                
                iou = self._calculate_iou(det1['bbox'], det2['bbox'])
                if iou > best_iou and iou > 0.3:
                    best_iou = iou
                    best_match = det2
                    best_idx = i
            
            if best_match:
                used_indices.add(best_idx)
                matched_pairs.append((det1, best_match))
                matches += 1
                
                # 检查区域错误
                if det1['region_name'] != best_match['region_name']:
                    region_errors += 1
                
                # 检查标签错误
                if det1['label'] != best_match['label']:
                    label_errors += 1
        
        return {
            'matches': matches,
            'region_errors': region_errors,
            'label_errors': label_errors,
            'total_detections1': len(detections1),
            'total_detections2': len(detections2)
        }
    
    def _calculate_iou(self, bbox1: List[float], bbox2: List[float]) -> float:
        """计算IoU"""
        try:
            x1, y1, w1, h1 = bbox1
            x2, y2, w2, h2 = bbox2
            
            # 转换为 [x1, y1, x2, y2] 格式
            box1 = [x1, y1, x1 + w1, y1 + h1]
            box2 = [x2, y2, x2 + w2, y2 + h2]
            
            # 计算交集
            x_left = max(box1[0], box2[0])
            y_top = max(box1[1], box2[1])
            x_right = min(box1[2], box2[2])
            y_bottom = min(box1[3], box2[3])
            
            if x_right < x_left or y_bottom < y_top:
                return 0.0
            
            intersection = (x_right - x_left) * (y_bottom - y_top)
            
            # 计算并集
            area1 = w1 * h1
            area2 = w2 * h2
            union = area1 + area2 - intersection
            
            return intersection / union if union > 0 else 0.0
        
        except (ValueError, IndexError, ZeroDivisionError):
            return 0.0
    
    def _generate_comparison_report(self, results: Dict[str, Any], total_files: int) -> Dict[str, Any]:
        """生成对比报告"""
        
        def calculate_metrics(comparison_data):
            total_matches = comparison_data['matches']
            total_region_errors = comparison_data['region_errors']
            total_label_errors = comparison_data['label_errors']
            
            return {
                'total_matches': total_matches,
                'region_error_rate': total_region_errors / total_matches if total_matches > 0 else 0,
                'label_error_rate': total_label_errors / total_matches if total_matches > 0 else 0,
                'region_accuracy': 1 - (total_region_errors / total_matches) if total_matches > 0 else 0,
                'label_accuracy': 1 - (total_label_errors / total_matches) if total_matches > 0 else 0
            }
        
        report = {
            'summary': {
                'total_files_compared': total_files,
                'original_vs_enhanced': calculate_metrics(results['original_vs_enhanced']),
                'original_vs_fixed': calculate_metrics(results['original_vs_fixed']),
                'enhanced_vs_fixed': calculate_metrics(results['enhanced_vs_fixed'])
            },
            'improvement_analysis': self._analyze_improvements(results),
            'detailed_results': results['detailed_results']
        }
        
        # 保存报告
        report_path = "output/fixed_annotation_comparison_report.json"
        os.makedirs(os.path.dirname(report_path), exist_ok=True)
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        return report
    
    def _analyze_improvements(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """分析改进效果"""
        original_vs_enhanced = results['original_vs_enhanced']
        original_vs_fixed = results['original_vs_fixed']
        
        # 计算改进指标
        enhanced_region_accuracy = 1 - (original_vs_enhanced['region_errors'] / original_vs_enhanced['matches']) if original_vs_enhanced['matches'] > 0 else 0
        fixed_region_accuracy = 1 - (original_vs_fixed['region_errors'] / original_vs_fixed['matches']) if original_vs_fixed['matches'] > 0 else 0
        
        enhanced_label_accuracy = 1 - (original_vs_enhanced['label_errors'] / original_vs_enhanced['matches']) if original_vs_enhanced['matches'] > 0 else 0
        fixed_label_accuracy = 1 - (original_vs_fixed['label_errors'] / original_vs_fixed['matches']) if original_vs_fixed['matches'] > 0 else 0
        
        return {
            'region_accuracy_improvement': fixed_region_accuracy - enhanced_region_accuracy,
            'label_accuracy_improvement': fixed_label_accuracy - enhanced_label_accuracy,
            'region_accuracy_before': enhanced_region_accuracy,
            'region_accuracy_after': fixed_region_accuracy,
            'label_accuracy_before': enhanced_label_accuracy,
            'label_accuracy_after': fixed_label_accuracy,
            'overall_improvement': (fixed_region_accuracy + fixed_label_accuracy) - (enhanced_region_accuracy + enhanced_label_accuracy)
        }


def main():
    """主函数"""
    print("📊 修复版标注对比分析器")
    print("=" * 50)
    
    # 创建对比器
    comparator = FixedAnnotationComparator()
    
    # 进行三版本对比
    report = comparator.compare_all_versions(sample_size=50)
    
    # 打印结果
    print("\n📊 对比结果汇总:")
    print(f"   对比文件数: {report['summary']['total_files_compared']}")
    
    print("\n🔍 原始 vs 修复前:")
    enhanced_metrics = report['summary']['original_vs_enhanced']
    print(f"   区域准确率: {enhanced_metrics['region_accuracy']:.1%}")
    print(f"   标签准确率: {enhanced_metrics['label_accuracy']:.1%}")
    
    print("\n🔧 原始 vs 修复后:")
    fixed_metrics = report['summary']['original_vs_fixed']
    print(f"   区域准确率: {fixed_metrics['region_accuracy']:.1%}")
    print(f"   标签准确率: {fixed_metrics['label_accuracy']:.1%}")
    
    print("\n📈 改进效果:")
    improvements = report['improvement_analysis']
    print(f"   区域准确率改进: {improvements['region_accuracy_improvement']:+.1%}")
    print(f"   标签准确率改进: {improvements['label_accuracy_improvement']:+.1%}")
    print(f"   整体改进: {improvements['overall_improvement']:+.1%}")
    
    print(f"\n📁 详细报告已保存: output/fixed_annotation_comparison_report.json")


if __name__ == "__main__":
    main()
