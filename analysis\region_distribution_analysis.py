"""
区域分布深度分析脚本

本脚本深度分析zhuangtaiquyu数据集中各区域的真实位置分布特征，
为开发精确的区域推断算法提供数据支撑。

分析内容：
1. 各区域的位置分布统计
2. 区域边界分析
3. 重叠区域识别
4. 错误模式深度分析
"""

import sys
import os
import json
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path
from typing import List, Dict, Any, Tuple
from collections import defaultdict, Counter
import re

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

class RegionDistributionAnalyzer:
    """区域分布分析器"""
    
    def __init__(self):
        self.region_data = defaultdict(list)  # 存储各区域的位置数据
        self.card_name_mapping = {
            "壹": "一", "贰": "二", "叁": "三", "肆": "四", "伍": "五",
            "陆": "六", "柒": "七", "捌": "八", "玖": "九", "拾": "十"
        }
        
    def parse_ground_truth_label(self, label: str) -> <PERSON><PERSON>[str, str]:
        """解析人工标注标签"""
        match = re.match(r'^(\d+)(.+)$', label)
        if match:
            twin_id = match.group(1)
            card_name = match.group(2)
            
            # 转换繁体到简体
            if card_name in self.card_name_mapping:
                card_name = self.card_name_mapping[card_name]
            
            return twin_id, card_name
        else:
            return "", label
    
    def collect_region_data(self, data_path: Path):
        """收集区域分布数据"""
        print("📊 收集区域分布数据...")
        
        sequence_dirs = [d for d in data_path.iterdir() if d.is_dir()]
        sequence_dirs = sorted(sequence_dirs, key=lambda x: int(x.name) if x.name.isdigit() else 0)
        
        total_cards = 0
        
        for seq_dir in sequence_dirs[:5]:  # 分析前5个序列
            print(f"  📁 处理序列: {seq_dir.name}")
            
            json_files = sorted(seq_dir.glob("*.json"))
            for json_file in json_files:
                try:
                    with open(json_file, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                    
                    image_width = data.get("imageWidth", 640)
                    image_height = data.get("imageHeight", 320)
                    
                    for shape in data.get("shapes", []):
                        if len(shape.get("points", [])) >= 4:
                            points = shape["points"]
                            x1, y1 = points[0]
                            x2, y2 = points[2]
                            
                            # 计算中心点和归一化坐标
                            x_center = (x1 + x2) / 2
                            y_center = (y1 + y2) / 2
                            x_norm = x_center / image_width
                            y_norm = y_center / image_height
                            
                            # 计算卡牌尺寸
                            width = x2 - x1
                            height = y2 - y1
                            
                            region_id = shape.get("group_id", 0)
                            label = shape.get("label", "")
                            twin_id, card_name = self.parse_ground_truth_label(label)
                            
                            # 存储区域数据
                            self.region_data[region_id].append({
                                "x_center": x_center,
                                "y_center": y_center,
                                "x_norm": x_norm,
                                "y_norm": y_norm,
                                "width": width,
                                "height": height,
                                "card_name": card_name,
                                "twin_id": twin_id,
                                "file": json_file.name,
                                "sequence": seq_dir.name
                            })
                            
                            total_cards += 1
                            
                except Exception as e:
                    print(f"    ❌ 处理文件{json_file.name}时出错: {e}")
        
        print(f"✅ 收集完成，总计{total_cards}张卡牌，{len(self.region_data)}个区域")
        return total_cards
    
    def analyze_region_boundaries(self):
        """分析各区域的边界"""
        print("\n📏 分析区域边界...")
        
        region_stats = {}
        
        for region_id, cards in self.region_data.items():
            if not cards:
                continue
                
            x_norms = [card["x_norm"] for card in cards]
            y_norms = [card["y_norm"] for card in cards]
            
            stats = {
                "count": len(cards),
                "x_min": min(x_norms),
                "x_max": max(x_norms),
                "x_mean": np.mean(x_norms),
                "x_std": np.std(x_norms),
                "y_min": min(y_norms),
                "y_max": max(y_norms),
                "y_mean": np.mean(y_norms),
                "y_std": np.std(y_norms)
            }
            
            region_stats[region_id] = stats
            
            print(f"  区域{region_id}: {stats['count']}张卡牌")
            print(f"    X范围: {stats['x_min']:.3f} - {stats['x_max']:.3f} (均值: {stats['x_mean']:.3f})")
            print(f"    Y范围: {stats['y_min']:.3f} - {stats['y_max']:.3f} (均值: {stats['y_mean']:.3f})")
        
        return region_stats
    
    def identify_region_overlaps(self, region_stats):
        """识别区域重叠"""
        print("\n🔍 识别区域重叠...")
        
        overlaps = []
        region_ids = list(region_stats.keys())
        
        for i, region1 in enumerate(region_ids):
            for region2 in region_ids[i+1:]:
                stats1 = region_stats[region1]
                stats2 = region_stats[region2]
                
                # 检查X轴重叠
                x_overlap = not (stats1["x_max"] < stats2["x_min"] or stats2["x_max"] < stats1["x_min"])
                # 检查Y轴重叠
                y_overlap = not (stats1["y_max"] < stats2["y_min"] or stats2["y_max"] < stats1["y_min"])
                
                if x_overlap and y_overlap:
                    # 计算重叠程度
                    x_overlap_size = min(stats1["x_max"], stats2["x_max"]) - max(stats1["x_min"], stats2["x_min"])
                    y_overlap_size = min(stats1["y_max"], stats2["y_max"]) - max(stats1["y_min"], stats2["y_min"])
                    
                    overlaps.append({
                        "region1": region1,
                        "region2": region2,
                        "x_overlap": x_overlap_size,
                        "y_overlap": y_overlap_size,
                        "overlap_area": x_overlap_size * y_overlap_size
                    })
        
        # 按重叠面积排序
        overlaps.sort(key=lambda x: x["overlap_area"], reverse=True)
        
        print("主要区域重叠:")
        for overlap in overlaps[:5]:
            print(f"  区域{overlap['region1']} ↔ 区域{overlap['region2']}: "
                  f"重叠面积 {overlap['overlap_area']:.4f}")
        
        return overlaps
    
    def analyze_error_patterns(self):
        """分析错误模式"""
        print("\n🎯 分析错误模式...")
        
        # 从之前的验证报告中读取错误数据
        report_path = Path("tests/region_validation_report_20250717_073607.json")
        if not report_path.exists():
            print("  ⚠️ 未找到验证报告，跳过错误模式分析")
            return {}
        
        try:
            with open(report_path, 'r', encoding='utf-8') as f:
                report_data = json.load(f)
            
            # 分析错误模式
            all_errors = []
            for seq_result in report_data["sequence_results"]:
                all_errors.extend(seq_result["region_errors"])
            
            error_patterns = Counter((error["true_region"], error["inferred_region"]) for error in all_errors)
            # 转换为可序列化的格式
            error_patterns_dict = {f"{k[0]}->{k[1]}": v for k, v in error_patterns.items()}
            
            print("最常见的错误模式:")
            for (true_region, inferred_region), count in error_patterns.most_common(10):
                print(f"  {true_region} -> {inferred_region}: {count}次")
            
            # 分析错误位置分布
            error_positions = defaultdict(list)
            for error in all_errors:
                true_region = error["true_region"]
                bbox = error["bbox"]
                x_center = (bbox[0] + bbox[2]) / 2
                y_center = (bbox[1] + bbox[3]) / 2
                
                error_positions[true_region].append({
                    "x_norm": x_center / 640,  # 假设图像宽度640
                    "y_norm": y_center / 320,  # 假设图像高度320
                    "inferred_region": error["inferred_region"]
                })
            
            return {"error_patterns": error_patterns_dict, "error_positions": error_positions}
            
        except Exception as e:
            print(f"  ❌ 分析错误模式时出错: {e}")
            return {}
    
    def generate_region_rules(self, region_stats, overlaps, error_analysis):
        """生成精确的区域划分规则"""
        print("\n🔧 生成区域划分规则...")
        
        rules = {}
        
        for region_id, stats in region_stats.items():
            # 基于统计数据生成规则
            x_center = stats["x_mean"]
            y_center = stats["y_mean"]
            x_range = (stats["x_min"], stats["x_max"])
            y_range = (stats["y_min"], stats["y_max"])
            
            # 考虑标准差来设定边界
            x_boundary = (
                max(0, stats["x_mean"] - 2 * stats["x_std"]),
                min(1, stats["x_mean"] + 2 * stats["x_std"])
            )
            y_boundary = (
                max(0, stats["y_mean"] - 2 * stats["y_std"]),
                min(1, stats["y_mean"] + 2 * stats["y_std"])
            )
            
            rules[region_id] = {
                "x_center": x_center,
                "y_center": y_center,
                "x_range": x_range,
                "y_range": y_range,
                "x_boundary": x_boundary,
                "y_boundary": y_boundary,
                "priority": self._get_region_priority(region_id),
                "description": self._get_region_description(region_id)
            }
        
        # 按优先级排序
        sorted_rules = dict(sorted(rules.items(), key=lambda x: x[1]["priority"]))
        
        print("生成的区域划分规则:")
        for region_id, rule in sorted_rules.items():
            print(f"  区域{region_id} ({rule['description']}):")
            print(f"    X边界: {rule['x_boundary'][0]:.3f} - {rule['x_boundary'][1]:.3f}")
            print(f"    Y边界: {rule['y_boundary'][0]:.3f} - {rule['y_boundary'][1]:.3f}")
            print(f"    优先级: {rule['priority']}")
        
        return sorted_rules
    
    def _get_region_priority(self, region_id):
        """获取区域优先级（数字越小优先级越高）"""
        priority_map = {
            1: 1,   # 手牌区_观战方
            6: 2,   # 吃碰区_观战方
            5: 3,   # 弃牌区_观战方
            16: 4,  # 吃碰区_对战方
            9: 5,   # 对战方区域
            12: 6,  # 其他区域
            11: 7,
            13: 8,
            14: 9,
            15: 10
        }
        return priority_map.get(region_id, 99)
    
    def _get_region_description(self, region_id):
        """获取区域描述"""
        desc_map = {
            1: "手牌区_观战方",
            6: "吃碰区_观战方", 
            5: "弃牌区_观战方",
            16: "吃碰区_对战方",
            9: "对战方区域",
            12: "其他区域12",
            11: "其他区域11",
            13: "其他区域13",
            14: "其他区域14",
            15: "其他区域15"
        }
        return desc_map.get(region_id, f"未知区域{region_id}")
    
    def save_analysis_results(self, region_stats, overlaps, error_analysis, rules):
        """保存分析结果"""
        results = {
            "analysis_timestamp": "2025-01-17 07:40:00",
            "region_statistics": region_stats,
            "region_overlaps": overlaps,
            "error_analysis": error_analysis,
            "region_rules": rules
        }
        
        output_path = "analysis/region_analysis_results.json"
        os.makedirs("analysis", exist_ok=True)
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        
        print(f"\n💾 分析结果已保存: {output_path}")
        return output_path

def main():
    """主分析函数"""
    print("🔍 区域分布深度分析")
    print("=" * 50)
    
    analyzer = RegionDistributionAnalyzer()
    
    # 数据路径
    data_path = Path("legacy_assets/ceshi/zhuangtaiquyu/labels/train")
    if not data_path.exists():
        print("❌ zhuangtaiquyu数据集未找到")
        return False
    
    # 1. 收集区域数据
    total_cards = analyzer.collect_region_data(data_path)
    
    # 2. 分析区域边界
    region_stats = analyzer.analyze_region_boundaries()
    
    # 3. 识别区域重叠
    overlaps = analyzer.identify_region_overlaps(region_stats)
    
    # 4. 分析错误模式
    error_analysis = analyzer.analyze_error_patterns()
    
    # 5. 生成区域规则
    rules = analyzer.generate_region_rules(region_stats, overlaps, error_analysis)
    
    # 6. 保存结果
    analyzer.save_analysis_results(region_stats, overlaps, error_analysis, rules)
    
    print("\n🎉 区域分布分析完成！")
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
