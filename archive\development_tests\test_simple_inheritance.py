"""
简单测试继承机制修复
验证一对一继承是否正确工作
"""

import sys
import logging
sys.path.insert(0, '.')

from src.core.digital_twin_v3 import create_digital_twin_system

# 设置详细日志
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')

def test_simple_inheritance():
    """测试简单的一对一继承"""
    print("🧪 测试简单的一对一继承")
    print("-" * 50)
    
    dt = create_digital_twin_system()
    
    # 第一帧：3张"二"
    frame1 = [
        {'label': '二', 'bbox': [100, 100, 150, 200], 'confidence': 0.9, 'group_id': 1},
        {'label': '二', 'bbox': [200, 100, 250, 200], 'confidence': 0.8, 'group_id': 1},
        {'label': '二', 'bbox': [300, 100, 350, 200], 'confidence': 0.9, 'group_id': 1},
    ]
    
    result1 = dt.process_frame(frame1, frame_id=1)
    cards1 = result1['digital_twin_cards']
    
    print("第一帧ID分配:")
    physical_cards1 = [c for c in cards1 if not c.is_virtual]
    for card in physical_cards1:
        print(f"  {card.twin_id} - {card.label}")
    
    ids1 = [c.twin_id for c in physical_cards1]
    print(f"第一帧ID列表: {ids1}")
    print(f"第一帧ID唯一性: {'✅ 正确' if len(set(ids1)) == len(ids1) else '❌ 重复'}")
    
    # 第二帧：相同的3张"二"
    frame2 = [
        {'label': '二', 'bbox': [105, 105, 155, 205], 'confidence': 0.9, 'group_id': 1},
        {'label': '二', 'bbox': [205, 105, 255, 205], 'confidence': 0.8, 'group_id': 1},
        {'label': '二', 'bbox': [305, 105, 355, 205], 'confidence': 0.9, 'group_id': 1},
    ]
    
    result2 = dt.process_frame(frame2, frame_id=2)
    cards2 = result2['digital_twin_cards']
    
    print("\n第二帧ID继承:")
    physical_cards2 = [c for c in cards2 if not c.is_virtual and c.confidence > 0.5]
    for card in physical_cards2:
        print(f"  {card.twin_id} - {card.label}")
    
    ids2 = [c.twin_id for c in physical_cards2]
    print(f"第二帧ID列表: {ids2}")
    print(f"第二帧ID唯一性: {'✅ 正确' if len(set(ids2)) == len(ids2) else '❌ 重复'}")
    
    # 检查是否所有ID都是1_二（错误情况）
    if all(id == '1_二' for id in ids2):
        print("❌ 错误：所有卡牌都继承了同一个ID (1_二)")
        return False
    else:
        print("✅ 正确：卡牌继承了不同的ID")
        return True

if __name__ == "__main__":
    success = test_simple_inheritance()
    print(f"\n🎯 测试结果: {'✅ 成功' if success else '❌ 失败'}")
