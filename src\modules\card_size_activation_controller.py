"""
模块7：卡牌尺寸启动控制器 (CardSizeActivationController)
只做一件事：基于卡牌尺寸判断是否启动数字孪生功能
"""

from typing import List, Dict, Any, Optional
from dataclasses import dataclass
from enum import Enum
import logging
import json
import statistics
from pathlib import Path

logger = logging.getLogger(__name__)

class GameSessionState(Enum):
    """游戏会话状态"""
    NOT_STARTED = "not_started"        # 未开始
    INITIALIZING = "initializing"      # 初始化中（开局检测）
    ACTIVE = "active"                  # 激活状态（游戏进行中）
    ENDING = "ending"                  # 结束中（小结算画面）
    ENDED = "ended"                    # 已结束

@dataclass
class ActivationDecision:
    """启动决策结果"""
    should_activate: bool           # 是否应该启动数字孪生
    qualified_ratio: float          # 尺寸合格率
    card_count: int                # 观战方手牌数量
    reason: str                    # 决策原因
    preserve_data: bool            # 是否保留原始数据

    # 详细信息
    size_analysis: Optional[Dict] = None     # 尺寸分析详情
    baseline_info: Optional[Dict] = None     # 基准信息
    timestamp: Optional[float] = None        # 决策时间戳
    game_session_active: bool = False        # 🔧 新增：游戏会话是否激活

@dataclass
class SizeBaseline:
    """尺寸基准数据"""
    width_median: float
    height_median: float
    area_median: float
    width_std: float
    height_std: float
    sample_count: int
    confidence_level: float

@dataclass
class CardSizeActivationConfig:
    """卡牌尺寸启动控制配置"""
    
    # 尺寸检测配置
    size_threshold: float = 0.85           # 尺寸阈值
    qualified_ratio_threshold: float = 0.9 # 合格卡牌比例阈值
    min_card_count: int = 10               # 最少卡牌数（降低阈值以支持区域流转场景）
    
    # 基准提取配置
    baseline_source: str = "existing_json" # 基准来源
    baseline_cache_enabled: bool = True    # 缓存基准数据
    baseline_cache_file: str = "card_size_baseline.json"
    
    # 数据保留配置
    preserve_original_data: bool = True    # 保留原始数据
    preserve_metadata: bool = True         # 保留元数据
    
    # 调试配置
    enable_size_logging: bool = True       # 启用尺寸日志
    save_activation_decisions: bool = True # 保存启动决策

class CardSizeActivationController:
    """卡牌尺寸启动控制器 - 只负责判断是否启动数字孪生功能"""
    
    def __init__(self, config: Optional[CardSizeActivationConfig] = None):
        self.config = config or CardSizeActivationConfig()

        # 🔧 新增：游戏会话状态跟踪
        self.game_session_state = GameSessionState.NOT_STARTED
        self.session_start_time = None
        self.last_activation_time = None
        self.consecutive_low_card_frames = 0  # 连续低卡牌数帧计数
        self.settlement_keywords = {'小结算', '结算', '胡牌', '流局', '游戏结束', '下一局'}  # 小结算画面关键词

        # 有效的卡牌标签（复用DataValidator的逻辑）
        self.valid_card_labels = {
            "一", "二", "三", "四", "五", "六", "七", "八", "九", "十",
            "壹", "贰", "叁", "肆", "伍", "陆", "柒", "捌", "玖", "拾",
            "暗"
        }

        # UI元素关键词（复用state_builder的逻辑）
        self.ui_keywords = {'打鸟选择', '碰', '吃', '胡', '过', '已准备', '选择', '提示'}

        # 初始化尺寸基准
        self.size_baseline = self._load_or_extract_baseline()

        # 统计信息
        self.activation_stats = {
            "total_decisions": 0,
            "activated_count": 0,
            "deactivated_count": 0,
            "average_qualified_ratio": 0.0,
            "average_card_count": 0.0,
            "session_activations": 0,  # 🔧 新增：会话激活次数
            "session_duration": 0.0    # 🔧 新增：会话持续时间
        }

        logger.info("卡牌尺寸启动控制器初始化完成")
    
    def should_activate_digital_twin(self, detections: List[Dict[str, Any]]) -> ActivationDecision:
        """判断是否应该启动数字孪生功能 - 🔧 增强版：支持游戏会话持续激活"""
        import time
        start_time = time.time()

        logger.info(f"开始分析{len(detections)}个检测结果的启动条件")

        # 🔧 1. 检查游戏会话状态
        self._update_game_session_state(detections)

        # 🔧 2. 如果游戏会话已激活，直接返回激活决策（不受卡牌数量限制）
        if self.game_session_state == GameSessionState.ACTIVE:
            spectator_cards = self._filter_spectator_hand_cards(detections)
            decision = ActivationDecision(
                should_activate=True,
                qualified_ratio=1.0,  # 会话激活状态下假设合格率为100%
                card_count=len(spectator_cards),
                reason=f"游戏会话已激活，持续数字孪生处理（当前{len(spectator_cards)}张卡牌）",
                preserve_data=True,
                timestamp=start_time,
                game_session_active=True
            )
            self._update_stats(decision)
            logger.info(f"启动决策：{decision.reason}")
            return decision

        # 3. 过滤观战方手牌区的有效卡牌
        spectator_cards = self._filter_spectator_hand_cards(detections)

        # 4. 检查基础条件：卡牌数量（仅在初始化阶段检查）
        if len(spectator_cards) < self.config.min_card_count:
            # 🔧 增加连续低卡牌数帧计数
            self.consecutive_low_card_frames += 1

            decision = ActivationDecision(
                should_activate=False,
                qualified_ratio=0.0,
                card_count=len(spectator_cards),
                reason=f"观战方手牌数量不足{self.config.min_card_count}张，当前{len(spectator_cards)}张",
                preserve_data=True,
                timestamp=start_time,
                game_session_active=False
            )
            self._update_stats(decision)
            logger.info(f"启动决策：{decision.reason}")
            return decision

        # 🔧 重置连续低卡牌数帧计数
        self.consecutive_low_card_frames = 0

        # 5. 计算尺寸合格率
        size_analysis = self._analyze_card_sizes(spectator_cards)
        qualified_ratio = size_analysis["qualified_ratio"]

        # 6. 做出启动决策
        should_activate = qualified_ratio >= self.config.qualified_ratio_threshold

        # 🔧 如果满足启动条件，激活游戏会话
        if should_activate and self.game_session_state == GameSessionState.INITIALIZING:
            self._activate_game_session()

        # 🔧 临时修复：如果卡牌数量达到一定阈值，强制激活游戏会话（用于测试）
        elif (len(spectator_cards) >= 10 and
              self.game_session_state in [GameSessionState.NOT_STARTED, GameSessionState.INITIALIZING]):
            logger.info(f"🔧 临时强制激活：卡牌数量{len(spectator_cards)}张，强制启动数字孪生处理")
            self._activate_game_session()
            should_activate = True

        decision = ActivationDecision(
            should_activate=should_activate,
            qualified_ratio=qualified_ratio,
            card_count=len(spectator_cards),
            reason=f"尺寸合格率{qualified_ratio:.1%}{'≥' if should_activate else '<'}{self.config.qualified_ratio_threshold:.1%}",
            preserve_data=True,
            size_analysis=size_analysis,
            baseline_info=self._get_baseline_info(),
            timestamp=start_time,
            game_session_active=(self.game_session_state == GameSessionState.ACTIVE)
        )

        self._update_stats(decision)

        if self.config.enable_size_logging:
            logger.info(f"启动决策：{decision.reason}")
            logger.debug(f"尺寸分析详情：{size_analysis}")

        return decision

    def _update_game_session_state(self, detections: List[Dict[str, Any]]):
        """🔧 新增：更新游戏会话状态"""
        import time

        # 检查是否有小结算画面关键词
        has_settlement_ui = self._detect_settlement_screen(detections)

        if has_settlement_ui:
            if self.game_session_state == GameSessionState.ACTIVE:
                logger.info("检测到小结算画面，游戏会话结束")
                self.game_session_state = GameSessionState.ENDED
                if self.session_start_time:
                    session_duration = time.time() - self.session_start_time
                    self.activation_stats["session_duration"] += session_duration
                    logger.info(f"游戏会话持续时间：{session_duration:.1f}秒")
            return

        # 状态转换逻辑
        if self.game_session_state == GameSessionState.NOT_STARTED:
            self.game_session_state = GameSessionState.INITIALIZING
            logger.info("游戏会话进入初始化状态")
        elif self.game_session_state == GameSessionState.ENDED:
            # 结束后重新开始
            self.game_session_state = GameSessionState.INITIALIZING
            self.consecutive_low_card_frames = 0
            logger.info("游戏会话重新初始化")

    def _activate_game_session(self):
        """🔧 新增：激活游戏会话"""
        import time

        self.game_session_state = GameSessionState.ACTIVE
        self.session_start_time = time.time()
        self.last_activation_time = time.time()
        self.activation_stats["session_activations"] += 1

        logger.info("🎮 游戏会话已激活，数字孪生系统将持续运行直到小结算画面")

    def _detect_settlement_screen(self, detections: List[Dict[str, Any]]) -> bool:
        """🔧 新增：检测小结算画面"""
        for detection in detections:
            label = detection.get('label', '')
            if any(keyword in label for keyword in self.settlement_keywords):
                return True

        # 额外检查：如果连续多帧卡牌数量极少，可能是结算画面
        spectator_cards = self._filter_spectator_hand_cards(detections)
        if len(spectator_cards) < 5:  # 卡牌数量极少
            self.consecutive_low_card_frames += 1
            if self.consecutive_low_card_frames > 10:  # 连续10帧卡牌极少
                logger.info("连续多帧卡牌数量极少，可能进入结算画面")
                return True
        else:
            self.consecutive_low_card_frames = 0

        return False

    def _filter_spectator_hand_cards(self, detections: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """过滤观战方手牌区的有效卡牌"""
        spectator_cards = []
        
        for detection in detections:
            # 1. 必须是观战方手牌区 (group_id=1)
            if detection.get('group_id') != 1:
                continue
            
            # 2. 必须是有效卡牌标签
            label = detection.get('label', '')
            if label not in self.valid_card_labels:
                continue
            
            # 3. 排除UI元素
            if any(keyword in label for keyword in self.ui_keywords):
                continue
            
            # 4. 必须有有效的边界框
            bbox = detection.get('bbox', [])
            if len(bbox) != 4 or any(x <= 0 for x in bbox[2:]):  # 宽高必须大于0
                continue
            
            spectator_cards.append(detection)
        
        logger.debug(f"从{len(detections)}个检测中过滤出{len(spectator_cards)}张观战方手牌")
        return spectator_cards
    
    def _analyze_card_sizes(self, cards: List[Dict[str, Any]]) -> Dict[str, Any]:
        """分析卡牌尺寸"""
        if not cards:
            return {
                "qualified_count": 0,
                "total_count": 0,
                "qualified_ratio": 0.0,
                "size_ratios": [],
                "average_size_ratio": 0.0
            }
        
        size_ratios = []
        qualified_count = 0

        for card in cards:
            size_ratio = self._calculate_size_ratio(card)
            size_ratios.append(size_ratio)

            if size_ratio >= self.config.size_threshold:
                qualified_count += 1

        qualified_ratio = qualified_count / len(cards)
        average_size_ratio = statistics.mean(size_ratios) if size_ratios else 0.0
        
        return {
            "qualified_count": qualified_count,
            "total_count": len(cards),
            "qualified_ratio": qualified_ratio,
            "size_ratios": size_ratios,
            "average_size_ratio": average_size_ratio,
            "min_size_ratio": min(size_ratios) if size_ratios else 0.0,
            "max_size_ratio": max(size_ratios) if size_ratios else 0.0
        }
    
    def _calculate_size_ratio(self, card: Dict[str, Any]) -> float:
        """计算卡牌与基准的尺寸比例"""
        bbox = card.get('bbox', [0, 0, 0, 0])
        if len(bbox) != 4:
            return 0.0
        
        x1, y1, x2, y2 = bbox
        width = abs(x2 - x1)
        height = abs(y2 - y1)
        area = width * height
        
        if area <= 0 or not self.size_baseline:
            return 0.0
        
        # 计算与基准的比例（使用面积作为主要指标）
        area_ratio = area / self.size_baseline.area_median
        
        # 限制比例在合理范围内 [0.0, 2.0]
        area_ratio = max(0.0, min(2.0, area_ratio))
        
        return area_ratio
    
    def _load_or_extract_baseline(self) -> Optional[SizeBaseline]:
        """加载或提取尺寸基准"""
        # 尝试从缓存加载
        if self.config.baseline_cache_enabled:
            cached_baseline = self._load_cached_baseline()
            if cached_baseline:
                logger.info("从缓存加载尺寸基准")
                return cached_baseline
        
        # 从现有JSON文件提取基准
        extracted_baseline = self._extract_baseline_from_json()
        
        # 保存到缓存
        if extracted_baseline and self.config.baseline_cache_enabled:
            self._save_baseline_to_cache(extracted_baseline)
        
        return extracted_baseline
    
    def _extract_baseline_from_json(self) -> Optional[SizeBaseline]:
        """从现有JSON文件中提取尺寸基准"""
        try:
            # 查找现有的JSON文件
            json_files = self._find_existing_json_files()
            if not json_files:
                logger.warning("未找到现有JSON文件，使用默认基准")
                return self._create_default_baseline()
            
            spectator_cards_data = []
            
            # 从JSON文件中提取观战方手牌数据
            for json_file in json_files[:10]:  # 限制文件数量避免过长处理
                try:
                    with open(json_file, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                    
                    shapes = data.get('shapes', [])
                    for shape in shapes:
                        if (shape.get('group_id') == 1 and 
                            shape.get('label') in self.valid_card_labels):
                            spectator_cards_data.append(shape)
                            
                except Exception as e:
                    logger.debug(f"跳过文件{json_file}: {e}")
                    continue
            
            if len(spectator_cards_data) < 10:
                logger.warning(f"提取的样本数量过少({len(spectator_cards_data)})，使用默认基准")
                return self._create_default_baseline()
            
            # 计算尺寸统计
            widths, heights, areas = [], [], []
            for card_data in spectator_cards_data:
                points = card_data.get('points', [])
                if len(points) == 4:
                    x_coords = [p[0] for p in points]
                    y_coords = [p[1] for p in points]
                    width = max(x_coords) - min(x_coords)
                    height = max(y_coords) - min(y_coords)
                    area = width * height
                    
                    if width > 0 and height > 0:
                        widths.append(width)
                        heights.append(height)
                        areas.append(area)
            
            if len(areas) < 10:
                logger.warning("有效样本数量不足，使用默认基准")
                return self._create_default_baseline()
            
            # 使用中位数作为基准（抗异常值）
            baseline = SizeBaseline(
                width_median=float(statistics.median(widths)),
                height_median=float(statistics.median(heights)),
                area_median=float(statistics.median(areas)),
                width_std=float(statistics.stdev(widths)) if len(widths) > 1 else 0.0,
                height_std=float(statistics.stdev(heights)) if len(heights) > 1 else 0.0,
                sample_count=len(areas),
                confidence_level=0.95
            )
            
            logger.info(f"从{len(json_files)}个JSON文件中提取尺寸基准，样本数量：{baseline.sample_count}")
            logger.info(f"基准面积：{baseline.area_median:.1f}，宽度：{baseline.width_median:.1f}，高度：{baseline.height_median:.1f}")
            
            return baseline
            
        except Exception as e:
            logger.error(f"提取尺寸基准失败: {e}")
            return self._create_default_baseline()
    
    def _find_existing_json_files(self) -> List[Path]:
        """查找现有的JSON标注文件"""
        json_files = []
        
        # 搜索可能的JSON文件位置
        search_paths = [
            Path("legacy_assets/ceshi/calibration_gt"),
            Path("output"),
            Path("data"),
            Path("tests")
        ]
        
        for search_path in search_paths:
            if search_path.exists():
                json_files.extend(search_path.rglob("*.json"))
        
        # 过滤出包含shapes的标注文件
        valid_json_files = []
        for json_file in json_files[:20]:  # 限制检查数量
            try:
                with open(json_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                if 'shapes' in data and len(data['shapes']) > 0:
                    valid_json_files.append(json_file)
            except:
                continue
        
        logger.debug(f"找到{len(valid_json_files)}个有效的JSON标注文件")
        return valid_json_files
    
    def _create_default_baseline(self) -> SizeBaseline:
        """创建默认尺寸基准"""
        # 基于经验的默认值（640x320分辨率下的典型卡牌尺寸）
        return SizeBaseline(
            width_median=45.0,
            height_median=60.0,
            area_median=2700.0,
            width_std=5.0,
            height_std=8.0,
            sample_count=0,
            confidence_level=0.5
        )
    
    def _load_cached_baseline(self) -> Optional[SizeBaseline]:
        """从缓存加载基准"""
        try:
            cache_file = Path(self.config.baseline_cache_file)
            if cache_file.exists():
                with open(cache_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                return SizeBaseline(**data)
        except Exception as e:
            logger.debug(f"加载缓存基准失败: {e}")
        return None
    
    def _save_baseline_to_cache(self, baseline: SizeBaseline):
        """保存基准到缓存"""
        try:
            cache_file = Path(self.config.baseline_cache_file)
            with open(cache_file, 'w', encoding='utf-8') as f:
                json.dump(baseline.__dict__, f, indent=2, ensure_ascii=False)
            logger.debug(f"基准已保存到缓存: {cache_file}")
        except Exception as e:
            logger.debug(f"保存基准到缓存失败: {e}")
    
    def _get_baseline_info(self) -> Dict[str, Any]:
        """获取基准信息"""
        if not self.size_baseline:
            return {"status": "no_baseline"}
        
        return {
            "area_median": self.size_baseline.area_median,
            "width_median": self.size_baseline.width_median,
            "height_median": self.size_baseline.height_median,
            "sample_count": self.size_baseline.sample_count,
            "confidence_level": self.size_baseline.confidence_level
        }
    
    def _update_stats(self, decision: ActivationDecision):
        """更新统计信息"""
        self.activation_stats["total_decisions"] += 1
        
        if decision.should_activate:
            self.activation_stats["activated_count"] += 1
        else:
            self.activation_stats["deactivated_count"] += 1
        
        # 更新平均值
        total = self.activation_stats["total_decisions"]
        self.activation_stats["average_qualified_ratio"] = (
            (self.activation_stats["average_qualified_ratio"] * (total - 1) + decision.qualified_ratio) / total
        )
        self.activation_stats["average_card_count"] = (
            (self.activation_stats["average_card_count"] * (total - 1) + decision.card_count) / total
        )
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        return {
            "activation_stats": self.activation_stats.copy(),
            "baseline_info": self._get_baseline_info(),
            "game_session": {  # 🔧 新增：游戏会话信息
                "state": self.game_session_state.value,
                "session_start_time": self.session_start_time,
                "last_activation_time": self.last_activation_time,
                "consecutive_low_card_frames": self.consecutive_low_card_frames
            },
            "config": {
                "size_threshold": self.config.size_threshold,
                "qualified_ratio_threshold": self.config.qualified_ratio_threshold,
                "min_card_count": self.config.min_card_count
            }
        }

    def get_game_session_state(self) -> GameSessionState:
        """🔧 新增：获取当前游戏会话状态"""
        return self.game_session_state

    def is_session_active(self) -> bool:
        """🔧 新增：检查游戏会话是否激活"""
        return self.game_session_state == GameSessionState.ACTIVE

    def reset_session(self):
        """🔧 新增：重置游戏会话状态"""
        self.game_session_state = GameSessionState.NOT_STARTED
        self.session_start_time = None
        self.last_activation_time = None
        self.consecutive_low_card_frames = 0
        logger.info("游戏会话状态已重置")

def create_card_size_activation_controller(config: Optional[CardSizeActivationConfig] = None):
    """创建卡牌尺寸启动控制器"""
    return CardSizeActivationController(config)
