"""
手动验证双轨机制
基于zhuangtaiquyu数据格式，手动创建测试数据验证双轨输出
"""

import sys
import os
import json
from pathlib import Path
from datetime import datetime
from typing import Dict, Any, List

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def create_zhuangtaiquyu_style_test_data():
    """创建zhuangtaiquyu风格的测试数据"""
    # 基于真实zhuangtaiquyu标注创建测试数据
    test_detections = [
        {
            'label': '壹',
            'bbox': [114.54, 268.05, 159.0, 319.0],
            'confidence': 0.95,
            'group_id': 1,
            'region_name': '手牌_观战方',
            'owner': 'spectator'
        },
        {
            'label': '贰',
            'bbox': [159.94, 268.33, 204.4, 319.29],
            'confidence': 0.92,
            'group_id': 1,
            'region_name': '手牌_观战方',
            'owner': 'spectator'
        },
        {
            'label': '二',
            'bbox': [205.34, 267.76, 249.8, 318.71],
            'confidence': 0.88,
            'group_id': 1,
            'region_name': '手牌_观战方',
            'owner': 'spectator'
        },
        {
            'label': '三',
            'bbox': [252.47, 266.61, 296.93, 317.56],
            'confidence': 0.90,
            'group_id': 1,
            'region_name': '手牌_观战方',
            'owner': 'spectator'
        },
        {
            'label': '捌',
            'bbox': [261.09, 97.36, 276.9, 112.73],
            'confidence': 0.85,
            'group_id': 6,
            'region_name': '吃碰牌_观战方',
            'owner': 'spectator'
        },
        {
            'label': '玖',
            'bbox': [74.31, 101.38, 90.69, 122.07],
            'confidence': 0.87,
            'group_id': 9,
            'region_name': '弃牌区_对手方',
            'owner': 'opponent'
        }
    ]
    
    # 对应的zhuangtaiquyu原始标注格式
    original_zhuangtaiquyu = {
        "version": "2.4.3",
        "flags": {},
        "shapes": [
            {
                "label": "1壹",
                "points": [[114.54, 268.05], [159.0, 268.05], [159.0, 319.0], [114.54, 319.0]],
                "group_id": 1,
                "shape_type": "rectangle"
            },
            {
                "label": "1贰",
                "points": [[159.94, 268.33], [204.4, 268.33], [204.4, 319.29], [159.94, 319.29]],
                "group_id": 1,
                "shape_type": "rectangle"
            },
            {
                "label": "1二",
                "points": [[205.34, 267.76], [249.8, 267.76], [249.8, 318.71], [205.34, 318.71]],
                "group_id": 1,
                "shape_type": "rectangle"
            },
            {
                "label": "2三",
                "points": [[252.47, 266.61], [296.93, 266.61], [296.93, 317.56], [252.47, 317.56]],
                "group_id": 1,
                "shape_type": "rectangle"
            },
            {
                "label": "1捌",
                "points": [[261.09, 97.36], [276.9, 97.36], [276.9, 112.73], [261.09, 112.73]],
                "group_id": 6,
                "shape_type": "rectangle"
            },
            {
                "label": "2玖",
                "points": [[74.31, 101.38], [90.69, 101.38], [90.69, 122.07], [74.31, 122.07]],
                "group_id": 9,
                "shape_type": "rectangle"
            }
        ],
        "imagePath": "test_frame.jpg",
        "imageHeight": 320,
        "imageWidth": 640
    }
    
    return test_detections, original_zhuangtaiquyu

def test_dual_format_manual():
    """手动验证双轨格式"""
    print("🚀 手动验证双轨机制")
    print("=" * 60)
    
    try:
        # 导入模块
        from src.core.digital_twin_v2 import create_digital_twin_system, CardDetection
        from src.core.synchronized_dual_format_validator import SynchronizedDualFormatValidator
        print("✅ 模块导入成功")
        
        # 创建系统
        dt_system = create_digital_twin_system()
        validator = SynchronizedDualFormatValidator(strict_mode=True)
        print("✅ 系统创建成功")
        
        # 创建测试数据
        test_detections, original_zhuangtaiquyu = create_zhuangtaiquyu_style_test_data()
        print(f"✅ 创建测试数据: {len(test_detections)} 个检测结果")
        
        # 转换为CardDetection对象
        card_detections = []
        for detection in test_detections:
            card_detection = CardDetection(
                label=detection['label'],
                bbox=detection['bbox'],
                confidence=detection['confidence'],
                group_id=detection['group_id'],
                region_name=detection['region_name'],
                owner=detection['owner']
            )
            card_detections.append(card_detection)
        
        print(f"✅ 转换为 {len(card_detections)} 个CardDetection对象")
        
        # 数字孪生系统处理
        print("🧠 数字孪生系统处理...")
        dt_result = dt_system.process_frame(card_detections)
        digital_twin_cards = dt_result['digital_twin_cards']
        print(f"✅ 数字孪生处理完成: {len(digital_twin_cards)} 张卡牌")
        
        # 显示数字孪生卡牌详情
        print("\n📋 数字孪生卡牌详情:")
        for i, card in enumerate(digital_twin_cards):
            print(f"   {i+1}. twin_id='{card.twin_id}', label='{card.label}', region='{card.region_name}', group_id={card.group_id}")
        
        # 生成同步双轨输出
        print("\n🔄 生成同步双轨输出...")
        dual_result = dt_system.export_synchronized_dual_format(
            dt_result, 640, 320, "test_frame.jpg"
        )
        print("✅ 同步双轨输出生成成功")
        
        # 验证双轨一致性
        print("\n🔍 验证双轨一致性...")
        consistency_validation = validator.validate_comprehensive(
            dual_result['rlcard_format'],
            dual_result['anylabeling_format'],
            digital_twin_cards
        )
        
        print(f"📊 双轨一致性分数: {consistency_validation['overall_consistency_score']:.3f}")
        print(f"📊 双轨是否一致: {'✅' if consistency_validation['is_consistent'] else '❌'}")
        
        if consistency_validation['issues']:
            print(f"⚠️ 一致性问题:")
            for issue in consistency_validation['issues']:
                print(f"   - {issue}")
        
        # 检查RLCard格式
        rlcard_format = dual_result['rlcard_format']
        print(f"\n📊 RLCard格式分析:")
        print(f"   手牌: {len(rlcard_format.get('hand', []))} 张")
        print(f"   弃牌区: {len(rlcard_format.get('discard_pile', []))} 张")
        print(f"   对手弃牌区: {len(rlcard_format.get('opponent_discard_pile', []))} 张")
        print(f"   吃碰牌: {len(rlcard_format.get('combo_cards', []))} 张")
        print(f"   对手吃碰牌: {len(rlcard_format.get('opponent_combo_cards', []))} 张")
        
        # 显示RLCard格式的数字孪生元数据
        rlcard_metadata = rlcard_format.get('digital_twin_metadata', {})
        print(f"   数字孪生元数据: 总卡牌={rlcard_metadata.get('total_cards', 0)}, 虚拟卡牌={rlcard_metadata.get('virtual_cards', 0)}")
        
        # 检查AnyLabeling格式
        anylabeling_format = dual_result['anylabeling_format']
        anylabeling_shapes = anylabeling_format.get('shapes', [])
        print(f"\n📊 AnyLabeling格式分析:")
        print(f"   标注总数: {len(anylabeling_shapes)}")
        
        # 显示生成的AnyLabeling标注详情
        print("   生成的标注详情:")
        for i, shape in enumerate(anylabeling_shapes):
            twin_id = shape.get('attributes', {}).get('digital_twin_id', '')
            label = shape.get('label', '')
            group_id = shape.get('group_id', 0)
            print(f"     {i+1}. label='{label}', twin_id='{twin_id}', group_id={group_id}")
        
        # 与原始zhuangtaiquyu标注对比
        print(f"\n🔍 与原始zhuangtaiquyu标注对比:")
        original_shapes = original_zhuangtaiquyu.get('shapes', [])
        print(f"   原始标注数: {len(original_shapes)}")
        print(f"   生成标注数: {len(anylabeling_shapes)}")
        print(f"   数字孪生卡牌数: {len(digital_twin_cards)}")
        
        # 详细对比分析
        print("\n📋 详细对比分析:")
        
        # 1. 标签对比
        original_labels = [shape.get('label', '') for shape in original_shapes]
        generated_labels = []
        for shape in anylabeling_shapes:
            twin_id = shape.get('attributes', {}).get('digital_twin_id', '')
            # 转换数字孪生ID为zhuangtaiquyu格式
            if '_' in twin_id and not twin_id.startswith('虚拟'):
                parts = twin_id.split('_')
                if len(parts) >= 2:
                    generated_labels.append(f"{parts[0]}{parts[1]}")
            else:
                generated_labels.append(twin_id)
        
        print(f"   原始标签: {original_labels}")
        print(f"   生成标签: {generated_labels}")
        
        # 2. 区域分配对比
        original_groups = [shape.get('group_id', 0) for shape in original_shapes]
        generated_groups = [shape.get('group_id', 0) for shape in anylabeling_shapes]
        print(f"   原始区域ID: {original_groups}")
        print(f"   生成区域ID: {generated_groups}")
        
        # 3. 计算匹配度
        if len(original_labels) == len(generated_labels):
            label_matches = sum(1 for o, g in zip(original_labels, generated_labels) if o == g)
            group_matches = sum(1 for o, g in zip(original_groups, generated_groups) if o == g)
            
            label_accuracy = label_matches / len(original_labels) if original_labels else 0
            group_accuracy = group_matches / len(original_groups) if original_groups else 0
            
            print(f"   标签匹配率: {label_accuracy:.3f} ({label_matches}/{len(original_labels)})")
            print(f"   区域匹配率: {group_accuracy:.3f} ({group_matches}/{len(original_groups)})")
        else:
            print(f"   ⚠️ 数量不匹配，无法计算准确率")
        
        # 保存测试结果
        output_dir = project_root / "tests" / "dual_format_manual_verification_output"
        output_dir.mkdir(exist_ok=True)
        
        # 保存生成的双轨输出
        with open(output_dir / "generated_rlcard.json", 'w', encoding='utf-8') as f:
            json.dump(rlcard_format, f, ensure_ascii=False, indent=2)
        
        with open(output_dir / "generated_anylabeling.json", 'w', encoding='utf-8') as f:
            json.dump(anylabeling_format, f, ensure_ascii=False, indent=2)
        
        # 保存原始zhuangtaiquyu标注
        with open(output_dir / "original_zhuangtaiquyu.json", 'w', encoding='utf-8') as f:
            json.dump(original_zhuangtaiquyu, f, ensure_ascii=False, indent=2)
        
        # 保存一致性验证结果
        with open(output_dir / "consistency_validation.json", 'w', encoding='utf-8') as f:
            json.dump(consistency_validation, f, ensure_ascii=False, indent=2)
        
        # 保存测试数据
        with open(output_dir / "test_detections.json", 'w', encoding='utf-8') as f:
            json.dump(test_detections, f, ensure_ascii=False, indent=2)
        
        print(f"\n💾 测试结果已保存到: {output_dir}")
        
        # 评估测试结果
        success_criteria = [
            consistency_validation['is_consistent'],  # 双轨一致性
            len(rlcard_format.get('hand', [])) > 0,   # RLCard有手牌
            len(anylabeling_shapes) > 0,              # AnyLabeling有标注
            len(anylabeling_shapes) == len(digital_twin_cards)  # 数量一致
        ]
        
        success = all(success_criteria)
        
        print(f"\n🎯 测试结果评估:")
        print(f"   双轨一致性: {'✅' if consistency_validation['is_consistent'] else '❌'}")
        print(f"   RLCard有效: {'✅' if len(rlcard_format.get('hand', [])) > 0 else '❌'}")
        print(f"   AnyLabeling有效: {'✅' if len(anylabeling_shapes) > 0 else '❌'}")
        print(f"   数量一致: {'✅' if len(anylabeling_shapes) == len(digital_twin_cards) else '❌'}")
        print(f"   总体成功: {'✅' if success else '❌'}")
        
        # 特别验证：zhuangtaiquyu兼容性
        print(f"\n🎯 zhuangtaiquyu兼容性验证:")
        
        # 检查生成的标签是否符合zhuangtaiquyu格式
        zhuangtaiquyu_compatible = True
        for shape in anylabeling_shapes:
            label = shape.get('label', '')
            if not (label and (label[0].isdigit() or label.startswith('虚拟'))):
                zhuangtaiquyu_compatible = False
                break
        
        print(f"   标签格式兼容: {'✅' if zhuangtaiquyu_compatible else '❌'}")
        print(f"   包含数字孪生ID: {'✅' if all(shape.get('attributes', {}).get('digital_twin_id') for shape in anylabeling_shapes) else '❌'}")
        print(f"   区域分配正确: {'✅' if all(shape.get('group_id', 0) > 0 for shape in anylabeling_shapes) else '❌'}")
        
        overall_success = success and zhuangtaiquyu_compatible
        
        return overall_success, {
            'dual_format_consistent': consistency_validation['is_consistent'],
            'consistency_score': consistency_validation['overall_consistency_score'],
            'zhuangtaiquyu_compatible': zhuangtaiquyu_compatible,
            'card_counts': {
                'original': len(original_shapes),
                'generated': len(anylabeling_shapes),
                'digital_twin': len(digital_twin_cards)
            }
        }
        
    except Exception as e:
        print(f"❌ 测试过程中发生异常: {str(e)}")
        import traceback
        traceback.print_exc()
        return False, {'error': str(e)}

def main():
    """主函数"""
    print("🚀 开始手动验证双轨机制")
    print("=" * 60)
    
    success, results = test_dual_format_manual()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 双轨机制手动验证成功！")
        print("   - 双轨输出功能正常")
        print("   - 一致性验证通过")
        print("   - zhuangtaiquyu格式兼容")
        print("   - 数字孪生ID正确分配")
        print("   - 区域分配准确")
        print(f"   - 一致性分数: {results.get('consistency_score', 0):.3f}")
    else:
        print("⚠️ 双轨机制验证失败")
        print("   - 请检查错误信息")
        print("   - 修复问题后重新测试")
        if 'error' in results:
            print(f"   - 错误: {results['error']}")
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
