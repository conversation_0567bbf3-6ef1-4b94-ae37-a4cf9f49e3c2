#!/usr/bin/env python3
"""
测试暗牌ID格式修复
验证暗牌处理器和继承器不再生成包含区域信息的错误格式
"""

import sys
sys.path.append('src')

def test_dark_card_processor():
    """测试暗牌处理器的ID格式"""
    print("🧪 测试暗牌处理器...")
    
    from modules.dark_card_processor import DarkCardProcessor
    
    processor = DarkCardProcessor()
    
    # 模拟吃碰区的卡牌数据
    test_cards = [
        # 明牌
        {'label': '拾', 'group_id': 6, 'twin_id': '1拾', 'bbox': [100, 100, 150, 150]},
        # 暗牌
        {'label': '暗', 'group_id': 6, 'twin_id': '临时暗_123456', 'bbox': [150, 100, 200, 150]},
        {'label': '暗', 'group_id': 6, 'twin_id': '临时暗_123457', 'bbox': [200, 100, 250, 150]},
    ]
    
    result = processor.process_dark_cards(test_cards)
    
    print(f"✅ 处理了{len(result.processed_cards)}张卡牌:")
    for card in result.processed_cards:
        twin_id = card.get('twin_id', 'None')
        label = card.get('label', 'None')
        group_id = card.get('group_id', 'None')
        is_dark = card.get('is_dark', False)
        
        # 验证暗牌ID格式
        if is_dark and '暗' in twin_id:
            # 检查是否包含区域信息（错误格式）
            if any(char.isdigit() for char in twin_id.split('暗')[-1]):
                print(f"  ❌ {twin_id} (标签:{label}, 区域:{group_id}) - 包含区域信息！")
            else:
                print(f"  ✅ {twin_id} (标签:{label}, 区域:{group_id}) - 格式正确")
        else:
            print(f"  ✅ {twin_id} (标签:{label}, 区域:{group_id})")

def test_inheritor_id_fix():
    """测试继承器的ID格式修复功能"""
    print("\n🧪 测试继承器ID格式修复...")
    
    from modules.simple_inheritor import SimpleInheritor
    
    inheritor = SimpleInheritor()
    
    # 测试各种错误格式的暗牌ID
    test_cases = [
        ("1拾暗16", "1拾暗"),
        ("2三暗6", "2三暗"),
        ("4五暗14", "4五暗"),
        ("1二暗", "1二暗"),  # 已经正确的格式
        ("3七暗15", "3七暗"),
    ]
    
    print("  暗牌ID格式修复测试:")
    for input_id, expected_id in test_cases:
        fixed_id = inheritor._fix_id_format(input_id)
        if fixed_id == expected_id:
            print(f"    ✅ {input_id} → {fixed_id}")
        else:
            print(f"    ❌ {input_id} → {fixed_id} (期望: {expected_id})")

def test_region_transitioner():
    """测试区域流转器的ID格式"""
    print("\n🧪 测试区域流转器...")
    
    from modules.region_transitioner import RegionTransitioner
    
    transitioner = RegionTransitioner()
    
    # 测试基础ID提取（暗牌）
    test_cases = [
        ("1拾暗", "1拾"),
        ("2三暗", "2三"),
        ("4五暗", "4五"),
        ("虚拟暗", None),  # 虚拟牌不提取基础ID
    ]
    
    print("  暗牌基础ID提取测试:")
    for input_id, expected_base_id in test_cases:
        base_id = transitioner._extract_base_id(input_id)
        if base_id == expected_base_id:
            print(f"    ✅ {input_id} → {base_id}")
        else:
            print(f"    ❌ {input_id} → {base_id} (期望: {expected_base_id})")

if __name__ == "__main__":
    print("🎯 暗牌ID格式修复测试")
    print("=" * 50)
    
    try:
        test_dark_card_processor()
        test_inheritor_id_fix()
        test_region_transitioner()
        
        print("\n🎉 所有测试完成!")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
