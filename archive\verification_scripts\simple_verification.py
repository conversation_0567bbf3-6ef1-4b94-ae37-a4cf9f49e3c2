"""
简单的双轨验证测试
"""

import sys
import json
from pathlib import Path

# 添加项目路径
sys.path.insert(0, '.')

def run_verification():
    print("🚀 启动双轨机制验证")
    
    try:
        # 导入模块
        from src.core.digital_twin_v2 import create_digital_twin_system, CardDetection
        print("✅ 模块导入成功")
        
        # 创建系统
        dt_system = create_digital_twin_system()
        print("✅ 数字孪生系统创建成功")
        
        # 检查方法
        if hasattr(dt_system, 'export_synchronized_dual_format'):
            print("✅ 双轨输出方法存在")
        else:
            print("❌ 双轨输出方法不存在")
            return
        
        # 创建测试数据
        detection = CardDetection("二", [100, 100, 150, 150], 0.95, 1, "手牌_观战方", "spectator")
        print("✅ 测试数据创建成功")
        
        # 处理
        result = dt_system.process_frame([detection])
        print(f"✅ 数字孪生处理成功: {len(result['digital_twin_cards'])} 张卡牌")
        
        # 双轨输出
        dual_result = dt_system.export_synchronized_dual_format(result, 640, 320, "test.jpg")
        print("✅ 双轨输出成功")
        
        # 检查一致性
        consistency = dual_result['consistency_validation']
        print(f"📊 一致性分数: {consistency['consistency_score']:.3f}")
        print(f"📊 是否一致: {'✅' if consistency['is_consistent'] else '❌'}")
        
        # 保存结果
        with open("verification_result.json", 'w', encoding='utf-8') as f:
            json.dump({
                'success': True,
                'consistency_score': consistency['consistency_score'],
                'is_consistent': consistency['is_consistent'],
                'rlcard_cards': sum(len(cards) for cards in [
                    dual_result['rlcard_format'].get('hand', []),
                    dual_result['rlcard_format'].get('discard_pile', []),
                    dual_result['rlcard_format'].get('combo_cards', [])
                ]),
                'anylabeling_shapes': len(dual_result['anylabeling_format'].get('shapes', []))
            }, f, indent=2)
        
        print("💾 结果已保存到 verification_result.json")
        print("🎉 验证完成！")
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    run_verification()
