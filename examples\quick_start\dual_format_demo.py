#!/usr/bin/env python3
"""
双格式输出演示 - 快速开始示例

这个示例展示了跑胡子AI系统的核心特色功能：同步双轨输出。
演示如何同时生成RLCard格式(AI决策用)和AnyLabeling格式(人工审核用)。

运行方法：
    python examples/quick_start/dual_format_demo.py
"""

import sys
import os
import json
import tempfile
from datetime import datetime

# 添加项目根目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(current_dir))
sys.path.insert(0, project_root)

def main():
    """主演示函数"""
    print("🔄 跑胡子AI系统 - 双格式输出演示")
    print("=" * 50)
    
    try:
        # 步骤1：系统初始化
        print("1. 系统初始化...")
        from src.core.digital_twin_v2 import create_digital_twin_system, CardDetection
        
        dt_system = create_digital_twin_system()
        print("   ✅ 数字孪生系统创建成功")
        
        # 验证双轨输出功能
        if not hasattr(dt_system, 'export_synchronized_dual_format'):
            print("   ❌ 双轨输出功能不可用")
            return False
        print("   ✅ 双轨输出功能可用")
        
        # 步骤2：准备测试场景
        print("\n2. 准备测试场景...")
        
        # 场景1：手牌区域
        hand_cards = [
            CardDetection("二", [50, 50, 100, 100], 0.95, 1, "手牌_观战方", "spectator"),
            CardDetection("三", [110, 50, 160, 100], 0.92, 2, "手牌_观战方", "spectator"),
            CardDetection("四", [170, 50, 220, 100], 0.89, 3, "手牌_观战方", "spectator"),
        ]
        
        # 场景2：出牌区域
        public_cards = [
            CardDetection("五", [250, 150, 300, 200], 0.88, 4, "出牌区_观战方", "spectator"),
            CardDetection("六", [310, 150, 360, 200], 0.85, 5, "出牌区_观战方", "spectator"),
        ]
        
        # 合并所有卡牌
        all_detections = hand_cards + public_cards
        
        print(f"   ✅ 测试场景准备完成")
        print(f"      • 手牌区域: {len(hand_cards)} 张卡牌")
        print(f"      • 出牌区域: {len(public_cards)} 张卡牌")
        print(f"      • 总计: {len(all_detections)} 张卡牌")
        
        # 步骤3：数字孪生处理
        print("\n3. 数字孪生处理...")
        result = dt_system.process_frame(all_detections)
        
        digital_twin_cards = result.get('digital_twin_cards', [])
        print(f"   ✅ 数字孪生处理完成: {len(digital_twin_cards)} 张卡牌")
        
        # 显示数字孪生ID分配
        print("   📋 数字孪生ID分配:")
        for card in digital_twin_cards:
            print(f"      • {card.card_value} -> {card.twin_id} ({card.region_name})")
        
        # 步骤4：双轨输出生成
        print("\n4. 双轨输出生成...")
        
        # 设置图像参数
        image_width, image_height = 640, 480
        image_path = "demo_dual_format.jpg"
        
        # 生成双轨输出
        dual_result = dt_system.export_synchronized_dual_format(
            result, image_width, image_height, image_path
        )
        
        print("   ✅ 双轨输出生成完成")
        
        # 步骤5：RLCard格式分析
        print("\n5. RLCard格式分析 (AI决策用)")
        print("   " + "-" * 30)
        
        rlcard_format = dual_result['rlcard_format']
        
        # 分析手牌
        hand_data = rlcard_format.get('hand', [])
        print(f"   🎮 手牌数据: {len(hand_data)} 张")
        for i, card_data in enumerate(hand_data, 1):
            card_value, suit, twin_id, confidence = card_data
            print(f"      {i}. {card_value} (ID: {twin_id}, 置信度: {confidence:.2f})")
        
        # 分析公共牌
        public_data = rlcard_format.get('public', [])
        print(f"   🌐 公共牌数据: {len(public_data)} 张")
        for i, card_data in enumerate(public_data, 1):
            card_value, suit, twin_id, confidence = card_data
            print(f"      {i}. {card_value} (ID: {twin_id}, 置信度: {confidence:.2f})")
        
        # 分析元数据
        metadata = rlcard_format.get('metadata', {})
        if metadata:
            print(f"   📊 元数据:")
            dt_info = metadata.get('digital_twin_info', {})
            print(f"      • 总卡牌数: {dt_info.get('total_cards', 0)}")
            print(f"      • 虚拟卡牌数: {dt_info.get('virtual_cards', 0)}")
            print(f"      • 共识分数: {dt_info.get('consensus_score', 0):.3f}")
        
        # 步骤6：AnyLabeling格式分析
        print("\n6. AnyLabeling格式分析 (人工审核用)")
        print("   " + "-" * 35)
        
        anylabeling_format = dual_result['anylabeling_format']
        
        # 基本信息
        print(f"   🏷️ 图像信息:")
        print(f"      • 尺寸: {anylabeling_format.get('imageWidth', 0)}x{anylabeling_format.get('imageHeight', 0)}")
        print(f"      • 路径: {anylabeling_format.get('imagePath', 'N/A')}")
        print(f"      • 版本: {anylabeling_format.get('version', 'N/A')}")
        
        # 标注信息
        shapes = anylabeling_format.get('shapes', [])
        print(f"   📐 标注数据: {len(shapes)} 个标注")
        for i, shape in enumerate(shapes, 1):
            label = shape.get('label', 'unknown')
            points_count = len(shape.get('points', []))
            shape_type = shape.get('shape_type', 'unknown')
            print(f"      {i}. {label} ({shape_type}, {points_count} 个点)")
        
        # 步骤7：一致性验证
        print("\n7. 一致性验证")
        print("   " + "-" * 20)
        
        consistency = dual_result['consistency_validation']
        
        # 基本一致性指标
        consistency_score = consistency.get('consistency_score', 0)
        is_consistent = consistency.get('is_consistent', False)
        
        print(f"   🔍 一致性分数: {consistency_score:.3f}")
        print(f"   ✅ 一致性状态: {'通过' if is_consistent else '失败'}")
        
        # 详细验证结果
        detailed = consistency.get('detailed_validations', {})
        if detailed:
            print(f"   📋 详细验证:")
            for key, value in detailed.items():
                status = "✅" if value else "❌"
                print(f"      • {key}: {status} {value}")
        
        # 步骤8：文件输出演示
        print("\n8. 文件输出演示")
        print("   " + "-" * 20)
        
        # 使用临时目录
        with tempfile.TemporaryDirectory() as temp_dir:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            
            # 保存RLCard格式
            rlcard_file = os.path.join(temp_dir, f"demo_rlcard_{timestamp}.json")
            with open(rlcard_file, 'w', encoding='utf-8') as f:
                json.dump(rlcard_format, f, ensure_ascii=False, indent=2)
            
            # 保存AnyLabeling格式
            anylabeling_file = os.path.join(temp_dir, f"demo_anylabeling_{timestamp}.json")
            with open(anylabeling_file, 'w', encoding='utf-8') as f:
                json.dump(anylabeling_format, f, ensure_ascii=False, indent=2)
            
            # 保存一致性验证
            validation_file = os.path.join(temp_dir, f"demo_validation_{timestamp}.json")
            with open(validation_file, 'w', encoding='utf-8') as f:
                json.dump(consistency, f, ensure_ascii=False, indent=2)
            
            print(f"   ✅ 文件保存成功 (临时目录)")
            print(f"      • RLCard: {os.path.basename(rlcard_file)}")
            print(f"      • AnyLabeling: {os.path.basename(anylabeling_file)}")
            print(f"      • 验证报告: {os.path.basename(validation_file)}")
            
            # 验证文件内容
            print(f"   🔍 文件验证:")
            
            # 检查RLCard文件
            with open(rlcard_file, 'r', encoding='utf-8') as f:
                loaded_rlcard = json.load(f)
                print(f"      • RLCard文件: {len(json.dumps(loaded_rlcard))} 字符")
            
            # 检查AnyLabeling文件
            with open(anylabeling_file, 'r', encoding='utf-8') as f:
                loaded_anylabeling = json.load(f)
                print(f"      • AnyLabeling文件: {len(json.dumps(loaded_anylabeling))} 字符")
        
        # 步骤9：演示总结
        print("\n9. 演示总结")
        print("   " + "=" * 30)
        
        # 数据统计
        total_input = len(all_detections)
        total_twin = len(digital_twin_cards)
        total_rlcard = len(hand_data) + len(public_data)
        total_anylabeling = len(shapes)
        
        print(f"   📊 数据流转统计:")
        print(f"      输入检测 → 数字孪生 → RLCard → AnyLabeling")
        print(f"      {total_input:^8} → {total_twin:^8} → {total_rlcard:^7} → {total_anylabeling:^11}")
        
        print(f"   🎯 质量指标:")
        print(f"      • 一致性分数: {consistency_score:.3f}")
        print(f"      • 数据完整性: {'✅ 完整' if total_anylabeling > 0 else '❌ 不完整'}")
        print(f"      • 格式兼容性: {'✅ 兼容' if is_consistent else '❌ 不兼容'}")
        
        # 成功判断
        success = (
            total_twin > 0 and
            total_rlcard > 0 and
            total_anylabeling > 0 and
            consistency_score >= 0.8 and
            is_consistent
        )
        
        if success:
            print("\n🎉 双格式输出演示完全成功！")
            print("   ✅ RLCard格式生成正常")
            print("   ✅ AnyLabeling格式生成正常")
            print("   ✅ 一致性验证通过")
            print("   ✅ 文件输出功能正常")
        else:
            print("\n⚠️ 演示存在问题，需要检查")
            if total_twin == 0:
                print("   ❌ 数字孪生处理失败")
            if not is_consistent:
                print("   ❌ 一致性验证失败")
            if consistency_score < 0.8:
                print("   ❌ 一致性分数过低")
        
        # 应用场景说明
        print("\n💡 应用场景说明:")
        print("   🎮 RLCard格式用途:")
        print("      • AI决策算法输入")
        print("      • 游戏状态分析")
        print("      • 策略计算")
        
        print("   🏷️ AnyLabeling格式用途:")
        print("      • 人工审核和标注")
        print("      • 可视化调试")
        print("      • 训练数据生成")
        
        print("   🔄 双轨同步优势:")
        print("      • 100%数据一致性")
        print("      • AI+人工双重保障")
        print("      • 无信息丢失")
        
        return success
        
    except Exception as e:
        print(f"\n❌ 演示过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    success = main()
    print(f"\n结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"演示结果: {'✅ 成功' if success else '❌ 失败'}")
    
    sys.exit(0 if success else 1)
