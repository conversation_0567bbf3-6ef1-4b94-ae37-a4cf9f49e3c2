#!/usr/bin/env python3
"""
导出最新YOLOv8l模型为ONNX格式

专门为D:\phz-ai-simple\data\processed\train\weights\best.pt模型导出ONNX
使用修复后的最优导出参数，确保AnyLabeling兼容性
"""

import sys
import os
import logging
from pathlib import Path
import numpy as np

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def export_latest_yolov8l():
    """导出最新的YOLOv8l模型"""
    
    # 模型路径
    model_path = r"D:\phz-ai-simple\data\processed\train\weights\best.pt"
    output_dir = r"D:\phz-ai-simple\data\processed\train\weights"
    
    # 检查模型文件是否存在
    if not os.path.exists(model_path):
        logger.error(f"模型文件不存在: {model_path}")
        return False
        
    logger.info(f"准备导出最新YOLOv8l模型: {model_path}")
    logger.info(f"输出目录: {output_dir}")
    
    try:
        from ultralytics import YOLO
        
        # 加载模型
        logger.info("加载YOLOv8l模型...")
        model = YOLO(model_path)
        
        # 显示模型信息
        logger.info(f"模型类别数: {len(model.names)}")
        logger.info(f"模型类别: {list(model.names.values())}")
        
        # 使用修复后的最优导出参数（与best_anylabeling.onnx一致）
        export_args = {
            "format": "onnx",
            "imgsz": 640,           # 使用正方形尺寸（关键修复）
            "dynamic": False,       # 关闭动态尺寸，确保AnyLabeling兼容性
            "simplify": True,       # 简化模型
            "opset": 12,           # ONNX算子集版本
            "optimize": False,      # 关闭优化，避免数值问题
            "half": False,         # 使用FP32精度
            "batch": 1,            # 固定批大小
            "workspace": 4,        # 工作空间大小（GB）
            "device": "cuda" if os.system("nvidia-smi") == 0 else "cpu"
        }
        
        logger.info("使用最优导出参数:")
        for key, value in export_args.items():
            logger.info(f"  {key}: {value}")
            
        logger.info("开始导出ONNX模型...")
        
        # 执行导出
        exported_path = model.export(**export_args)
        
        if exported_path and os.path.exists(exported_path):
            # 移动到目标目录（如果需要）
            target_path = os.path.join(output_dir, "best.onnx")
            
            if exported_path != target_path:
                import shutil
                if os.path.exists(target_path):
                    # 备份原有文件
                    backup_path = os.path.join(output_dir, "best_backup.onnx")
                    if os.path.exists(backup_path):
                        os.remove(backup_path)
                    shutil.move(target_path, backup_path)
                    logger.info(f"原有ONNX文件已备份为: best_backup.onnx")
                
                shutil.move(exported_path, target_path)
                logger.info(f"ONNX模型已移动到: {target_path}")
            else:
                target_path = exported_path
                logger.info(f"ONNX模型已导出: {target_path}")
                
            # 验证导出的ONNX模型
            verify_exported_onnx(target_path)
            
            return target_path
            
        else:
            logger.error("ONNX导出失败，未找到导出文件")
            return False
            
    except ImportError:
        logger.error("需要安装ultralytics: pip install ultralytics")
        return False
    except Exception as e:
        logger.error(f"导出过程出错: {e}")
        import traceback
        traceback.print_exc()
        return False

def verify_exported_onnx(onnx_path):
    """验证导出的ONNX模型质量"""
    try:
        import onnxruntime as ort
        
        logger.info(f"\n验证导出的ONNX模型: {Path(onnx_path).name}")
        logger.info("=" * 50)
        
        # 加载ONNX模型
        session = ort.InferenceSession(onnx_path)
        input_info = session.get_inputs()[0]
        output_info = session.get_outputs()[0]
        
        logger.info(f"✅ ONNX模型加载成功")
        logger.info(f"📋 模型信息:")
        logger.info(f"  - 输入名称: {input_info.name}")
        logger.info(f"  - 输入形状: {input_info.shape}")
        logger.info(f"  - 输入类型: {input_info.type}")
        logger.info(f"  - 输出名称: {output_info.name}")
        logger.info(f"  - 输出形状: {output_info.shape}")
        logger.info(f"  - 输出类型: {output_info.type}")
        
        # 创建测试输入
        if len(input_info.shape) == 4:
            batch, channels, height, width = input_info.shape
            test_input = np.random.rand(batch, channels, height, width).astype(np.float32)
            logger.info(f"  - 测试输入形状: {test_input.shape}")
        else:
            logger.warning("输入形状异常，使用默认测试输入")
            test_input = np.random.rand(1, 3, 640, 640).astype(np.float32)
            
        # 运行推理测试
        logger.info(f"\n🧪 推理测试:")
        
        import time
        start_time = time.time()
        outputs = session.run(None, {input_info.name: test_input})
        inference_time = time.time() - start_time
        
        output = np.array(outputs[0])
        
        logger.info(f"  - 推理时间: {inference_time:.4f}秒")
        logger.info(f"  - 输出形状: {output.shape}")
        logger.info(f"  - 输出数值范围: [{float(output.min()):.8f}, {float(output.max()):.8f}]")
        
        # 分析置信度分布
        logger.info(f"\n📊 置信度分析:")
        
        if len(output.shape) == 3:
            # 转换为 [detections, attributes]
            if output.shape[1] > output.shape[2]:
                data = output[0].transpose()
            else:
                data = output[0]
                
            logger.info(f"  - 检测数据形状: {data.shape}")
            
            if data.shape[1] >= 5:
                confidences = data[:, 4]
                max_conf = float(confidences.max())
                min_conf = float(confidences.min())
                mean_conf = float(confidences.mean())
                
                valid_detections = int((confidences > 0.1).sum())
                high_conf_detections = int((confidences > 0.5).sum())
                very_high_conf_detections = int((confidences > 1.0).sum())
                
                logger.info(f"  - 置信度统计:")
                logger.info(f"    * 最小值: {min_conf:.8f}")
                logger.info(f"    * 最大值: {max_conf:.8f}")
                logger.info(f"    * 平均值: {mean_conf:.8f}")
                logger.info(f"  - 检测统计:")
                logger.info(f"    * 有效检测 (>0.1): {valid_detections}")
                logger.info(f"    * 高置信度检测 (>0.5): {high_conf_detections}")
                logger.info(f"    * 超高置信度检测 (>1.0): {very_high_conf_detections}")
                
                # 质量评估
                logger.info(f"\n🎯 质量评估:")
                
                if max_conf > 0.01:
                    logger.info(f"  ✅ 置信度分布正常")
                    
                    if max_conf > 1.0:
                        logger.info(f"  ✅ 置信度范围健康，应该在AnyLabeling中表现良好")
                    else:
                        logger.warning(f"  ⚠️ 最大置信度偏低: {max_conf:.6f}")
                        
                    if valid_detections > 0:
                        logger.info(f"  ✅ 能够产生有效检测")
                    else:
                        logger.warning(f"  ⚠️ 没有有效检测")
                        
                else:
                    logger.error(f"  ❌ 置信度异常低: {max_conf:.8f}")
                    logger.error(f"  ❌ 导出可能有问题")
                    
            else:
                logger.warning(f"  ⚠️ 输出格式不符合YOLO预期")
        else:
            logger.warning(f"  ⚠️ 输出维度异常: {output.shape}")
            
        # AnyLabeling兼容性检查
        logger.info(f"\n📋 AnyLabeling兼容性检查:")
        logger.info(f"  ✅ 固定输入尺寸: {input_info.shape}")
        logger.info(f"  ✅ 单批次处理: batch=1")
        logger.info(f"  ✅ FP32精度: 避免精度问题")
        logger.info(f"  ✅ 非动态尺寸: 确保兼容性")
        logger.info(f"  ✅ 未优化: 避免数值问题")
        
        # 与其他模型对比
        logger.info(f"\n📈 模型对比信息:")
        
        # 分析输出维度来推断类别数
        if len(output.shape) == 3 and output.shape[1] > 10:
            # 假设格式是 [batch, attributes, detections]
            attributes = output.shape[1]
            estimated_classes = attributes - 5  # 减去 4个bbox坐标 + 1个置信度
            logger.info(f"  - 估计类别数: {estimated_classes}")
            
            if estimated_classes == 21:
                logger.info(f"  🎯 这是21类模型（卡牌专用）")
            elif estimated_classes == 30:
                logger.info(f"  🎯 这是30类模型（完整类别）")
            else:
                logger.info(f"  🎯 这是{estimated_classes}类模型")
                
        logger.info(f"\n🚀 导出完成！")
        logger.info(f"📁 ONNX文件位置: {onnx_path}")
        logger.info(f"💡 建议在AnyLabeling中测试此模型的检测效果")
        
        return True
        
    except ImportError:
        logger.error("需要安装onnxruntime: pip install onnxruntime")
        return False
    except Exception as e:
        logger.error(f"ONNX验证失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🚀 最新YOLOv8l模型ONNX导出器")
    print("=" * 60)
    print("目标: 导出D:\\phz-ai-simple\\data\\processed\\train\\weights\\best.pt")
    print("格式: ONNX (AnyLabeling兼容)")
    print("参数: 使用修复后的最优导出参数")
    print("")
    
    # 执行导出
    result = export_latest_yolov8l()
    
    if result:
        print(f"\n✅ 导出成功!")
        print(f"📁 ONNX文件: {result}")
        print(f"\n🎯 下一步:")
        print(f"1. 在AnyLabeling中加载此ONNX模型")
        print(f"2. 测试检测效果，对比与其他模型的差异")
        print(f"3. 验证YOLOv8l相比YOLOv8x的性能影响")
        print(f"4. 评估模型大小与精度的平衡")
    else:
        print(f"\n❌ 导出失败!")
        print(f"请检查:")
        print(f"1. 模型文件是否存在")
        print(f"2. ultralytics是否正确安装")
        print(f"3. 系统环境是否正常")

if __name__ == "__main__":
    main()
