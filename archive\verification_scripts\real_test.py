import sys
sys.path.insert(0, '.')
from src.core.digital_twin_v2 import create_digital_twin_system, CardDetection

print("开始真实验证...")

# 创建系统
dt_system = create_digital_twin_system()
print('系统创建成功')

# 检查双轨输出方法
if hasattr(dt_system, 'export_synchronized_dual_format'):
    print('双轨输出方法存在')
    
    # 创建测试数据
    detection = CardDetection('二', [100, 100, 150, 150], 0.95, 1, '手牌_观战方', 'spectator')
    
    # 处理数据
    result = dt_system.process_frame([detection])
    card_count = len(result.get('digital_twin_cards', []))
    print(f'处理结果: {card_count} 张卡牌')
    
    # 双轨输出
    dual_result = dt_system.export_synchronized_dual_format(result, 640, 320, 'test.jpg')
    
    # 检查结果
    consistency = dual_result['consistency_validation']
    score = consistency.get('consistency_score', 0)
    print(f'一致性分数: {score}')
    
    # 检查输出格式
    rlcard_format = dual_result['rlcard_format']
    anylabeling_format = dual_result['anylabeling_format']
    
    hand_count = len(rlcard_format.get('hand', []))
    shapes_count = len(anylabeling_format.get('shapes', []))
    
    print(f'RLCard卡牌数: {hand_count}')
    print(f'AnyLabeling标注数: {shapes_count}')
    print('真实验证完成')
else:
    print('双轨输出方法不存在')
