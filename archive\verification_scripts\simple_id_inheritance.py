"""
极简化数字孪生ID继承系统
只做一件事：ID继承

核心原则：
1. 相同区域+相同标签 = 继承ID
2. 否则分配新ID
3. 不考虑暗牌、不考虑补偿、不考虑复杂规则
"""

from typing import Dict, List, Optional
from dataclasses import dataclass
import logging

logger = logging.getLogger(__name__)

@dataclass
class SimpleCard:
    """简化的卡牌数据结构"""
    label: str          # 牌面标签
    group_id: int       # 区域ID
    bbox: List[float]   # 边界框
    twin_id: Optional[str] = None  # 数字孪生ID

class SimpleIDInheritance:
    """极简化ID继承系统"""
    
    def __init__(self):
        # 前一帧的卡牌：{(区域, 标签): ID}
        self.previous_cards: Dict[tuple, str] = {}
        
        # ID计数器：{标签: 计数}
        self.id_counters: Dict[str, int] = {}
        
        # 当前帧计数
        self.frame_count = 0
        
        logger.info("极简化ID继承系统初始化完成")
    
    def process_frame(self, cards: List[SimpleCard]) -> List[SimpleCard]:
        """处理一帧数据"""
        self.frame_count += 1
        logger.info(f"处理第{self.frame_count}帧，共{len(cards)}张卡牌")
        
        # 当前帧的卡牌映射
        current_cards: Dict[tuple, SimpleCard] = {}
        
        # 第一步：建立当前帧映射
        for card in cards:
            key = (card.group_id, card.label)
            current_cards[key] = card
        
        # 第二步：尝试继承ID
        for key, card in current_cards.items():
            if key in self.previous_cards:
                # 继承前一帧的ID
                card.twin_id = self.previous_cards[key]
                logger.debug(f"继承ID: {card.twin_id} (区域{card.group_id}, 标签{card.label})")
            else:
                # 分配新ID
                label = card.label
                if label not in self.id_counters:
                    self.id_counters[label] = 0
                
                self.id_counters[label] += 1
                card.twin_id = f"{self.id_counters[label]}{label}"
                logger.info(f"分配新ID: {card.twin_id} (区域{card.group_id}, 标签{card.label})")
        
        # 第三步：更新前一帧记录
        self.previous_cards = {
            (card.group_id, card.label): card.twin_id 
            for card in current_cards.values()
        }
        
        return list(current_cards.values())
    
    def get_statistics(self) -> Dict[str, any]:
        """获取统计信息"""
        return {
            "frame_count": self.frame_count,
            "total_unique_cards": sum(self.id_counters.values()),
            "card_types": len(self.id_counters),
            "id_counters": self.id_counters.copy()
        }

def create_simple_system():
    """创建极简化系统"""
    return SimpleIDInheritance()

# 测试代码
if __name__ == "__main__":
    # 创建系统
    system = create_simple_system()
    
    # 第一帧：手牌区有两张二
    frame1_cards = [
        SimpleCard(label="二", group_id=1, bbox=[100, 100, 150, 150]),
        SimpleCard(label="二", group_id=1, bbox=[100, 200, 150, 250]),
    ]
    
    result1 = system.process_frame(frame1_cards)
    print("第一帧结果:")
    for card in result1:
        print(f"  {card.twin_id} (区域{card.group_id}, 标签{card.label})")
    
    # 第二帧：一张二移动到调整区
    frame2_cards = [
        SimpleCard(label="二", group_id=1, bbox=[100, 100, 150, 150]),  # 继承
        SimpleCard(label="二", group_id=2, bbox=[200, 100, 250, 150]),  # 新区域
    ]
    
    result2 = system.process_frame(frame2_cards)
    print("\n第二帧结果:")
    for card in result2:
        print(f"  {card.twin_id} (区域{card.group_id}, 标签{card.label})")
    
    # 统计信息
    stats = system.get_statistics()
    print(f"\n统计信息: {stats}")
