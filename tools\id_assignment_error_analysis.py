#!/usr/bin/env python3
"""
ID分配错误深度分析工具

基于开发过程10的发现和素材介绍，深入分析ID分配的具体错误模式：
1. 系统性偏移问题（+1/-1偏移）
2. 特定卡牌错误（"六"、"四"等）
3. 空间排序精度问题
4. 区域内排序逻辑问题
"""

import json
import os
import sys
from collections import defaultdict, Counter
from typing import Dict, List, Tuple, Any
import logging

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.core.digital_twin_v2 import DigitalTwinCoordinator

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class IDAssignmentErrorAnalyzer:
    """ID分配错误分析器"""
    
    def __init__(self):
        self.system = DigitalTwinCoordinator()
        self.error_patterns = defaultdict(list)
        self.card_specific_errors = defaultdict(list)
        self.spatial_errors = defaultdict(list)
        self.offset_errors = []
        
    def analyze_existing_validation_report(self):
        """分析现有的验证报告数据"""
        logger.info("分析现有的验证报告数据...")

        # 查找最新的验证报告
        report_files = [
            "tests/ground_truth_validation_report_20250717_070024.json",
            "tests/enhanced_system_validation_20250717_075841.json"
        ]

        for report_file in report_files:
            if os.path.exists(report_file):
                logger.info(f"分析报告文件: {report_file}")
                return self._analyze_validation_report(report_file)

        logger.error("未找到验证报告文件")
        return self._generate_error_report()

    def _analyze_validation_report(self, report_file: str) -> Dict:
        """分析验证报告文件"""
        try:
            with open(report_file, 'r', encoding='utf-8') as f:
                report_data = json.load(f)

            logger.info(f"成功加载验证报告: {report_file}")

            # 提取错误样本
            if 'error_samples' in report_data:
                self._analyze_error_samples(report_data['error_samples'])

            # 提取统计信息
            if 'overall_statistics' in report_data:
                stats = report_data['overall_statistics']
                logger.info(f"总体统计: ID准确率 {stats.get('id_accuracy', 0)*100:.1f}%")

            return self._generate_error_report()

        except Exception as e:
            logger.error(f"分析验证报告时出错: {e}")
            return self._generate_error_report()

    def _analyze_error_samples(self, error_samples: List[Dict]):
        """分析错误样本"""
        logger.info(f"分析 {len(error_samples)} 个错误样本...")

        for sample in error_samples:
            expected_id = sample.get('expected_id', '')
            actual_id = sample.get('actual_id', '')
            card_type = sample.get('card_type', '')

            # 分析偏移错误
            expected_num = self._extract_id_number(expected_id)
            actual_num = self._extract_id_number(actual_id)

            if expected_num and actual_num:
                offset = actual_num - expected_num
                if offset != 0:
                    self.offset_errors.append({
                        'offset': offset,
                        'card_type': card_type,
                        'expected': expected_num,
                        'actual': actual_num,
                        'expected_id': expected_id,
                        'actual_id': actual_id
                    })

            # 记录卡牌特定错误
            error_info = {
                'card_type': card_type,
                'expected_id': expected_id,
                'actual_id': actual_id
            }
            self.card_specific_errors[card_type].append(error_info)
        
    def _analyze_single_file(self, json_path: str, region_dir: str) -> Tuple[int, int]:
        """分析单个JSON文件的ID分配错误"""
        try:
            with open(json_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
                
            if 'shapes' not in data:
                return 0, 0
                
            # 提取真实标注（人工标注的{ID}{卡牌名称}格式）
            ground_truth = []
            for shape in data['shapes']:
                if 'label' in shape and 'group_id' in shape:
                    # 解析人工标注的ID和卡牌名称
                    label = shape['label']
                    if '_' in label or any(char.isdigit() for char in label[:2]):
                        # 这是带ID的标注，如"1壹"或"2_二"
                        ground_truth.append({
                            'label': label,
                            'bbox': shape['points'][0] + shape['points'][2] if len(shape['points']) >= 2 else [0,0,0,0],
                            'group_id': shape.get('group_id', 0),
                            'expected_id': self._extract_expected_id(label),
                            'card_type': self._extract_card_type(label)
                        })
                        
            if not ground_truth:
                return 0, 0
                
            # 模拟系统处理
            detections = self._convert_to_detections(ground_truth)
            if not detections:
                return 0, len(ground_truth)
                
            # 获取系统分配的ID
            result = self.system.process_frame(detections, frame_id=1)
            system_assignments = {
                f"{card['label']}_{card['bbox'][0]}_{card['bbox'][1]}": card.get('twin_id', '')
                for card in result.get('cards', [])
            }
            
            # 比较分配结果
            errors = 0
            for gt_card in ground_truth:
                card_key = f"{gt_card['label']}_{gt_card['bbox'][0]}_{gt_card['bbox'][1]}"
                expected_id = gt_card['expected_id']
                actual_id = system_assignments.get(card_key, '')
                
                if expected_id != actual_id:
                    errors += 1
                    self._record_error(gt_card, expected_id, actual_id, region_dir)
                    
            return errors, len(ground_truth)
            
        except Exception as e:
            logger.error(f"分析文件 {json_path} 时出错: {e}")
            return 0, 0
            
    def _extract_expected_id(self, label: str) -> str:
        """从人工标注中提取期望的ID"""
        # 处理"1壹"格式
        if len(label) >= 2 and label[0].isdigit():
            return f"{label[0]}_{label[1:]}"
        # 处理"2_二"格式
        elif '_' in label:
            return label
        else:
            return f"1_{label}"  # 默认ID
            
    def _extract_card_type(self, label: str) -> str:
        """从标注中提取卡牌类型"""
        if '_' in label:
            return label.split('_', 1)[1]
        elif len(label) >= 2 and label[0].isdigit():
            return label[1:]
        else:
            return label
            
    def _convert_to_detections(self, ground_truth: List[Dict]) -> List[Any]:
        """将真实标注转换为检测格式"""
        detections = []
        for gt in ground_truth:
            # 这里需要根据实际的CardDetection类来构造
            detection = {
                'label': gt['card_type'],
                'bbox': gt['bbox'],
                'confidence': 0.9,
                'group_id': gt['group_id'],
                'region_name': f"region_{gt['group_id']}"
            }
            detections.append(detection)
        return detections
        
    def _record_error(self, gt_card: Dict, expected_id: str, actual_id: str, region_dir: str):
        """记录错误信息"""
        error_info = {
            'region': region_dir,
            'card_type': gt_card['card_type'],
            'expected_id': expected_id,
            'actual_id': actual_id,
            'bbox': gt_card['bbox'],
            'group_id': gt_card['group_id']
        }
        
        # 分析错误类型
        if expected_id and actual_id:
            expected_num = self._extract_id_number(expected_id)
            actual_num = self._extract_id_number(actual_id)
            
            if expected_num and actual_num:
                offset = actual_num - expected_num
                if offset != 0:
                    self.offset_errors.append({
                        'offset': offset,
                        'card_type': gt_card['card_type'],
                        'region': region_dir,
                        'expected': expected_num,
                        'actual': actual_num
                    })
                    
        # 记录卡牌特定错误
        self.card_specific_errors[gt_card['card_type']].append(error_info)
        
        # 记录空间错误（同区域内的排序错误）
        self.spatial_errors[region_dir].append(error_info)
        
    def _extract_id_number(self, id_str: str) -> int:
        """从ID字符串中提取数字"""
        try:
            if '_' in id_str:
                return int(id_str.split('_')[0])
            elif id_str.startswith('虚拟_'):
                return 0  # 虚拟ID
            else:
                return 1  # 默认
        except:
            return 0
            
    def _generate_error_report(self) -> Dict:
        """生成错误分析报告"""
        report = {
            'offset_analysis': self._analyze_offset_errors(),
            'card_specific_analysis': self._analyze_card_specific_errors(),
            'spatial_analysis': self._analyze_spatial_errors(),
            'recommendations': self._generate_recommendations()
        }
        
        return report
        
    def _analyze_offset_errors(self) -> Dict:
        """分析偏移错误"""
        if not self.offset_errors:
            return {'message': '未发现偏移错误'}
            
        offset_counter = Counter(error['offset'] for error in self.offset_errors)
        card_offsets = defaultdict(list)
        
        for error in self.offset_errors:
            card_offsets[error['card_type']].append(error['offset'])
            
        return {
            'total_offset_errors': len(self.offset_errors),
            'offset_distribution': dict(offset_counter),
            'most_common_offset': offset_counter.most_common(1)[0] if offset_counter else None,
            'card_specific_offsets': {
                card: Counter(offsets).most_common(3) 
                for card, offsets in card_offsets.items()
            }
        }
        
    def _analyze_card_specific_errors(self) -> Dict:
        """分析特定卡牌错误"""
        card_error_counts = {
            card: len(errors) 
            for card, errors in self.card_specific_errors.items()
        }
        
        # 按错误数量排序
        sorted_cards = sorted(card_error_counts.items(), key=lambda x: x[1], reverse=True)
        
        return {
            'total_card_types_with_errors': len(card_error_counts),
            'top_error_cards': sorted_cards[:10],
            'error_distribution': card_error_counts
        }
        
    def _analyze_spatial_errors(self) -> Dict:
        """分析空间排序错误"""
        region_error_counts = {
            region: len(errors)
            for region, errors in self.spatial_errors.items()
        }
        
        return {
            'total_regions_with_errors': len(region_error_counts),
            'region_error_distribution': region_error_counts,
            'most_problematic_regions': sorted(
                region_error_counts.items(), 
                key=lambda x: x[1], 
                reverse=True
            )[:5]
        }
        
    def _generate_recommendations(self) -> List[str]:
        """生成改进建议"""
        recommendations = []
        
        # 基于偏移错误的建议
        if self.offset_errors:
            offset_counter = Counter(error['offset'] for error in self.offset_errors)
            most_common_offset = offset_counter.most_common(1)[0]
            if most_common_offset[1] > len(self.offset_errors) * 0.3:  # 超过30%
                recommendations.append(
                    f"发现系统性偏移问题：{most_common_offset[0]:+d}偏移占{most_common_offset[1]/len(self.offset_errors)*100:.1f}%，"
                    f"建议检查空间排序算法的起始索引"
                )
                
        # 基于卡牌特定错误的建议
        if self.card_specific_errors:
            top_error_card = max(self.card_specific_errors.items(), key=lambda x: len(x[1]))
            if len(top_error_card[1]) > 5:  # 错误超过5次
                recommendations.append(
                    f"卡牌'{top_error_card[0]}'错误率最高({len(top_error_card[1])}次)，"
                    f"建议针对该卡牌优化识别和排序逻辑"
                )
                
        # 基于空间错误的建议
        if self.spatial_errors:
            top_error_region = max(self.spatial_errors.items(), key=lambda x: len(x[1]))
            if len(top_error_region[1]) > 10:  # 错误超过10次
                recommendations.append(
                    f"区域{top_error_region[0]}空间排序错误最多({len(top_error_region[1])}次)，"
                    f"建议优化该区域的空间排序规则"
                )
                
        if not recommendations:
            recommendations.append("未发现明显的系统性问题，建议进行精细化调优")
            
        return recommendations

def main():
    """主函数"""
    analyzer = IDAssignmentErrorAnalyzer()

    logger.info("开始ID分配错误深度分析...")
    report = analyzer.analyze_existing_validation_report()
    
    # 保存分析报告
    output_path = "analysis/id_assignment_error_analysis_report.json"
    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    
    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump(report, f, ensure_ascii=False, indent=2)
        
    logger.info(f"分析报告已保存到: {output_path}")
    
    # 打印关键发现
    print("\n🔍 ID分配错误分析关键发现:")
    print("=" * 50)
    
    if 'offset_analysis' in report and 'most_common_offset' in report['offset_analysis']:
        offset_info = report['offset_analysis']['most_common_offset']
        if offset_info:
            print(f"📊 最常见偏移: {offset_info[0]:+d} (出现{offset_info[1]}次)")
            
    if 'card_specific_analysis' in report and 'top_error_cards' in report['card_specific_analysis']:
        top_cards = report['card_specific_analysis']['top_error_cards'][:3]
        print(f"🎯 错误最多的卡牌:")
        for card, count in top_cards:
            print(f"   - {card}: {count}次错误")
            
    if 'spatial_analysis' in report and 'most_problematic_regions' in report['spatial_analysis']:
        top_regions = report['spatial_analysis']['most_problematic_regions'][:3]
        print(f"📍 问题最多的区域:")
        for region, count in top_regions:
            print(f"   - 区域{region}: {count}次错误")
            
    print(f"\n💡 改进建议:")
    for i, rec in enumerate(report.get('recommendations', []), 1):
        print(f"   {i}. {rec}")

if __name__ == "__main__":
    main()
