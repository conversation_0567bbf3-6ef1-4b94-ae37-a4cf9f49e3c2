import os
import sys
from pprint import pprint

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from src.state_builder import StateBuilder

def test_state_builder():
    """测试状态转换模块"""
    print("===== 测试状态转换模块 =====")
    
    # 创建状态转换模块
    state_builder = StateBuilder()
    
    # 模拟YOLO检测结果
    yolo_detections = {
        'cards': [
            {'id': '1_二', 'group_id': 1, 'pos': [100, 100, 50, 70], 'conf': 0.95},
            {'id': '2_三', 'group_id': 1, 'pos': [160, 100, 50, 70], 'conf': 0.93},
            {'id': '3_四', 'group_id': 1, 'pos': [220, 100, 50, 70], 'conf': 0.91},
            {'id': '4_五', 'group_id': 1, 'pos': [280, 100, 50, 70], 'conf': 0.94},
            {'id': '5_六', 'group_id': 1, 'pos': [340, 100, 50, 70], 'conf': 0.92},
            
            {'id': '6_七', 'group_id': 5, 'pos': [100, 200, 50, 70], 'conf': 0.90},
            {'id': '7_八', 'group_id': 5, 'pos': [160, 200, 50, 70], 'conf': 0.89},
            
            {'id': '8_九', 'group_id': 6, 'pos': [100, 300, 50, 70], 'conf': 0.88},
            {'id': '9_九_暗', 'group_id': 6, 'pos': [160, 300, 50, 70], 'conf': 0.87},
            {'id': '10_九_暗', 'group_id': 6, 'pos': [220, 300, 50, 70], 'conf': 0.86},
            
            {'id': '11_十', 'group_id': 9, 'pos': [100, 400, 50, 70], 'conf': 0.85},
            {'id': '12_壹', 'group_id': 9, 'pos': [160, 400, 50, 70], 'conf': 0.84},
            
            {'id': '13_贰', 'group_id': 16, 'pos': [100, 500, 50, 70], 'conf': 0.83},
            {'id': '14_贰', 'group_id': 16, 'pos': [160, 500, 50, 70], 'conf': 0.82},
            {'id': '15_贰', 'group_id': 16, 'pos': [220, 500, 50, 70], 'conf': 0.81},
        ]
    }
    
    # 转换为RLCard状态
    rlcard_state = state_builder.yolo_to_rlcard_state(yolo_detections)
    
    # 打印RLCard状态
    print("RLCard状态:")
    pprint(rlcard_state)
    
    # 转换为显示格式
    display_state = state_builder.rlcard_to_display_format(rlcard_state)
    
    # 打印显示格式
    print("\n显示格式:")
    pprint(display_state)

def test_edge_cases():
    """测试边缘情况"""
    print("\n===== 测试边缘情况 =====")
    
    # 创建状态转换模块
    state_builder = StateBuilder()
    
    # 测试空检测结果
    print("\n测试空检测结果:")
    empty_state = state_builder.yolo_to_rlcard_state({})
    pprint(empty_state)
    
    # 测试没有卡牌的检测结果
    print("\n测试没有卡牌的检测结果:")
    no_cards_state = state_builder.yolo_to_rlcard_state({'cards': []})
    pprint(no_cards_state)
    
    # 测试无效的卡牌ID
    print("\n测试无效的卡牌ID:")
    invalid_id_detections = {
        'cards': [
            {'id': 'invalid_id', 'group_id': 1, 'pos': [100, 100, 50, 70], 'conf': 0.95},
            {'id': '1_未知牌', 'group_id': 1, 'pos': [160, 100, 50, 70], 'conf': 0.93},
        ]
    }
    invalid_id_state = state_builder.yolo_to_rlcard_state(invalid_id_detections)
    pprint(invalid_id_state)

if __name__ == "__main__":
    test_state_builder()
    test_edge_cases() 