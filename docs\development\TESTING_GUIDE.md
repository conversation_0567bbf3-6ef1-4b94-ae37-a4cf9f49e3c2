# 测试开发指南

## 测试层级
1. 单元测试：每个函数的独立测试
2. 集成测试：模块间接口测试
3. 端到端测试：完整流程测试
4. 性能测试：帧率、延迟测试
5. 模型性能测试：YOLOv8l模型专项测试（2025-07-17新增）

## 跑胡子特定测试
- 特殊牌型识别测试
- 暗牌处理测试
- 规则优先级测试
- 边缘情况测试（遮挡、重叠）

## 测试数据要求
- 包含所有卡牌类型
- 包含所有游戏状态
- 包含异常情况

## YOLOv8l模型性能测试（2025-07-17）
### 测试配置
- **模型**: YOLOv8l ONNX (data/processed/train/weights/best.onnx)
- **参数**: conf_threshold=0.25, iou_threshold=0.45
- **测试规模**: 594张图像，大规模验证

### 性能基准
- **精确率**: ≥98% (当前: 98.1%)
- **召回率**: ≥97% (当前: 97.2%)
- **F1分数**: ≥97% (当前: 97.7%)
- **推理速度**: ≥3 FPS (当前: 3.6 FPS)

### 测试工具
```bash
# 运行YOLOv8l性能测试
python tools/test_yolov8l_performance.py

# 验证ONNX导出质量
python tools/export_latest_yolov8l.py
```

### AnyLabeling兼容性验证
- 模型加载测试
- 推理效果验证
- 参数一致性检查
- 长期稳定性测试