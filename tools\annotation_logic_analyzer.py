#!/usr/bin/env python3
"""
人工标注逻辑深度分析器

分析人工标注的真实ID分配逻辑，建立标注逻辑模型
目标：理解人工标注者的思维模式，调整系统算法匹配
"""

import sys
import os
import json
import re
from pathlib import Path
from typing import List, Dict, Tuple, Any
from collections import defaultdict, Counter
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class AnnotationLogicAnalyzer:
    """人工标注逻辑分析器"""
    
    def __init__(self):
        self.samples = []
        self.card_name_mapping = {
            "壹": "一", "贰": "二", "叁": "三", "肆": "四", "伍": "五",
            "陆": "六", "柒": "七", "捌": "八", "玖": "九", "拾": "十"
        }
        
    def parse_annotation_label(self, label: str) -> <PERSON><PERSON>[str, str]:
        """解析人工标注标签"""
        match = re.match(r'^(\d+)(.+)$', label)
        if match:
            twin_id = match.group(1)
            card_name = match.group(2)
            
            # 转换繁体到简体
            if card_name in self.card_name_mapping:
                card_name = self.card_name_mapping[card_name]
            
            return twin_id, card_name
        else:
            return "", label
            
    def load_sample_data(self, json_file: Path) -> Dict:
        """加载单个样本数据"""
        try:
            with open(json_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
                
            sample = {
                'file_name': json_file.name,
                'file_path': str(json_file),
                'cards_by_region': defaultdict(list),
                'cards_by_type': defaultdict(list),
                'total_cards': 0
            }
            
            for shape in data.get('shapes', []):
                if len(shape.get('points', [])) >= 4:
                    points = shape['points']
                    x1, y1 = points[0]
                    x2, y2 = points[2]
                    
                    label = shape.get('label', '')
                    twin_id, card_name = self.parse_annotation_label(label)
                    
                    # 只处理有效的卡牌标注
                    if twin_id and card_name and card_name not in ['吃', '过', '胡', '碰']:
                        card_info = {
                            'twin_id': twin_id,
                            'card_name': card_name,
                            'bbox': [x1, y1, x2, y2],
                            'x_center': (x1 + x2) / 2,
                            'y_center': (y1 + y2) / 2,
                            'group_id': shape.get('group_id', 1),
                            'full_label': label
                        }
                        
                        region_id = shape.get('group_id', 1)
                        sample['cards_by_region'][region_id].append(card_info)
                        sample['cards_by_type'][card_name].append(card_info)
                        sample['total_cards'] += 1
                        
            return sample
            
        except Exception as e:
            logger.error(f"加载文件 {json_file} 失败: {e}")
            return {}
            
    def analyze_spatial_patterns(self, sample: Dict) -> Dict:
        """分析空间排列模式"""
        patterns = {
            'region_patterns': {},
            'type_patterns': {},
            'spatial_consistency': {}
        }
        
        # 分析每个区域的空间模式
        for region_id, cards in sample['cards_by_region'].items():
            if len(cards) > 1:
                # 按ID排序
                cards_by_id = sorted(cards, key=lambda c: int(c['twin_id']))
                
                # 分析空间顺序
                spatial_order = self._analyze_spatial_order(cards_by_id)
                patterns['region_patterns'][region_id] = spatial_order
                
        # 分析每种卡牌类型的ID分配模式
        for card_type, cards in sample['cards_by_type'].items():
            if len(cards) > 1:
                # 按ID排序
                cards_by_id = sorted(cards, key=lambda c: int(c['twin_id']))
                
                # 分析ID连续性
                id_sequence = [int(c['twin_id']) for c in cards_by_id]
                patterns['type_patterns'][card_type] = {
                    'id_sequence': id_sequence,
                    'is_continuous': self._is_continuous_sequence(id_sequence),
                    'spatial_order': self._analyze_spatial_order(cards_by_id)
                }
                
        return patterns
        
    def _analyze_spatial_order(self, cards: List[Dict]) -> Dict:
        """分析卡牌的空间排列顺序"""
        if len(cards) < 2:
            return {'pattern': 'single_card'}
            
        # 计算相邻卡牌的空间关系
        relationships = []
        for i in range(len(cards) - 1):
            curr_card = cards[i]
            next_card = cards[i + 1]
            
            dx = next_card['x_center'] - curr_card['x_center']
            dy = next_card['y_center'] - curr_card['y_center']
            
            # 判断主要方向
            if abs(dx) > abs(dy):
                direction = 'right' if dx > 0 else 'left'
            else:
                direction = 'down' if dy > 0 else 'up'
                
            relationships.append({
                'from_id': curr_card['twin_id'],
                'to_id': next_card['twin_id'],
                'direction': direction,
                'dx': dx,
                'dy': dy
            })
            
        # 分析主要模式
        directions = [r['direction'] for r in relationships]
        direction_counter = Counter(directions)
        
        return {
            'relationships': relationships,
            'main_direction': direction_counter.most_common(1)[0][0] if direction_counter else 'unknown',
            'direction_distribution': dict(direction_counter),
            'is_consistent': len(set(directions)) == 1
        }
        
    def _is_continuous_sequence(self, sequence: List[int]) -> bool:
        """检查ID序列是否连续"""
        if len(sequence) < 2:
            return True
            
        for i in range(len(sequence) - 1):
            if sequence[i + 1] - sequence[i] != 1:
                return False
        return True
        
    def collect_samples(self, max_samples: int = 100) -> List[Dict]:
        """收集分析样本"""
        logger.info(f"收集 {max_samples} 个分析样本...")
        
        zhuangtaiquyu_path = Path("legacy_assets/ceshi/zhuangtaiquyu/labels/train")
        
        if not zhuangtaiquyu_path.exists():
            logger.error("zhuangtaiquyu数据集未找到")
            return []
            
        # 收集所有JSON文件
        json_files = []
        for region_dir in zhuangtaiquyu_path.iterdir():
            if region_dir.is_dir():
                for json_file in region_dir.glob("*.json"):
                    json_files.append(json_file)
                    
        # 选择有代表性的样本
        selected_files = json_files[:max_samples]
        logger.info(f"选择了 {len(selected_files)} 个文件进行分析")
        
        samples = []
        for json_file in selected_files:
            sample = self.load_sample_data(json_file)
            if sample and sample['total_cards'] > 0:
                samples.append(sample)
                
        logger.info(f"成功加载 {len(samples)} 个有效样本")
        return samples
        
    def analyze_annotation_logic(self, samples: List[Dict]) -> Dict:
        """分析人工标注逻辑"""
        logger.info("开始分析人工标注逻辑...")
        
        analysis = {
            'total_samples': len(samples),
            'total_cards': sum(s['total_cards'] for s in samples),
            'region_analysis': defaultdict(list),
            'card_type_analysis': defaultdict(list),
            'spatial_patterns': defaultdict(list),
            'id_assignment_rules': {}
        }
        
        # 分析每个样本
        for sample in samples:
            patterns = self.analyze_spatial_patterns(sample)
            
            # 收集区域模式
            for region_id, pattern in patterns['region_patterns'].items():
                analysis['region_analysis'][region_id].append(pattern)
                
            # 收集卡牌类型模式
            for card_type, pattern in patterns['type_patterns'].items():
                analysis['card_type_analysis'][card_type].append(pattern)
                
        # 分析总体规律
        analysis['id_assignment_rules'] = self._extract_assignment_rules(analysis)
        
        return analysis
        
    def _extract_assignment_rules(self, analysis: Dict) -> Dict:
        """提取ID分配规律"""
        rules = {
            'region_rules': {},
            'general_patterns': {},
            'consistency_analysis': {}
        }
        
        # 分析每个区域的规律
        for region_id, patterns in analysis['region_analysis'].items():
            if patterns:
                # 统计主要方向
                main_directions = [p.get('main_direction', 'unknown') for p in patterns]
                direction_counter = Counter(main_directions)
                
                # 统计一致性
                consistent_patterns = sum(1 for p in patterns if p.get('is_consistent', False))
                consistency_rate = consistent_patterns / len(patterns) if patterns else 0
                
                rules['region_rules'][region_id] = {
                    'sample_count': len(patterns),
                    'main_direction': direction_counter.most_common(1)[0][0] if direction_counter else 'unknown',
                    'direction_distribution': dict(direction_counter),
                    'consistency_rate': consistency_rate
                }
                
        # 分析卡牌类型规律
        for card_type, patterns in analysis['card_type_analysis'].items():
            if patterns:
                # 统计ID连续性
                continuous_count = sum(1 for p in patterns if p.get('is_continuous', False))
                continuity_rate = continuous_count / len(patterns) if patterns else 0
                
                rules['general_patterns'][card_type] = {
                    'sample_count': len(patterns),
                    'id_continuity_rate': continuity_rate
                }
                
        return rules
        
    def generate_report(self, analysis: Dict) -> str:
        """生成分析报告"""
        report = []
        report.append("# 人工标注逻辑深度分析报告\n")
        
        # 总体统计
        report.append(f"## 📊 总体统计")
        report.append(f"- 分析样本数: {analysis['total_samples']}")
        report.append(f"- 总卡牌数: {analysis['total_cards']}")
        report.append("")
        
        # 区域分析
        report.append("## 📍 区域ID分配规律")
        rules = analysis['id_assignment_rules']
        
        for region_id, rule in sorted(rules['region_rules'].items()):
            report.append(f"### 区域 {region_id}")
            report.append(f"- 样本数: {rule['sample_count']}")
            report.append(f"- 主要方向: {rule['main_direction']}")
            report.append(f"- 一致性: {rule['consistency_rate']:.1%}")
            report.append(f"- 方向分布: {rule['direction_distribution']}")
            report.append("")
            
        # 卡牌类型分析
        report.append("## 🎴 卡牌类型ID分配规律")
        for card_type, pattern in sorted(rules['general_patterns'].items()):
            report.append(f"### {card_type}")
            report.append(f"- 样本数: {pattern['sample_count']}")
            report.append(f"- ID连续性: {pattern['id_continuity_rate']:.1%}")
            report.append("")
            
        # 关键发现
        report.append("## 🔍 关键发现")
        
        # 分析最一致的区域
        consistent_regions = []
        for region_id, rule in rules['region_rules'].items():
            if rule['consistency_rate'] > 0.8:
                consistent_regions.append((region_id, rule['consistency_rate'], rule['main_direction']))
                
        if consistent_regions:
            report.append("### 高一致性区域")
            for region_id, consistency, direction in sorted(consistent_regions, key=lambda x: x[1], reverse=True):
                report.append(f"- 区域{region_id}: {consistency:.1%}一致性，主要方向{direction}")
            report.append("")
            
        # 分析ID连续性
        high_continuity_cards = []
        for card_type, pattern in rules['general_patterns'].items():
            if pattern['id_continuity_rate'] > 0.9:
                high_continuity_cards.append((card_type, pattern['id_continuity_rate']))
                
        if high_continuity_cards:
            report.append("### 高ID连续性卡牌")
            for card_type, continuity in sorted(high_continuity_cards, key=lambda x: x[1], reverse=True):
                report.append(f"- {card_type}: {continuity:.1%}连续性")
            report.append("")
            
        return "\n".join(report)
        
    def save_analysis(self, analysis: Dict, report: str):
        """保存分析结果"""
        # 保存详细分析数据
        analysis_file = "analysis/annotation_logic_analysis.json"
        os.makedirs(os.path.dirname(analysis_file), exist_ok=True)
        
        with open(analysis_file, 'w', encoding='utf-8') as f:
            json.dump(analysis, f, ensure_ascii=False, indent=2, default=str)
            
        # 保存分析报告
        report_file = "analysis/annotation_logic_report.md"
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report)
            
        logger.info(f"分析结果已保存:")
        logger.info(f"  - 详细数据: {analysis_file}")
        logger.info(f"  - 分析报告: {report_file}")

def main():
    """主函数"""
    print("🔍 人工标注逻辑深度分析")
    print("=" * 50)
    
    analyzer = AnnotationLogicAnalyzer()
    
    # 收集样本
    samples = analyzer.collect_samples(max_samples=100)
    
    if not samples:
        print("❌ 未找到有效样本")
        return
        
    # 分析标注逻辑
    analysis = analyzer.analyze_annotation_logic(samples)
    
    # 生成报告
    report = analyzer.generate_report(analysis)
    
    # 保存结果
    analyzer.save_analysis(analysis, report)
    
    # 打印关键发现
    print("\n🎯 关键发现:")
    print("=" * 30)
    
    rules = analysis['id_assignment_rules']
    
    # 最一致的区域
    consistent_regions = []
    for region_id, rule in rules['region_rules'].items():
        if rule['consistency_rate'] > 0.7:
            consistent_regions.append((region_id, rule['consistency_rate'], rule['main_direction']))
            
    if consistent_regions:
        print("📍 高一致性区域:")
        for region_id, consistency, direction in sorted(consistent_regions, key=lambda x: x[1], reverse=True)[:5]:
            print(f"   - 区域{region_id}: {consistency:.1%}一致性，主要方向{direction}")
    else:
        print("⚠️ 未发现高一致性区域")
        
    # ID连续性分析
    high_continuity = []
    for card_type, pattern in rules['general_patterns'].items():
        if pattern['id_continuity_rate'] > 0.8:
            high_continuity.append((card_type, pattern['id_continuity_rate']))
            
    if high_continuity:
        print("\n🎴 高ID连续性卡牌:")
        for card_type, continuity in sorted(high_continuity, key=lambda x: x[1], reverse=True)[:5]:
            print(f"   - {card_type}: {continuity:.1%}连续性")
    else:
        print("\n⚠️ ID连续性普遍较低")
        
    print(f"\n✅ 分析完成，详细报告已保存")

if __name__ == "__main__":
    main()
