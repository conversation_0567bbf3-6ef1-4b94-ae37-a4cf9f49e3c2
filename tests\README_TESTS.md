# 跑胡子AI测试脚本使用指南

本目录包含多个测试脚本，用于评估跑胡子AI系统的各个组件和整体性能。以下是各测试脚本的说明和使用方法。

## 1. 卡牌检测校准测试 (test_calibration.py)

该脚本用于评估YOLO卡牌检测模型的性能，通过将检测结果与人工标注的真实标签进行比较。

### 功能：
- 加载标注数据集
- 运行检测模型
- 比较检测结果与标注结果
- 计算准确率、召回率、F1分数等指标
- 可视化比较结果

### 使用方法：
```bash
python -m src.test_calibration [参数]
```

### 参数：
- `--images-dir`: 图像目录，默认为'ceshi/calibration_gt/images'
- `--labels-dir`: 标注目录，默认为'ceshi/calibration_gt/labels'
- `--model-path`: 模型路径，默认为'models/best.pt'
- `--output-dir`: 输出目录，默认为'output/calibration_test'
- `--conf-threshold`: 置信度阈值，默认为0.25
- `--iou-threshold`: IOU阈值，默认为0.5
- `--visualize`: 可视化结果
- `--save-json`: 保存JSON结果
- `--max-images`: 最大处理图像数量，0表示处理所有图像

### 输出：
- 总体评估指标（精确率、召回率、F1分数）
- 按类别评估指标
- 可视化结果图像（如果指定--visualize）
- 性能指标JSON文件（如果指定--save-json）

## 2. 状态转换测试 (test_state_conversion.py)

该脚本用于测试YOLO检测结果到RLCard状态的转换过程。

### 功能：
- 加载或生成YOLO检测结果
- 使用StateBuilder将检测结果转换为RLCard状态
- 可视化状态表示
- 可选：与预期状态进行比较

### 使用方法：
```bash
python -m src.test_state_conversion [参数]
```

### 参数：
- `--images-dir`: 图像目录，默认为'ceshi/calibration_gt/images'
- `--detections-dir`: 检测结果目录，为空则实时检测
- `--model-path`: 模型路径，默认为'models/best.pt'
- `--config-path`: 配置文件路径，默认为'src/config.json'
- `--output-dir`: 输出目录，默认为'output/state_conversion_test'
- `--max-images`: 最大处理图像数量，默认为10
- `--visualize`: 可视化结果
- `--save-json`: 保存JSON结果

### 输出：
- 状态转换结果的文本输出
- 可视化结果图像（如果指定--visualize）
- 状态JSON文件（如果指定--save-json）

## 3. 端到端测试 (test_end_to_end.py)

该脚本用于测试完整的端到端流程，包括检测、状态转换和决策。

### 功能：
- 加载测试图像
- 运行完整流程：检测 -> 状态转换 -> 决策
- 可视化结果
- 测量性能指标

### 使用方法：
```bash
python -m src.test_end_to_end [参数]
```

### 参数：
- `--images-dir`: 图像目录，默认为'ceshi/calibration_gt/images'
- `--model-path`: 模型路径，默认为'models/best.pt'
- `--config-path`: 配置文件路径，默认为'src/config.json'
- `--output-dir`: 输出目录，默认为'output/end_to_end_test'
- `--conf-threshold`: 置信度阈值，默认为0.25
- `--iou-threshold`: IOU阈值，默认为0.45
- `--max-images`: 最大处理图像数量，默认为10
- `--visualize`: 可视化结果
- `--save-json`: 保存JSON结果

### 输出：
- 端到端处理结果
- 性能统计（检测时间、状态转换时间、决策时间、FPS）
- 可视化结果图像（如果指定--visualize）
- 结果JSON文件（如果指定--save-json）

## 4. 视频处理测试 (test_video.py)

该脚本用于测试视频处理功能，包括逐帧检测、状态转换和决策。

### 功能：
- 加载测试视频
- 逐帧处理：检测 -> 状态转换 -> 决策
- 生成可视化结果视频
- 测量性能指标

### 使用方法：
```bash
python -m src.test_video [参数]
```

### 参数：
- `--video-path`: 视频文件路径，默认为'ceshi/shipin/video.mp4'
- `--model-path`: 模型路径，默认为'models/best.pt'
- `--config-path`: 配置文件路径，默认为'src/config.json'
- `--output-path`: 输出视频路径，默认为'output/processed_video.mp4'
- `--conf-threshold`: 置信度阈值，默认为0.25
- `--iou-threshold`: IOU阈值，默认为0.45
- `--max-frames`: 最大处理帧数，0表示处理所有帧
- `--frame-step`: 帧步长，每隔多少帧处理一次，默认为1
- `--save-json`: 保存JSON结果

### 输出：
- 处理后的视频文件
- 性能统计（检测时间、状态转换时间、决策时间、FPS）
- 帧处理结果JSON文件（如果指定--save-json）

## 5. 决策模块测试 (test_decision.py)

该脚本用于测试决策模块的功能，可以通过直接运行决策模块来测试。

### 使用方法：
```bash
python -m src.decision
```

### 输出：
- 测试决策结果
- 推荐动作、置信度和胜率

## 测试建议

1. **数据准备**：
   - 确保标注数据已正确放置在`ceshi/calibration_gt`目录下
   - 确保视频文件已放置在`ceshi/shipin`目录下

2. **模型准备**：
   - 将训练好的YOLO模型放置在`models`目录下
   - 确保配置文件`config.json`或`config.yaml`已正确设置

3. **测试顺序**：
   - 首先运行`test_calibration.py`检查检测模型性能
   - 然后运行`test_state_conversion.py`检查状态转换功能
   - 接着运行`test_end_to_end.py`检查完整流程
   - 最后运行`test_video.py`检查视频处理性能

4. **性能优化**：
   - 如果检测时间过长，可以尝试降低模型复杂度或调整置信度阈值
   - 如果FPS不足以实时处理，可以考虑跳帧处理（使用`--frame-step`参数）

5. **结果分析**：
   - 检查每个类别的精确率和召回率，关注性能较差的类别
   - 分析端到端处理的瓶颈（检测、状态转换或决策）
   - 评估实时性能是否满足需求（FPS > 视频帧率） 