"""
多算法融合区域分类器

集成多种算法的智能区域分类系统，目标将准确率从91.4%提升到95%+

融合算法：
1. 基于规则的分类器（V2.0优化版）
2. 机器学习分类器（随机森林）
3. 空间聚类算法（DBSCAN）
4. 集成学习投票机制
"""

import json
import numpy as np
from typing import List, Dict, Any, Tuple, Optional
from pathlib import Path
from collections import defaultdict, Counter
import logging

# 尝试导入机器学习库
try:
    from sklearn.ensemble import RandomForestClassifier
    from sklearn.cluster import DBSCAN
    from sklearn.preprocessing import StandardScaler
    from sklearn.model_selection import train_test_split
    from sklearn.metrics import accuracy_score
    SKLEARN_AVAILABLE = True
except ImportError:
    SKLEARN_AVAILABLE = False
    logging.warning("scikit-learn不可用，将使用简化的机器学习算法")

class EnhancedRuleBasedClassifier:
    """增强的基于规则的分类器"""
    
    def __init__(self):
        self.region_rules = self._load_optimized_rules()
    
    def _load_optimized_rules(self) -> Dict[int, Dict[str, Any]]:
        """加载针对剩余错误优化的规则"""
        rules = {
            1: {  # 手牌区_观战方（针对与区域2的混淆优化）
                "name": "手牌区_观战方",
                "x_boundary": (0.1, 0.7),  # 缩小X边界避免与区域2混淆
                "y_boundary": (0.65, 1.0),  # 提高Y下限
                "x_center": 0.485,
                "y_center": 0.808,
                "priority": 1,
                "confidence_boost": 0.2
            },
            2: {  # 区域2（针对与区域1的混淆优化）
                "name": "区域2",
                "x_boundary": (0.4, 0.9),  # 调整边界
                "y_boundary": (0.3, 0.7),  # 调整边界避免与区域1重叠
                "x_center": 0.536,
                "y_center": 0.602,
                "priority": 2,
                "confidence_boost": 0.15
            },
            7: {  # 区域7（针对与区域8的混淆优化）
                "name": "区域7",
                "x_boundary": (0.2, 0.35),  # 缩小边界
                "y_boundary": (0.15, 0.25),
                "x_center": 0.266,
                "y_center": 0.244,
                "priority": 10,
                "confidence_boost": 0.1
            },
            8: {  # 区域8（针对与区域7的混淆优化）
                "name": "区域8",
                "x_boundary": (0.15, 0.25),  # 调整边界
                "y_boundary": (0.25, 0.4),   # 调整边界
                "x_center": 0.234,
                "y_center": 0.297,
                "priority": 11,
                "confidence_boost": 0.1
            },
            # 保持其他区域规则
            3: {"name": "区域3", "x_boundary": (0.45, 0.7), "y_boundary": (0.15, 0.3), 
                "x_center": 0.506, "y_center": 0.248, "priority": 8, "confidence_boost": 0.05},
            4: {"name": "区域4", "x_boundary": (0.4, 0.6), "y_boundary": (0.2, 0.3), 
                "x_center": 0.503, "y_center": 0.250, "priority": 9, "confidence_boost": 0.05},
            5: {"name": "弃牌区_观战方", "x_boundary": (0.8, 1.0), "y_boundary": (0.8, 1.0), 
                "x_center": 0.927, "y_center": 0.967, "priority": 3, "confidence_boost": 0.1},
            6: {"name": "吃碰区_观战方", "x_boundary": (0.35, 0.6), "y_boundary": (0.2, 0.45), 
                "x_center": 0.451, "y_center": 0.330, "priority": 4, "confidence_boost": 0.08},
            9: {"name": "对战方区域", "x_boundary": (0.05, 0.3), "y_boundary": (0.25, 0.45), 
                "x_center": 0.159, "y_center": 0.350, "priority": 5, "confidence_boost": 0.05},
            10: {"name": "区域10", "x_boundary": (0.6, 0.95), "y_boundary": (0.1, 0.4), 
                 "x_center": 0.808, "y_center": 0.263, "priority": 12, "confidence_boost": 0.05},
            16: {"name": "吃碰区_对战方", "x_boundary": (0.05, 0.25), "y_boundary": (0.05, 0.35), 
                 "x_center": 0.142, "y_center": 0.196, "priority": 6, "confidence_boost": 0.08}
        }
        return rules
    
    def classify(self, x_norm: float, y_norm: float, width: float, height: float) -> Tuple[int, float]:
        """基于规则的分类"""
        best_region = 0
        best_score = 0.0
        
        for region_id, rule in self.region_rules.items():
            score = self._calculate_rule_score(x_norm, y_norm, rule)
            if score > best_score:
                best_score = score
                best_region = region_id
        
        return best_region, min(0.95, best_score)
    
    def _calculate_rule_score(self, x_norm: float, y_norm: float, rule: Dict[str, Any]) -> float:
        """计算规则分数"""
        x_min, x_max = rule["x_boundary"]
        y_min, y_max = rule["y_boundary"]
        
        if not (x_min <= x_norm <= x_max and y_min <= y_norm <= y_max):
            return 0.0
        
        # 计算到中心的距离
        x_center = rule["x_center"]
        y_center = rule["y_center"]
        distance = np.sqrt((x_norm - x_center)**2 + (y_norm - y_center)**2)
        
        # 距离越小分数越高
        score = max(0, 1.0 - distance * 2) + rule.get("confidence_boost", 0.0)
        return min(1.0, score)

class SimpleMachineLearningClassifier:
    """简化的机器学习分类器（当sklearn不可用时）"""
    
    def __init__(self):
        self.trained = False
        self.region_centroids = {}
        self.region_weights = {}
    
    def train(self, training_data: List[Tuple[List[float], int]]):
        """训练分类器"""
        if not training_data:
            return
        
        # 计算每个区域的质心
        region_points = defaultdict(list)
        for features, region_id in training_data:
            region_points[region_id].append(features)
        
        for region_id, points in region_points.items():
            points_array = np.array(points)
            self.region_centroids[region_id] = np.mean(points_array, axis=0)
            self.region_weights[region_id] = len(points)  # 样本数量作为权重
        
        self.trained = True
    
    def predict(self, features: List[float]) -> Tuple[int, float]:
        """预测区域"""
        if not self.trained or not self.region_centroids:
            return 0, 0.0
        
        features_array = np.array(features)
        best_region = 0
        min_distance = float('inf')
        
        for region_id, centroid in self.region_centroids.items():
            distance = np.linalg.norm(features_array - centroid)
            # 考虑样本权重
            weighted_distance = distance / np.sqrt(self.region_weights[region_id])
            
            if weighted_distance < min_distance:
                min_distance = weighted_distance
                best_region = region_id
        
        # 转换距离为置信度
        confidence = max(0.1, 1.0 / (1.0 + min_distance))
        return best_region, min(0.95, confidence)

class MultiAlgorithmRegionClassifier:
    """多算法融合区域分类器"""
    
    def __init__(self):
        self.rule_classifier = EnhancedRuleBasedClassifier()
        
        # 初始化机器学习分类器
        if SKLEARN_AVAILABLE:
            self.ml_classifier = RandomForestClassifier(n_estimators=100, random_state=42)
            self.scaler = StandardScaler()
            self.ml_trained = False
        else:
            self.ml_classifier = SimpleMachineLearningClassifier()
            self.scaler = None
            self.ml_trained = False
        
        self.training_data = []
        
    def add_training_data(self, bbox: List[float], true_region: int, image_width: int = 640, image_height: int = 320):
        """添加训练数据"""
        features = self._extract_features(bbox, image_width, image_height)
        self.training_data.append((features, true_region))
    
    def _extract_features(self, bbox: List[float], image_width: int, image_height: int) -> List[float]:
        """提取特征"""
        x1, y1, x2, y2 = bbox
        
        # 基础位置特征
        x_center = (x1 + x2) / 2 / image_width
        y_center = (y1 + y2) / 2 / image_height
        width = (x2 - x1) / image_width
        height = (y2 - y1) / image_height
        
        # 扩展特征
        aspect_ratio = width / height if height > 0 else 1.0
        area = width * height
        
        # 位置特征
        distance_to_center = np.sqrt((x_center - 0.5)**2 + (y_center - 0.5)**2)
        
        # 边界特征
        distance_to_left = x_center
        distance_to_right = 1.0 - x_center
        distance_to_top = y_center
        distance_to_bottom = 1.0 - y_center
        
        return [
            x_center, y_center, width, height, aspect_ratio, area,
            distance_to_center, distance_to_left, distance_to_right,
            distance_to_top, distance_to_bottom
        ]
    
    def train_ml_classifier(self):
        """训练机器学习分类器"""
        if not self.training_data:
            print("⚠️ 无训练数据，跳过ML分类器训练")
            return
        
        features = [data[0] for data in self.training_data]
        labels = [data[1] for data in self.training_data]
        
        if SKLEARN_AVAILABLE:
            # 使用sklearn
            features_array = np.array(features)
            labels_array = np.array(labels)
            
            # 标准化特征
            features_scaled = self.scaler.fit_transform(features_array)
            
            # 训练分类器
            self.ml_classifier.fit(features_scaled, labels_array)
            self.ml_trained = True
            
            print(f"✅ ML分类器训练完成，使用{len(self.training_data)}个样本")
        else:
            # 使用简化分类器
            self.ml_classifier.train(self.training_data)
            self.ml_trained = True
            
            print(f"✅ 简化ML分类器训练完成，使用{len(self.training_data)}个样本")
    
    def classify_region(self, bbox: List[float], image_width: int = 640, image_height: int = 320) -> Tuple[int, float]:
        """多算法融合分类"""
        features = self._extract_features(bbox, image_width, image_height)
        x_norm, y_norm, width, height = features[:4]
        
        # 1. 基于规则的分类
        rule_region, rule_confidence = self.rule_classifier.classify(x_norm, y_norm, width, height)
        
        # 2. 机器学习分类
        ml_region, ml_confidence = 0, 0.0
        if self.ml_trained:
            if SKLEARN_AVAILABLE:
                features_scaled = self.scaler.transform([features])
                ml_region = self.ml_classifier.predict(features_scaled)[0]
                ml_confidence = np.max(self.ml_classifier.predict_proba(features_scaled))
            else:
                ml_region, ml_confidence = self.ml_classifier.predict(features)
        
        # 3. 集成决策
        return self._ensemble_decision(
            (rule_region, rule_confidence),
            (ml_region, ml_confidence)
        )
    
    def _ensemble_decision(self, rule_result: Tuple[int, float], ml_result: Tuple[int, float]) -> Tuple[int, float]:
        """集成决策"""
        rule_region, rule_conf = rule_result
        ml_region, ml_conf = ml_result
        
        # 如果ML分类器未训练，使用规则分类器
        if not self.ml_trained:
            return rule_region, rule_conf
        
        # 加权投票
        rule_weight = 0.6  # 规则分类器权重
        ml_weight = 0.4    # ML分类器权重
        
        # 如果两个分类器结果一致，提高置信度
        if rule_region == ml_region:
            final_confidence = min(0.95, rule_conf * rule_weight + ml_conf * ml_weight + 0.1)
            return rule_region, final_confidence
        
        # 如果结果不一致，选择置信度更高的
        if rule_conf * rule_weight > ml_conf * ml_weight:
            return rule_region, rule_conf * 0.8  # 降低置信度
        else:
            return ml_region, ml_conf * 0.8
    
    def get_algorithm_statistics(self) -> Dict[str, Any]:
        """获取算法统计信息"""
        return {
            "rule_classifier": "Enhanced Rule-Based V3.0",
            "ml_classifier": "Random Forest" if SKLEARN_AVAILABLE else "Simple ML",
            "ml_trained": self.ml_trained,
            "training_samples": len(self.training_data),
            "sklearn_available": SKLEARN_AVAILABLE,
            "fusion_strategy": "Weighted Voting"
        }

def collect_training_data_from_dataset(classifier: MultiAlgorithmRegionClassifier, data_path: Path, max_samples: int = 1000):
    """从zhuangtaiquyu数据集收集训练数据"""
    print(f"📊 从数据集收集训练数据...")

    import re

    sequence_dirs = [d for d in data_path.iterdir() if d.is_dir()]
    collected_samples = 0

    for seq_dir in sorted(sequence_dirs)[:3]:  # 使用前3个序列
        if collected_samples >= max_samples:
            break

        print(f"  📁 处理序列: {seq_dir.name}")
        json_files = sorted(seq_dir.glob("*.json"))[:5]  # 每序列前5帧

        for json_file in json_files:
            if collected_samples >= max_samples:
                break

            try:
                with open(json_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)

                image_width = data.get("imageWidth", 640)
                image_height = data.get("imageHeight", 320)

                for shape in data.get("shapes", []):
                    if collected_samples >= max_samples:
                        break

                    if len(shape.get("points", [])) >= 4:
                        points = shape["points"]
                        x1, y1 = points[0]
                        x2, y2 = points[2]
                        bbox = [x1, y1, x2, y2]

                        true_region = shape.get("group_id")
                        if true_region is not None:
                            classifier.add_training_data(bbox, true_region, image_width, image_height)
                            collected_samples += 1

            except Exception as e:
                print(f"    ❌ 处理文件{json_file.name}时出错: {e}")

    print(f"✅ 收集完成，共{collected_samples}个训练样本")
    return collected_samples

def main():
    """测试多算法融合分类器"""
    print("🚀 测试多算法融合区域分类器")
    print("=" * 50)

    classifier = MultiAlgorithmRegionClassifier()

    # 显示算法信息
    stats = classifier.get_algorithm_statistics()
    print(f"📊 算法配置:")
    for key, value in stats.items():
        print(f"  {key}: {value}")

    # 从真实数据集收集训练数据
    data_path = Path("legacy_assets/ceshi/zhuangtaiquyu/labels/train")
    if data_path.exists():
        print("\n📊 从真实数据集收集训练数据...")
        samples_collected = collect_training_data_from_dataset(classifier, data_path, max_samples=500)

        if samples_collected > 0:
            # 训练ML分类器
            classifier.train_ml_classifier()
        else:
            print("⚠️ 未收集到训练数据，使用模拟数据")
            # 使用模拟数据
            training_samples = [
                ([300, 250, 350, 300], 1),
                ([400, 180, 450, 230], 2),
                ([450, 100, 500, 150], 6),
                ([100, 50, 150, 100], 16),
            ]
            for bbox, region in training_samples:
                classifier.add_training_data(bbox, region)
            classifier.train_ml_classifier()
    else:
        print("⚠️ 数据集未找到，使用模拟数据")
        training_samples = [
            ([300, 250, 350, 300], 1),
            ([400, 180, 450, 230], 2),
            ([450, 100, 500, 150], 6),
            ([100, 50, 150, 100], 16),
        ]
        for bbox, region in training_samples:
            classifier.add_training_data(bbox, region)
        classifier.train_ml_classifier()

    # 测试分类
    print("\n🧪 测试分类:")
    test_cases = [
        ([310, 260, 360, 310], "区域1测试"),
        ([410, 190, 460, 240], "区域2测试"),
        ([460, 110, 510, 160], "区域6测试"),
        ([150, 80, 200, 130], "区域16测试"),
        ([200, 200, 250, 250], "边界测试"),
    ]

    for bbox, description in test_cases:
        region_id, confidence = classifier.classify_region(bbox)
        print(f"  {description}: 区域{region_id} 置信度: {confidence:.3f}")

    print("\n✅ 测试完成！")

if __name__ == "__main__":
    main()
