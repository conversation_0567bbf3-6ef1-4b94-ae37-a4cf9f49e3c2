"""
模块1：数据验证器 (DataValidator)
只做一件事：验证输入数据的完整性和格式
"""

from typing import List, Dict, Any, Optional
from dataclasses import dataclass
import logging

logger = logging.getLogger(__name__)

@dataclass
class ValidationResult:
    """验证结果"""
    is_valid: bool
    errors: List[str]
    warnings: List[str]
    cleaned_data: List[Dict[str, Any]]

class DataValidator:
    """数据验证器 - 只负责验证输入数据"""
    
    def __init__(self):
        # 必需字段
        self.required_fields = ['label', 'bbox', 'confidence', 'group_id']
        
        # 有效的卡牌标签
        self.valid_labels = [
            "一", "二", "三", "四", "五", "六", "七", "八", "九", "十",
            "壹", "贰", "叁", "肆", "伍", "陆", "柒", "捌", "玖", "拾",
            "暗"
        ]
        
        # 有效的区域ID
        self.valid_regions = {1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16}
        
        logger.info("数据验证器初始化完成")
    
    def validate(self, detections: List[Dict[str, Any]]) -> ValidationResult:
        """验证检测数据"""
        errors = []
        warnings = []
        cleaned_data = []
        
        logger.info(f"开始验证{len(detections)}条检测数据")
        
        for i, detection in enumerate(detections):
            # 验证必需字段
            missing_fields = self._check_required_fields(detection)
            if missing_fields:
                errors.append(f"检测{i}: 缺少必需字段 {missing_fields}")
                continue
            
            # 验证数据类型
            type_errors = self._check_data_types(detection)
            if type_errors:
                errors.append(f"检测{i}: 数据类型错误 {type_errors}")
                continue
            
            # 验证数据范围
            range_warnings = self._check_data_ranges(detection)
            if range_warnings:
                warnings.extend([f"检测{i}: {w}" for w in range_warnings])
            
            # 清理和标准化数据
            cleaned_detection = self._clean_detection(detection)
            cleaned_data.append(cleaned_detection)
        
        is_valid = len(errors) == 0

        # 🔧 添加区域4数据调试
        region_4_cards = [card for card in cleaned_data if card.get('group_id') == 4]
        if region_4_cards:
            logger.info(f"🔧 [调试] DataValidator发现区域4数据: {len(region_4_cards)}张卡牌")
            for card in region_4_cards:
                label = card.get('label', 'None')
                logger.info(f"🔧 [调试] 区域4卡牌: 标签='{label}', group_id={card.get('group_id')}")
        else:
            logger.info(f"🔧 [调试] DataValidator未发现区域4数据")

        logger.info(f"验证完成: {len(cleaned_data)}条有效数据, {len(errors)}个错误, {len(warnings)}个警告")

        return ValidationResult(
            is_valid=is_valid,
            errors=errors,
            warnings=warnings,
            cleaned_data=cleaned_data
        )
    
    def _check_required_fields(self, detection: Dict[str, Any]) -> List[str]:
        """检查必需字段"""
        missing = []
        for field in self.required_fields:
            if field not in detection:
                missing.append(field)
        return missing
    
    def _check_data_types(self, detection: Dict[str, Any]) -> List[str]:
        """检查数据类型"""
        errors = []

        # 检查label是否为字符串
        if not isinstance(detection.get('label'), str):
            errors.append("label必须是字符串")

        # 检查bbox是否为列表且包含4个数字
        bbox = detection.get('bbox')
        if not isinstance(bbox, list) or len(bbox) != 4:
            errors.append("bbox必须是包含4个元素的列表")
        elif not all(isinstance(x, (int, float)) for x in bbox):
            errors.append("bbox元素必须是数字")

        # 检查confidence是否可以转换为数字
        confidence = detection.get('confidence')
        if confidence is not None:
            try:
                float(confidence)  # 尝试转换为数字
            except (ValueError, TypeError):
                errors.append("confidence必须是数字")

        # 检查group_id是否可以转换为整数
        group_id = detection.get('group_id')
        if group_id is not None:
            try:
                int(group_id)  # 尝试转换为整数
            except (ValueError, TypeError):
                errors.append("group_id必须是整数")

        return errors
    
    def _check_data_ranges(self, detection: Dict[str, Any]) -> List[str]:
        """检查数据范围"""
        warnings = []
        
        # 检查标签是否有效
        label = detection.get('label')
        if label not in self.valid_labels:
            warnings.append(f"未知标签: {label}")
        
        # 检查置信度范围（先转换为数字）
        confidence = detection.get('confidence')
        if confidence is not None:
            try:
                confidence_float = float(confidence)
                if confidence_float < 0 or confidence_float > 1:
                    warnings.append(f"置信度超出范围[0,1]: {confidence}")
            except (ValueError, TypeError):
                # 类型错误在之前的检查中已经处理
                pass

        # 检查区域ID是否有效（先转换为整数）
        group_id = detection.get('group_id')
        if group_id is not None:
            try:
                group_id_int = int(group_id)
                if group_id_int not in self.valid_regions:
                    warnings.append(f"未知区域ID: {group_id}")
            except (ValueError, TypeError):
                # 类型错误在之前的检查中已经处理
                pass
        
        # 检查bbox是否合理
        bbox = detection.get('bbox', [])
        if len(bbox) == 4:
            x, y, w, h = bbox
            if w <= 0 or h <= 0:
                warnings.append(f"bbox尺寸无效: w={w}, h={h}")
            if x < 0 or y < 0:
                warnings.append(f"bbox位置无效: x={x}, y={y}")
        
        return warnings
    
    def _clean_detection(self, detection: Dict[str, Any]) -> Dict[str, Any]:
        """清理和标准化检测数据"""
        cleaned = {}
        
        # 复制基础字段
        for field in self.required_fields:
            cleaned[field] = detection[field]
        
        # 标准化标签
        cleaned['label'] = str(detection['label']).strip()
        
        # 标准化bbox（处理None值）
        bbox = detection.get('bbox')
        if bbox is not None and len(bbox) >= 4:
            cleaned['bbox'] = [float(x) if x is not None else 0.0 for x in bbox]
        else:
            cleaned['bbox'] = [0.0, 0.0, 0.0, 0.0]  # 默认边界框
        
        # 标准化confidence（处理None值）
        confidence = detection.get('confidence')
        if confidence is not None:
            cleaned['confidence'] = float(confidence)
        else:
            cleaned['confidence'] = 1.0  # 默认置信度
        
        # 标准化group_id（处理None值）
        group_id = detection.get('group_id')
        if group_id is not None:
            cleaned['group_id'] = int(group_id)
        else:
            cleaned['group_id'] = 1  # 默认区域ID
        
        # 复制其他字段
        for key, value in detection.items():
            if key not in cleaned:
                cleaned[key] = value
        
        return cleaned

def create_data_validator():
    """创建数据验证器"""
    return DataValidator()
