# loudi_rules.yaml - 娄底跑胡子核心规则配置 V1.0
# 用于 AI 决策引擎识别与训练数据标注

game:
  name: "跑胡子"
  region: "湖南娄底"
  variant: "2人版"
  similar_games:
    - "腾讯跑胡子"
    - "大贰"
    - "煨胡子"
    - "二七十"

deck:
  description: "中国数字字牌"
  suits:
    lowercase:
      values: ["一", "二", "三", "四", "五", "六", "七", "八", "九", "十"]
      count_per_value: 4
    uppercase:
      values: ["壹", "贰", "叁", "肆", "伍", "陆", "柒", "捌", "玖", "拾"]
      count_per_value: 4
  total_tiles: 80

rules:
  basic:
    initial_hand:
      dealer: 21
      others: 20
    leftover_tiles: 19
    reveal_last_tile: true  # 庄家最后一张亮出
    turn_order: "逆时针"

  actions:
    priority: ["提", "偎/跑", "碰", "吃"]
    forced_actions:
      - "提"
      - "偎"
    optional_actions:
      - "碰"
      - "吃"
    special:
      bi_pai: true  # 比牌机制
      chou_pao: true  # 臭牌机制
      auto_run_after_first_run: true  # 第一次跑后自动跑

  combinations:
    types:
      - name: "提"
        desc: "四张同牌"
        example: "壹壹壹壹"
        trigger_hu: false
        score_multiplier: 1

      - name: "跑"
        desc: "三张已有 + 别人打出或自摸一张"
        example: "壹壹壹 + 壹(别人打出)"
        trigger_hu: false
        score_multiplier: 1

      - name: "偎"
        desc: "手中一对 + 自摸一张"
        example: "壹壹 + 壹(自摸)"
        trigger_hu: false
        score_multiplier: 1

      - name: "碰"
        desc: "手中一对 + 别人打出一张"
        example: "壹壹 + 壹(别人打出)"
        trigger_hu: false
        score_multiplier: 1

      - name: "吃"
        desc: "上家打出的牌可组成顺子"
        example: "小一二三, 大陆柒捌"
        trigger_hu: false
        score_multiplier: 1

      - name: "顺子"
        desc: "连续三张小字或大字"
        example: "一二三, 陆柒捌"
        trigger_hu: false
        score_multiplier: 1

      - name: "二七十"
        desc: "特定组合"
        example: "二、七、十"
        trigger_hu: false
        score_multiplier: 1

      - name: "大小三搭"
        desc: "两张大+1小 或 两张小+1大"
        example: "捌捌八, 十十拾"
        trigger_hu: false
        score_multiplier: 1

  play:
    allow_chow: true
    allow_pong: true
    allow_kong: true
    allow_run: true
    allow_bi_pai: true
    allow_chou_pao: true
