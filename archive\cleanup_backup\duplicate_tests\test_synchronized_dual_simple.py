"""
简单的同步双轨格式测试
验证基本功能是否正常工作
"""

import sys
import os
import json
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def test_basic_synchronized_dual_format():
    """测试基础同步双轨格式"""
    print("🚀 测试同步双轨输出系统")
    
    try:
        # 导入模块
        from src.core.digital_twin_v2 import create_digital_twin_system, CardDetection
        print("✅ 模块导入成功")
        
        # 创建系统
        dt_system = create_digital_twin_system()
        print("✅ 数字孪生系统创建成功")
        
        # 创建测试数据
        detection = CardDetection('二', [100, 100, 150, 150], 0.95, 1, '手牌_观战方', 'spectator')
        print("✅ 测试数据创建成功")
        
        # 处理数据
        result = dt_system.process_frame([detection])
        print(f"✅ 数字孪生系统处理成功: {len(result['digital_twin_cards'])}张卡牌")
        
        # 测试同步双轨输出
        dual_result = dt_system.export_synchronized_dual_format(result, 640, 320, 'test.jpg')
        print("✅ 同步双轨输出成功")
        
        # 检查结果结构
        required_keys = ['rlcard_format', 'anylabeling_format', 'consistency_validation', 'metadata']
        for key in required_keys:
            if key not in dual_result:
                print(f"❌ 缺少必需的键: {key}")
                return False
        
        print(f"✅ 结果结构完整")
        
        # 检查一致性
        consistency = dual_result['consistency_validation']
        print(f"📊 一致性分数: {consistency['consistency_score']:.3f}")
        print(f"📊 是否一致: {'✅' if consistency['is_consistent'] else '❌'}")
        
        if consistency['issues']:
            print(f"⚠️ 一致性问题: {consistency['issues']}")
        
        # 检查RLCard格式
        rlcard_format = dual_result['rlcard_format']
        rlcard_total = sum(len(cards) for cards in [
            rlcard_format.get('hand', []),
            rlcard_format.get('discard_pile', []),
            rlcard_format.get('opponent_discard_pile', []),
            rlcard_format.get('combo_cards', []),
            rlcard_format.get('opponent_combo_cards', [])
        ])
        print(f"📊 RLCard格式卡牌总数: {rlcard_total}")
        
        # 检查AnyLabeling格式
        anylabeling_format = dual_result['anylabeling_format']
        anylabeling_total = len(anylabeling_format.get('shapes', []))
        print(f"📊 AnyLabeling格式标注总数: {anylabeling_total}")
        
        # 检查数量一致性
        original_total = len(result['digital_twin_cards'])
        print(f"📊 原始卡牌总数: {original_total}")
        
        if rlcard_total == anylabeling_total == original_total:
            print("✅ 卡牌数量完全一致")
        else:
            print(f"❌ 卡牌数量不一致: 原始={original_total}, RLCard={rlcard_total}, AnyLabeling={anylabeling_total}")
        
        # 保存测试结果
        output_dir = project_root / "tests" / "synchronized_dual_simple_output"
        output_dir.mkdir(exist_ok=True)
        
        # 保存RLCard格式
        with open(output_dir / "test_rlcard.json", 'w', encoding='utf-8') as f:
            json.dump(rlcard_format, f, ensure_ascii=False, indent=2)
        
        # 保存AnyLabeling格式
        with open(output_dir / "test_anylabeling.json", 'w', encoding='utf-8') as f:
            json.dump(anylabeling_format, f, ensure_ascii=False, indent=2)
        
        # 保存一致性验证结果
        with open(output_dir / "test_consistency.json", 'w', encoding='utf-8') as f:
            json.dump(consistency, f, ensure_ascii=False, indent=2)
        
        print(f"💾 测试结果已保存到: {output_dir}")
        
        return consistency['is_consistent']
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_complex_scenario():
    """测试复杂场景"""
    print("\n🎯 测试复杂场景")
    
    try:
        from src.core.digital_twin_v2 import create_digital_twin_system, CardDetection
        
        dt_system = create_digital_twin_system()
        
        # 创建复杂测试数据
        detections = [
            CardDetection("二", [100, 100, 150, 150], 0.95, 1, "手牌_观战方", "spectator"),
            CardDetection("三", [160, 100, 210, 150], 0.92, 1, "手牌_观战方", "spectator"),
            CardDetection("四", [220, 100, 270, 150], 0.88, 2, "打出牌_观战方", "spectator"),
            CardDetection("五", [280, 100, 330, 150], 0.85, 3, "吃碰牌_观战方", "spectator")
        ]
        
        result = dt_system.process_frame(detections)
        dual_result = dt_system.export_synchronized_dual_format(result, 640, 320, 'test_complex.jpg')
        
        consistency = dual_result['consistency_validation']
        print(f"📊 复杂场景一致性分数: {consistency['consistency_score']:.3f}")
        print(f"📊 复杂场景是否一致: {'✅' if consistency['is_consistent'] else '❌'}")
        
        return consistency['is_consistent']
        
    except Exception as e:
        print(f"❌ 复杂场景测试失败: {str(e)}")
        return False

def main():
    """主函数"""
    print("🚀 开始简单同步双轨格式测试")
    print("=" * 60)
    
    # 测试基础功能
    basic_test_passed = test_basic_synchronized_dual_format()
    
    # 测试复杂场景
    complex_test_passed = test_complex_scenario()
    
    print("\n" + "=" * 60)
    print("📊 测试结果总结:")
    print(f"   基础测试: {'✅ 通过' if basic_test_passed else '❌ 失败'}")
    print(f"   复杂场景测试: {'✅ 通过' if complex_test_passed else '❌ 失败'}")
    
    overall_success = basic_test_passed and complex_test_passed
    print(f"   总体结果: {'✅ 成功' if overall_success else '❌ 失败'}")
    
    if overall_success:
        print("\n🎉 同步双轨输出系统测试成功！")
        print("   - 双轨格式输出正常")
        print("   - 一致性验证通过")
        print("   - 可以进行下一步开发")
    else:
        print("\n⚠️ 同步双轨输出系统需要修复")
        print("   - 请检查一致性验证结果")
        print("   - 修复问题后重新测试")
    
    return 0 if overall_success else 1

if __name__ == "__main__":
    sys.exit(main())
