#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
边界框格式调试脚本
分析YOLO检测结果与zhuangtaiquyu标注的边界框格式差异
"""

import os
import sys
import json
import cv2
import numpy as np
from pathlib import Path

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from src.core.detect import CardDetector


def load_annotation(dataset_id: str, frame_name: str):
    """加载标注文件"""
    base_path = Path("legacy_assets/ceshi/zhuangtaiquyu")
    json_file = base_path / "labels" / "train" / dataset_id / f"{frame_name}.json"
    
    if not json_file.exists():
        return None
    
    with open(json_file, 'r', encoding='utf-8') as f:
        return json.load(f)


def analyze_bbox_formats():
    """分析边界框格式"""
    print("🔍 分析边界框格式差异")
    print("="*50)
    
    # 初始化检测器
    detector = CardDetector("best.pt", enable_validation=False)
    
    # 测试一个样本
    dataset_id = "1"
    frame_name = "frame_00000"
    
    # 加载图片
    base_path = Path("legacy_assets/ceshi/zhuangtaiquyu")
    img_path = base_path / "images" / "train" / dataset_id / f"{frame_name}.jpg"
    
    if not img_path.exists():
        print(f"❌ 图片不存在: {img_path}")
        return
    
    image = cv2.imread(str(img_path))
    if image is None:
        print(f"❌ 无法读取图片: {img_path}")
        return
    
    print(f"📷 图片尺寸: {image.shape}")
    
    # YOLO检测
    detections = detector.detect_image(image)
    print(f"\n🎯 YOLO检测结果 ({len(detections)}个):")
    
    for i, det in enumerate(detections[:3]):  # 只显示前3个
        print(f"   检测{i+1}:")
        print(f"      标签: {det.get('label', 'unknown')}")
        print(f"      置信度: {det.get('confidence', 0):.3f}")

        # 处理bbox格式 [x, y, w, h]
        bbox = det.get('bbox', [0, 0, 0, 0])
        x, y, w, h = bbox
        x1, y1, x2, y2 = x, y, x + w, y + h

        print(f"      边界框原始: {bbox}")
        print(f"      边界框转换: x1={x1:.1f}, y1={y1:.1f}, x2={x2:.1f}, y2={y2:.1f}")
        print(f"      宽度: {w:.1f}, 高度: {h:.1f}")
    
    # 加载标注
    annotation = load_annotation(dataset_id, frame_name)
    if not annotation:
        print(f"❌ 无法加载标注文件")
        return
    
    print(f"\n📋 标注结果 ({len(annotation.get('shapes', []))}个):")
    
    for i, shape in enumerate(annotation.get('shapes', [])[:3]):  # 只显示前3个
        if shape.get('shape_type') == 'rectangle':
            points = shape.get('points', [])
            print(f"   标注{i+1}:")
            print(f"      标签: {shape.get('label', 'unknown')}")
            print(f"      区域ID: {shape.get('group_id', 0)}")
            print(f"      边界框原始: {points}")
            
            if len(points) >= 4:
                if isinstance(points[0], list):
                    # 4个点的矩形格式: [[x1, y1], [x2, y1], [x2, y2], [x1, y2]]
                    all_x = [p[0] for p in points]
                    all_y = [p[1] for p in points]
                    x1, x2 = min(all_x), max(all_x)
                    y1, y2 = min(all_y), max(all_y)
                    print(f"      边界框解析: x1={x1:.1f}, y1={y1:.1f}, x2={x2:.1f}, y2={y2:.1f}")
                    print(f"      宽度: {x2-x1:.1f}, 高度: {y2-y1:.1f}")
                else:
                    print(f"      边界框格式异常: {points}")
            elif len(points) >= 2:
                if isinstance(points[0], list):
                    # 2个点格式: [[x1, y1], [x2, y2]]
                    x1, y1 = points[0]
                    x2, y2 = points[1]
                    print(f"      边界框解析: x1={x1:.1f}, y1={y1:.1f}, x2={x2:.1f}, y2={y2:.1f}")
                    print(f"      宽度: {x2-x1:.1f}, 高度: {y2-y1:.1f}")
                else:
                    print(f"      边界框格式异常: {points}")
    
    # 尝试计算IoU
    print(f"\n🔄 尝试计算IoU...")
    
    if detections and annotation.get('shapes'):
        det = detections[0]
        shape = annotation['shapes'][0]
        
        # YOLO边界框 - 转换格式
        bbox = det.get('bbox', [0, 0, 0, 0])
        x, y, w, h = bbox
        det_bbox = [x, y, x + w, y + h]
        
        # 标注边界框
        points = shape.get('points', [])
        if len(points) >= 4 and isinstance(points[0], list):
            # 4个点的矩形格式
            all_x = [p[0] for p in points]
            all_y = [p[1] for p in points]
            x1, x2 = min(all_x), max(all_x)
            y1, y2 = min(all_y), max(all_y)
            gt_bbox = [x1, y1, x2, y2]
        elif len(points) >= 2 and isinstance(points[0], list):
            # 2个点格式
            gt_bbox = [points[0][0], points[0][1], points[1][0], points[1][1]]
        else:
            gt_bbox = [0, 0, 0, 0]

        print(f"   YOLO边界框: {det_bbox}")
        print(f"   标注边界框: {gt_bbox}")

        # 计算IoU
        iou = calculate_iou(det_bbox, gt_bbox)
        print(f"   IoU: {iou:.6f}")

        # 分析位置差异
        det_center = [(det_bbox[0] + det_bbox[2])/2, (det_bbox[1] + det_bbox[3])/2]
        gt_center = [(gt_bbox[0] + gt_bbox[2])/2, (gt_bbox[1] + gt_bbox[3])/2]

        print(f"   YOLO中心: ({det_center[0]:.1f}, {det_center[1]:.1f})")
        print(f"   标注中心: ({gt_center[0]:.1f}, {gt_center[1]:.1f})")
        print(f"   中心距离: {((det_center[0]-gt_center[0])**2 + (det_center[1]-gt_center[1])**2)**0.5:.1f}")


def calculate_iou(bbox1, bbox2):
    """计算IoU"""
    x1_1, y1_1, x2_1, y2_1 = bbox1
    x1_2, y1_2, x2_2, y2_2 = bbox2
    
    # 计算交集
    x1_inter = max(x1_1, x1_2)
    y1_inter = max(y1_1, y1_2)
    x2_inter = min(x2_1, x2_2)
    y2_inter = min(y2_1, y2_2)
    
    if x2_inter <= x1_inter or y2_inter <= y1_inter:
        return 0.0
    
    inter_area = (x2_inter - x1_inter) * (y2_inter - y1_inter)
    
    # 计算并集
    area1 = (x2_1 - x1_1) * (y2_1 - y1_1)
    area2 = (x2_2 - x1_2) * (y2_2 - y1_2)
    union_area = area1 + area2 - inter_area
    
    if union_area <= 0:
        return 0.0
    
    return inter_area / union_area


def analyze_coordinate_system():
    """分析坐标系统"""
    print(f"\n🗺️ 分析坐标系统...")
    
    # 加载图片获取尺寸
    base_path = Path("legacy_assets/ceshi/zhuangtaiquyu")
    img_path = base_path / "images" / "train" / "1" / "frame_00000.jpg"
    
    image = cv2.imread(str(img_path))
    if image is not None:
        height, width = image.shape[:2]
        print(f"   图片尺寸: 宽度={width}, 高度={height}")
        
        # 分析标注中的坐标范围
        annotation = load_annotation("1", "frame_00000")
        if annotation:
            all_x = []
            all_y = []
            
            for shape in annotation.get('shapes', []):
                points = shape.get('points', [])
                if len(points) >= 2 and isinstance(points[0], list):
                    x1, y1 = points[0]
                    x2, y2 = points[1]
                    all_x.extend([x1, x2])
                    all_y.extend([y1, y2])
            
            if all_x and all_y:
                print(f"   标注坐标范围:")
                print(f"      X: {min(all_x):.1f} ~ {max(all_x):.1f}")
                print(f"      Y: {min(all_y):.1f} ~ {max(all_y):.1f}")
                
                # 检查是否超出图片范围
                if max(all_x) > width or max(all_y) > height:
                    print(f"   ⚠️ 标注坐标超出图片范围！")
                else:
                    print(f"   ✅ 标注坐标在图片范围内")


def create_visualization():
    """创建可视化图片"""
    print(f"\n🎨 创建可视化图片...")
    
    # 初始化检测器
    detector = CardDetector("best.pt", enable_validation=False)
    
    # 加载图片
    base_path = Path("legacy_assets/ceshi/zhuangtaiquyu")
    img_path = base_path / "images" / "train" / "1" / "frame_00000.jpg"
    
    image = cv2.imread(str(img_path))
    if image is None:
        print(f"❌ 无法读取图片")
        return
    
    # 复制图片用于绘制
    vis_image = image.copy()
    
    # YOLO检测
    detections = detector.detect_image(image)
    
    # 绘制YOLO检测结果（红色）
    for det in detections[:5]:  # 只绘制前5个
        bbox = det.get('bbox', [0, 0, 0, 0])
        x, y, w, h = bbox
        x1, y1, x2, y2 = int(x), int(y), int(x + w), int(y + h)
        
        cv2.rectangle(vis_image, (x1, y1), (x2, y2), (0, 0, 255), 2)  # 红色
        cv2.putText(vis_image, f"YOLO:{det.get('label', 'unknown')}", 
                   (x1, y1-5), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 255), 1)
    
    # 加载标注并绘制（绿色）
    annotation = load_annotation("1", "frame_00000")
    if annotation:
        for i, shape in enumerate(annotation.get('shapes', [])[:5]):  # 只绘制前5个
            if shape.get('shape_type') == 'rectangle':
                points = shape.get('points', [])
                if len(points) >= 4 and isinstance(points[0], list):
                    # 4个点的矩形格式
                    all_x = [p[0] for p in points]
                    all_y = [p[1] for p in points]
                    x1, x2 = int(min(all_x)), int(max(all_x))
                    y1, y2 = int(min(all_y)), int(max(all_y))
                elif len(points) >= 2 and isinstance(points[0], list):
                    # 2个点格式
                    x1 = int(points[0][0])
                    y1 = int(points[0][1])
                    x2 = int(points[1][0])
                    y2 = int(points[1][1])
                else:
                    continue
                    
                    cv2.rectangle(vis_image, (x1, y1), (x2, y2), (0, 255, 0), 2)  # 绿色
                    cv2.putText(vis_image, f"GT:{shape.get('label', 'unknown')}", 
                               (x1, y2+15), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 1)
    
    # 保存可视化结果
    output_path = "bbox_comparison_debug.jpg"
    cv2.imwrite(output_path, vis_image)
    print(f"✅ 可视化图片已保存: {output_path}")
    print(f"   红色框: YOLO检测结果")
    print(f"   绿色框: 真实标注")


if __name__ == "__main__":
    print("🔍 边界框格式调试分析")
    print("="*60)
    
    try:
        analyze_bbox_formats()
        analyze_coordinate_system()
        create_visualization()
        
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()
