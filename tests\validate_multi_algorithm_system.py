"""
多算法融合系统验证脚本

验证集成了多算法融合区域分类器的数字孪生系统V2.0的完整性能。

验证目标：
- 区域分配准确率：从91.4%提升到95%+
- ID分配准确率：保持或提升
- 系统稳定性：确保无性能退化
"""

import sys
import os
import json
import time
from pathlib import Path
from typing import List, Dict, Any, Tuple
from collections import defaultdict, Counter
import re

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.core.digital_twin_v2 import (
    DigitalTwinCoordinator,
    CardDetection,
    create_digital_twin_system
)

class MultiAlgorithmSystemValidator:
    """多算法系统验证器"""
    
    def __init__(self):
        self.dt_system = create_digital_twin_system()
        
        # 卡牌名称映射
        self.card_name_mapping = {
            "壹": "一", "贰": "二", "叁": "三", "肆": "四", "伍": "五",
            "陆": "六", "柒": "七", "捌": "八", "玖": "九", "拾": "十"
        }
        
    def parse_ground_truth_label(self, label: str) -> Tuple[str, str]:
        """解析人工标注标签"""
        match = re.match(r'^(\d+)(.+)$', label)
        if match:
            twin_id = match.group(1)
            card_name = match.group(2)
            
            # 转换繁体到简体
            if card_name in self.card_name_mapping:
                card_name = self.card_name_mapping[card_name]
            
            return twin_id, card_name
        else:
            return "", label
    
    def validate_sequence_with_multi_algorithm(self, sequence_path: Path) -> Dict[str, Any]:
        """使用多算法系统验证序列"""
        print(f"  🔍 验证序列: {sequence_path.name}")
        
        # 重置系统
        self.dt_system.reset_session()
        
        results = {
            "sequence_name": sequence_path.name,
            "total_frames": 0,
            "total_cards": 0,
            "region_correct": 0,
            "id_correct": 0,
            "region_errors": [],
            "id_errors": [],
            "frame_results": [],
            "algorithm_stats": {}
        }
        
        json_files = sorted(sequence_path.glob("*.json"))[:8]  # 验证前8帧
        
        for frame_idx, json_file in enumerate(json_files):
            try:
                with open(json_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                # 构建检测输入
                detections = []
                ground_truth_cards = []
                
                for shape in data.get("shapes", []):
                    if len(shape.get("points", [])) >= 4:
                        points = shape["points"]
                        x1, y1 = points[0]
                        x2, y2 = points[2]
                        
                        label = shape.get("label", "")
                        twin_id, card_name = self.parse_ground_truth_label(label)
                        true_region = shape.get("group_id")
                        
                        if true_region is None or not card_name:
                            continue
                        
                        # 创建检测输入（不包含正确的区域ID）
                        detection = CardDetection(
                            label=card_name,
                            bbox=[x1, y1, x2, y2],
                            confidence=1.0,
                            group_id=0,  # 让系统自己推断
                            region_name="unknown",
                            owner="test"
                        )
                        detections.append(detection)
                        
                        # 保存真实答案
                        ground_truth_cards.append({
                            "twin_id": twin_id,
                            "card_name": card_name,
                            "true_region": true_region,
                            "bbox": [x1, y1, x2, y2]
                        })
                
                if not detections:
                    continue
                
                # 使用多算法系统处理
                dt_result = self.dt_system.process_frame(detections)
                
                # 验证结果
                frame_region_correct, frame_id_correct, frame_errors = self._validate_frame_result(
                    ground_truth_cards, dt_result["digital_twin_cards"], frame_idx
                )
                
                # 累计统计
                results["total_frames"] += 1
                results["total_cards"] += len(ground_truth_cards)
                results["region_correct"] += frame_region_correct
                results["id_correct"] += frame_id_correct
                results["region_errors"].extend(frame_errors["region_errors"])
                results["id_errors"].extend(frame_errors["id_errors"])
                
                # 记录帧结果
                frame_result = {
                    "frame_idx": frame_idx,
                    "file": json_file.name,
                    "total_cards": len(ground_truth_cards),
                    "region_correct": frame_region_correct,
                    "id_correct": frame_id_correct,
                    "region_accuracy": frame_region_correct / len(ground_truth_cards) if ground_truth_cards else 0,
                    "id_accuracy": frame_id_correct / len(ground_truth_cards) if ground_truth_cards else 0,
                    "consensus_score": dt_result["consensus_score"]
                }
                results["frame_results"].append(frame_result)
                
            except Exception as e:
                print(f"    ❌ 处理文件{json_file.name}时出错: {e}")
        
        # 计算整体准确率
        if results["total_cards"] > 0:
            results["region_accuracy"] = results["region_correct"] / results["total_cards"]
            results["id_accuracy"] = results["id_correct"] / results["total_cards"]
        else:
            results["region_accuracy"] = 0.0
            results["id_accuracy"] = 0.0
        
        # 获取算法统计信息
        if hasattr(self.dt_system, 'region_classifier') and self.dt_system.region_classifier:
            results["algorithm_stats"] = self.dt_system.region_classifier.get_algorithm_statistics()
        
        print(f"    📊 区域准确率: {results['region_accuracy']:.1%}")
        print(f"    📊 ID准确率: {results['id_accuracy']:.1%}")
        
        return results
    
    def _validate_frame_result(self, ground_truth: List[Dict], dt_cards: List, frame_idx: int) -> Tuple[int, int, Dict]:
        """验证单帧结果"""
        region_correct = 0
        id_correct = 0
        errors = {"region_errors": [], "id_errors": []}
        
        for gt_card in ground_truth:
            # 寻找最佳匹配的数字孪生卡牌
            best_match = None
            best_iou = 0.0
            
            for dt_card in dt_cards:
                if dt_card.label == gt_card["card_name"]:
                    iou = self._calculate_iou(gt_card["bbox"], dt_card.bbox)
                    if iou > best_iou:
                        best_iou = iou
                        best_match = dt_card
            
            if best_match and best_iou > 0.5:
                # 验证区域
                if best_match.group_id == gt_card["true_region"]:
                    region_correct += 1
                else:
                    errors["region_errors"].append({
                        "frame_idx": frame_idx,
                        "card": gt_card["card_name"],
                        "expected_region": gt_card["true_region"],
                        "actual_region": best_match.group_id,
                        "iou": best_iou
                    })
                
                # 验证ID
                expected_id = f"{gt_card['twin_id']}_{gt_card['card_name']}"
                if best_match.twin_id == expected_id:
                    id_correct += 1
                else:
                    errors["id_errors"].append({
                        "frame_idx": frame_idx,
                        "card": gt_card["card_name"],
                        "expected_id": expected_id,
                        "actual_id": best_match.twin_id,
                        "iou": best_iou
                    })
        
        return region_correct, id_correct, errors
    
    def _calculate_iou(self, bbox1: List[float], bbox2: List[float]) -> float:
        """计算IoU"""
        x1_1, y1_1, x2_1, y2_1 = bbox1
        x1_2, y1_2, x2_2, y2_2 = bbox2
        
        # 计算交集
        x1_inter = max(x1_1, x1_2)
        y1_inter = max(y1_1, y1_2)
        x2_inter = min(x2_1, x2_2)
        y2_inter = min(y2_1, y2_2)
        
        if x2_inter <= x1_inter or y2_inter <= y1_inter:
            return 0.0
        
        inter_area = (x2_inter - x1_inter) * (y2_inter - y1_inter)
        
        # 计算并集
        area1 = (x2_1 - x1_1) * (y2_1 - y1_1)
        area2 = (x2_2 - x1_2) * (y2_2 - y1_2)
        union_area = area1 + area2 - inter_area
        
        return inter_area / union_area if union_area > 0 else 0.0
    
    def run_comprehensive_validation(self) -> Dict[str, Any]:
        """运行全面验证"""
        print("🚀 多算法融合系统全面验证")
        print("=" * 60)
        
        data_path = Path("legacy_assets/ceshi/zhuangtaiquyu/labels/train")
        if not data_path.exists():
            print("❌ zhuangtaiquyu数据集未找到")
            return {"error": "dataset_not_found"}
        
        sequence_dirs = [d for d in data_path.iterdir() if d.is_dir()]
        sequence_dirs = sorted(sequence_dirs, key=lambda x: int(x.name) if x.name.isdigit() else 0)
        
        print(f"📊 发现 {len(sequence_dirs)} 个序列")
        
        all_results = {
            "validation_timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
            "algorithm_type": "Multi-Algorithm Fusion",
            "total_sequences": len(sequence_dirs),
            "sequence_results": [],
            "overall_statistics": {},
            "algorithm_comparison": {}
        }
        
        # 验证前3个序列
        for seq_dir in sequence_dirs[:3]:
            print(f"\n📁 处理序列: {seq_dir.name}")
            sequence_result = self.validate_sequence_with_multi_algorithm(seq_dir)
            all_results["sequence_results"].append(sequence_result)
        
        # 计算整体统计
        total_cards = sum(r["total_cards"] for r in all_results["sequence_results"])
        total_region_correct = sum(r["region_correct"] for r in all_results["sequence_results"])
        total_id_correct = sum(r["id_correct"] for r in all_results["sequence_results"])
        
        all_results["overall_statistics"] = {
            "total_cards_validated": total_cards,
            "total_region_correct": total_region_correct,
            "total_id_correct": total_id_correct,
            "overall_region_accuracy": total_region_correct / total_cards if total_cards > 0 else 0,
            "overall_id_accuracy": total_id_correct / total_cards if total_cards > 0 else 0,
            "sequences_validated": len(all_results["sequence_results"])
        }
        
        # 获取算法统计信息
        if all_results["sequence_results"]:
            first_result = all_results["sequence_results"][0]
            if "algorithm_stats" in first_result:
                all_results["algorithm_comparison"] = first_result["algorithm_stats"]
        
        return all_results

def main():
    """主验证函数"""
    print("🎯 多算法融合系统验证")
    print("=" * 60)
    
    validator = MultiAlgorithmSystemValidator()
    
    # 运行验证
    results = validator.run_comprehensive_validation()
    
    if "error" in results:
        print(f"❌ 验证失败: {results['error']}")
        return False
    
    # 保存报告
    timestamp = time.strftime("%Y%m%d_%H%M%S")
    report_file = f"tests/multi_algorithm_validation_{timestamp}.json"
    
    os.makedirs("tests", exist_ok=True)
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, ensure_ascii=False, indent=2)
    
    # 打印总结
    stats = results["overall_statistics"]
    algo_stats = results.get("algorithm_comparison", {})
    
    print("\n" + "=" * 60)
    print("📋 多算法融合系统验证总结")
    print(f"算法类型: {results.get('algorithm_type', 'Unknown')}")
    print(f"验证序列数: {stats.get('sequences_validated', 0)}")
    print(f"验证卡牌总数: {stats.get('total_cards_validated', 0)}")
    print(f"区域分配准确率: {stats.get('overall_region_accuracy', 0):.1%}")
    print(f"ID分配准确率: {stats.get('overall_id_accuracy', 0):.1%}")
    
    if algo_stats:
        print(f"\n🔧 算法配置:")
        for key, value in algo_stats.items():
            print(f"  {key}: {value}")
    
    print(f"\n报告已保存: {report_file}")
    
    # 判断性能
    region_accuracy = stats.get('overall_region_accuracy', 0)
    id_accuracy = stats.get('overall_id_accuracy', 0)
    
    if region_accuracy >= 0.95:
        print("🎉 区域分配准确率达到95%+目标！")
        return True
    elif region_accuracy >= 0.93:
        print("✅ 区域分配准确率显著提升！")
        return True
    else:
        print("⚠️ 区域分配准确率仍需进一步改进")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
