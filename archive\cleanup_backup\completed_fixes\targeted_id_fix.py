#!/usr/bin/env python3
"""
针对性ID分配修复方案

基于大数据验证发现的具体问题，实施针对性修复：
1. 虚拟牌分配过多问题
2. 系统性+1偏移问题
3. ID池管理问题
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def analyze_validation_report():
    """分析验证报告，识别具体问题"""
    report_file = "analysis/comprehensive_id_validation_report.json"
    
    if not os.path.exists(report_file):
        print("❌ 验证报告不存在")
        return
        
    import json
    with open(report_file, 'r', encoding='utf-8') as f:
        report = json.load(f)
        
    print("🔍 验证报告深度分析")
    print("=" * 50)
    
    # 分析虚拟牌问题
    virtual_errors = 0
    offset_errors = 0
    total_errors = len(report.get('detailed_errors', []))
    
    for error in report.get('detailed_errors', []):
        actual_id = error.get('actual', '')
        expected_id = error.get('expected', '')
        
        if actual_id.startswith('虚拟_'):
            virtual_errors += 1
        else:
            # 分析偏移
            try:
                expected_num = int(expected_id.split('_')[0])
                actual_num = int(actual_id.split('_')[0])
                if actual_num == expected_num + 1:
                    offset_errors += 1
            except:
                pass
                
    print(f"📊 错误类型分析:")
    print(f"   - 虚拟牌错误: {virtual_errors}/{total_errors} ({virtual_errors/total_errors*100:.1f}%)")
    print(f"   - +1偏移错误: {offset_errors}/{total_errors} ({offset_errors/total_errors*100:.1f}%)")
    print(f"   - 其他错误: {total_errors-virtual_errors-offset_errors}/{total_errors}")
    
    # 分析区域问题
    region_analysis = report.get('region_analysis', {})
    print(f"\n📍 区域问题分析:")
    
    problematic_regions = []
    for region_id, stats in region_analysis.items():
        if stats['total'] > 50:  # 只分析有足够样本的区域
            accuracy = stats['correct'] / stats['total'] * 100
            if accuracy < 50:  # 准确率低于50%的区域
                problematic_regions.append((region_id, accuracy, stats['total']))
                
    problematic_regions.sort(key=lambda x: x[1])  # 按准确率排序
    
    print("   问题区域（准确率<50%）:")
    for region_id, accuracy, total in problematic_regions:
        print(f"   - 区域{region_id}: {accuracy:.1f}% ({total}张)")
        
    return {
        'virtual_error_rate': virtual_errors / total_errors,
        'offset_error_rate': offset_errors / total_errors,
        'problematic_regions': problematic_regions
    }

def create_fix_plan(analysis_result):
    """创建修复计划"""
    print(f"\n💡 修复计划制定")
    print("=" * 50)
    
    fixes = []
    
    # 修复1：虚拟牌问题
    if analysis_result['virtual_error_rate'] > 0.3:
        fixes.append({
            'priority': 1,
            'name': '修复虚拟牌分配过多问题',
            'description': '优化ID池管理，减少虚拟牌的过早分配',
            'target_improvement': f"减少{analysis_result['virtual_error_rate']*100:.1f}%的虚拟牌错误"
        })
        
    # 修复2：+1偏移问题
    if analysis_result['offset_error_rate'] > 0.2:
        fixes.append({
            'priority': 2,
            'name': '修复系统性+1偏移问题',
            'description': '调整空间排序的起始索引，从0开始而不是从1开始',
            'target_improvement': f"减少{analysis_result['offset_error_rate']*100:.1f}%的偏移错误"
        })
        
    # 修复3：区域问题
    if analysis_result['problematic_regions']:
        fixes.append({
            'priority': 3,
            'name': '修复特定区域准确率问题',
            'description': f"优化{len(analysis_result['problematic_regions'])}个问题区域的排序规则",
            'target_improvement': "提升问题区域准确率到60%+"
        })
        
    print("🎯 修复优先级:")
    for fix in fixes:
        print(f"   {fix['priority']}. {fix['name']}")
        print(f"      - {fix['description']}")
        print(f"      - 目标: {fix['target_improvement']}")
        print()
        
    return fixes

def implement_virtual_card_fix():
    """实施虚拟牌修复"""
    print("🔧 实施修复1：虚拟牌分配优化")
    
    # 修改PhysicalCardManager的分配策略
    fix_code = '''
# 在PhysicalCardManager.allocate_id方法中添加：
def allocate_id(self, card_label: str) -> str:
    """为卡牌分配唯一ID（优化版）"""
    if card_label not in self.card_types:
        logger.warning(f"未知卡牌类型: {card_label}")
        return f"未知_{card_label}"
    
    # 检查是否还有可用的物理ID
    if self.available_ids[card_label]:
        twin_id = self.available_ids[card_label].pop(0)
        self.used_ids.add(twin_id)
        logger.debug(f"分配物理ID: {twin_id}")
        return twin_id
    else:
        # 物理ID用完时，检查是否可以回收
        if self._can_recycle_ids(card_label):
            recycled_id = self._recycle_id(card_label)
            if recycled_id:
                logger.debug(f"回收并分配ID: {recycled_id}")
                return recycled_id
                
        # 最后才分配虚拟ID
        self.virtual_counter += 1
        virtual_id = f"虚拟_{card_label}"
        logger.debug(f"分配虚拟ID: {virtual_id}")
        return virtual_id
'''
    
    print("   - 添加ID回收机制")
    print("   - 延迟虚拟ID分配")
    print("   - 优化ID池管理")

def implement_offset_fix():
    """实施+1偏移修复"""
    print("🔧 实施修复2：+1偏移问题修复")
    
    # 关键修复：enumerate从0开始
    fix_code = '''
# 在assign_spatial_ids方法中修改：
for i, card in enumerate(sorted_cards, 0):  # 从0开始而不是1
    if i < 4:  # 0,1,2,3对应1,2,3,4
        twin_id = f"{i+1}_{card_type}"
        # ...
'''
    
    print("   - 修改enumerate起始索引")
    print("   - 调整ID分配逻辑")
    print("   - 验证排序一致性")

def implement_region_fix():
    """实施区域修复"""
    print("🔧 实施修复3：区域排序规则优化")
    
    print("   - 优化区域3的排序规则")
    print("   - 调整区域9的排序参数")
    print("   - 增强区域16的处理逻辑")

def main():
    """主函数"""
    print("🎯 针对性ID分配修复方案")
    print("=" * 60)
    
    # 分析验证报告
    analysis_result = analyze_validation_report()
    
    if not analysis_result:
        return
        
    # 制定修复计划
    fixes = create_fix_plan(analysis_result)
    
    # 实施修复
    print("🚀 开始实施修复...")
    
    for fix in fixes:
        if fix['priority'] == 1:
            implement_virtual_card_fix()
        elif fix['priority'] == 2:
            implement_offset_fix()
        elif fix['priority'] == 3:
            implement_region_fix()
            
    print("\n✅ 修复方案制定完成")
    print("📋 下一步：手动实施代码修改并重新验证")

if __name__ == "__main__":
    main()
