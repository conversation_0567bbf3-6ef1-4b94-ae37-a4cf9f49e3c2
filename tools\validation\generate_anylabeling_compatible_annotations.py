#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
AnyLabeling兼容标注生成器

使用与AnyLabeling完全一致的设置：
1. 使用ONNX模型
2. 关闭数据清洗 (enable_validation=False)
3. 使用优化的阈值
"""

import os
import sys
import cv2
import json
import time
import shutil
from pathlib import Path
from typing import Dict, List, Any, Tuple
import numpy as np

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)

from ultralytics import YOLO
from src.core.state_builder import format_detections_for_state_builder


class AnyLabelingCompatibleGenerator:
    """AnyLabeling兼容标注生成器"""
    
    def __init__(self, 
                 dataset_path: str = "legacy_assets/ceshi/calibration_gt"):
        """
        初始化生成器
        
        Args:
            dataset_path: 数据集路径
        """
        # 使用ONNX模型（与AnyLabeling一致）
        self.onnx_model_path = r"D:\phz-ai-simple\data\processed\train9.0\weights\best.onnx"
        self.yaml_path = r"D:\phz-ai-simple\data\processed\train9.0\weights\yolov8x9.0.yaml"
        
        self.dataset_path = dataset_path
        self.images_dir = os.path.join(dataset_path, "images")
        self.original_labels_dir = os.path.join(dataset_path, "labels")
        
        # 输出目录
        dataset_name = os.path.basename(dataset_path)
        self.output_base_dir = f"legacy_assets/ceshi/{dataset_name}_anylabeling_compatible"
        self.output_images_dir = os.path.join(self.output_base_dir, "images")
        self.output_labels_dir = os.path.join(self.output_base_dir, "labels")
        
        # 加载ONNX模型
        self.model = YOLO(self.onnx_model_path)
        
        # 从YAML加载类别映射
        self.class_names = self._load_class_names_from_yaml()
        
        # 项目标准映射
        self.label_to_id = {
            "一": 1, "二": 2, "三": 3, "四": 4, "五": 5, "六": 6, "七": 7, "八": 8, "九": 9, "十": 10,
            "壹": 11, "贰": 12, "叁": 13, "肆": 14, "伍": 15, "陆": 16, "柒": 17, "捌": 18, "玖": 19, "拾": 20,
            "暗": 21, "吃": 22, "碰": 23, "胡": 24, "过": 25, "打鸟选择": 26, "已准备": 27,
            "你赢了": 28, "你输了": 29, "荒庄": 30, "牌局结束": 31
        }
        self.id_to_label = {v: k for k, v in self.label_to_id.items()}
        
        # 统计信息
        self.statistics = {
            'total_images': 0,
            'processed_images': 0,
            'total_detections': 0,
            'processing_time': 0,
            'anylabeling_compatibility': {
                'uses_onnx_model': True,
                'data_cleaning_disabled': True,
                'conf_threshold': 0.01,
                'iou_threshold': 0.1,
                'expected_high_recall': True
            }
        }
        
        print(f"🎯 AnyLabeling兼容标注生成器初始化完成")
        print(f"   - ONNX模型: {self.onnx_model_path}")
        print(f"   - YAML配置: {self.yaml_path}")
        print(f"   - 输入: {dataset_path}")
        print(f"   - 输出: {self.output_base_dir}")
        print(f"   - 置信度阈值: 0.01 (极低，与AnyLabeling一致)")
        print(f"   - IoU阈值: 0.1 (极低，与AnyLabeling一致)")
        print(f"   - 数据清洗: 关闭 (与AnyLabeling一致)")
        print(f"   - 类别数: {len(self.class_names)}")
    
    def _load_class_names_from_yaml(self) -> List[str]:
        """从YAML文件加载类别名称"""
        class_names = []
        
        try:
            with open(self.yaml_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            in_classes_section = False
            for line in lines:
                line = line.strip()
                if line == 'classes:':
                    in_classes_section = True
                    continue
                
                if in_classes_section:
                    if line.startswith('- '):
                        # 提取类别名称
                        class_name = line[2:].split('#')[0].strip()
                        class_names.append(class_name)
                    elif line and not line.startswith(' ') and not line.startswith('-'):
                        break
                        
        except Exception as e:
            print(f"❌ 加载YAML失败: {e}")
        
        return class_names
    
    def generate_anylabeling_compatible_annotations(self, 
                                                  max_images: int = None,
                                                  copy_images: bool = True) -> Dict[str, Any]:
        """
        生成AnyLabeling兼容的标注
        
        Args:
            max_images: 最大处理图像数（None表示全部）
            copy_images: 是否复制图像文件
            
        Returns:
            生成结果统计
        """
        print(f"🚀 开始生成AnyLabeling兼容标注...")
        
        # 1. 创建输出目录
        self._create_output_directories()
        
        # 2. 收集所有图像
        image_files = self._collect_image_files(max_images)
        print(f"📊 收集到 {len(image_files)} 张图像")
        
        # 3. 复制图像文件（可选）
        if copy_images:
            self._copy_images(image_files)
        
        # 4. 处理所有图像
        self._process_all_images(image_files)
        
        # 5. 生成统计报告
        report = self._generate_report()
        
        print(f"✅ AnyLabeling兼容标注生成完成")
        return report
    
    def _create_output_directories(self) -> None:
        """创建输出目录"""
        os.makedirs(self.output_images_dir, exist_ok=True)
        os.makedirs(self.output_labels_dir, exist_ok=True)
        print(f"📁 创建输出目录: {self.output_base_dir}")
    
    def _collect_image_files(self, max_images: int = None) -> List[str]:
        """收集图像文件"""
        image_files = []
        
        # 处理不同的目录结构
        if os.path.exists(self.images_dir):
            # 标准结构：images/目录
            for filename in sorted(os.listdir(self.images_dir)):
                if filename.lower().endswith(('.jpg', '.jpeg', '.png')):
                    image_files.append(filename)
        else:
            # 检查是否有train子目录
            train_dir = os.path.join(self.dataset_path, "images", "train")
            if os.path.exists(train_dir):
                # 遍历区域子目录
                for region_dir in sorted(os.listdir(train_dir)):
                    region_path = os.path.join(train_dir, region_dir)
                    if os.path.isdir(region_path):
                        for filename in sorted(os.listdir(region_path)):
                            if filename.lower().endswith(('.jpg', '.jpeg', '.png')):
                                # 存储相对路径
                                relative_path = os.path.join(region_dir, filename)
                                image_files.append(relative_path)
        
        if max_images:
            image_files = image_files[:max_images]
        
        self.statistics['total_images'] = len(image_files)
        return image_files
    
    def _copy_images(self, image_files: List[str]) -> None:
        """复制图像文件"""
        print(f"📋 复制图像文件...")
        
        for filename in image_files:
            # 处理不同的源路径结构
            if os.path.exists(self.images_dir):
                src_path = os.path.join(self.images_dir, filename)
            else:
                train_dir = os.path.join(self.dataset_path, "images", "train")
                src_path = os.path.join(train_dir, filename)
            
            # 目标路径只保留文件名
            dst_filename = os.path.basename(filename)
            dst_path = os.path.join(self.output_images_dir, dst_filename)
            
            if os.path.exists(src_path):
                shutil.copy2(src_path, dst_path)
        
        print(f"✅ 复制了 {len(image_files)} 张图像")
    
    def _process_all_images(self, image_files: List[str]) -> None:
        """处理所有图像"""
        print(f"🔬 开始处理图像...")
        
        start_time = time.time()
        
        for i, filename in enumerate(image_files):
            try:
                # 读取图像
                if os.path.exists(self.images_dir):
                    image_path = os.path.join(self.images_dir, filename)
                else:
                    train_dir = os.path.join(self.dataset_path, "images", "train")
                    image_path = os.path.join(train_dir, filename)
                
                image = cv2.imread(image_path)
                
                if image is None:
                    print(f"⚠️ 无法读取图像: {filename}")
                    continue
                
                # 处理图像
                dst_filename = os.path.basename(filename)
                frame_id = Path(dst_filename).stem
                self._process_single_image(image, frame_id, dst_filename)
                
                self.statistics['processed_images'] += 1
                
                # 显示进度
                if (i + 1) % 20 == 0:
                    progress = (i + 1) / len(image_files) * 100
                    print(f"   进度: {i+1}/{len(image_files)} ({progress:.1f}%)")
                
            except Exception as e:
                print(f"❌ 处理图像失败: {filename}, 错误: {e}")
        
        self.statistics['processing_time'] = time.time() - start_time
        print(f"✅ 图像处理完成，共处理 {self.statistics['processed_images']} 张")
    
    def _process_single_image(self, image: np.ndarray, frame_id: str, filename: str) -> None:
        """处理单张图像"""
        # 1. 使用ONNX模型检测（与AnyLabeling一致）
        results = self.model(image, conf=0.01, iou=0.1, verbose=False)
        
        # 2. 转换YOLO输出为标准格式
        detections = self._convert_yolo_results(results[0])
        
        # 3. 过滤掉background类别
        filtered_detections = [d for d in detections if d.get('label', '') != 'background']
        
        # 4. 使用标准区域分配逻辑
        height, width = image.shape[:2]
        region_assigned_detections = format_detections_for_state_builder(filtered_detections, (height, width))
        
        # 5. 生成AnyLabeling兼容的JSON
        anylabeling_json = self._create_anylabeling_json(
            image, region_assigned_detections, filename, frame_id
        )
        
        # 6. 保存JSON文件
        json_filename = f"{frame_id}.json"
        json_path = os.path.join(self.output_labels_dir, json_filename)
        
        with open(json_path, 'w', encoding='utf-8') as f:
            json.dump(anylabeling_json, f, ensure_ascii=False, indent=2)
        
        # 7. 更新统计
        self.statistics['total_detections'] += len(region_assigned_detections)
    
    def _convert_yolo_results(self, yolo_result) -> List[Dict[str, Any]]:
        """转换YOLO结果为标准格式"""
        detections = []
        
        if yolo_result.boxes is not None:
            for box in yolo_result.boxes:
                cls_id = int(box.cls[0])
                conf = float(box.conf[0])
                xyxy = box.xyxy[0].tolist()
                
                # 转换为[x, y, w, h]格式
                x1, y1, x2, y2 = xyxy
                x, y, w, h = x1, y1, x2 - x1, y2 - y1
                
                # 获取类别名称（修复映射）
                if cls_id == 0:
                    label = "background"
                else:
                    label = self.id_to_label.get(cls_id, "unknown")
                
                detection = {
                    'label': label,
                    'confidence': conf,
                    'bbox': [x, y, w, h],
                    'cls_id': cls_id
                }
                detections.append(detection)
        
        return detections
    
    def _create_anylabeling_json(self, 
                               image: np.ndarray,
                               detections: List[Dict[str, Any]],
                               filename: str,
                               frame_id: str) -> Dict[str, Any]:
        """创建AnyLabeling兼容的JSON格式"""
        height, width = image.shape[:2]
        
        # 基础结构
        anylabeling_json = {
            "version": "2.4.3",
            "flags": {},
            "shapes": [],
            "imagePath": filename,
            "imageData": None,
            "imageHeight": height,
            "imageWidth": width,
            # AnyLabeling兼容信息
            "anylabeling_compatible_metadata": {
                "generation_timestamp": time.time(),
                "frame_id": frame_id,
                "uses_onnx_model": True,
                "data_cleaning_disabled": True,
                "conf_threshold": 0.01,
                "iou_threshold": 0.1,
                "model_path": self.onnx_model_path,
                "expected_high_recall": True,
                "generation_method": "anylabeling_compatible"
            }
        }
        
        # 处理每个检测结果
        for i, detection in enumerate(detections):
            shape = self._create_shape_from_detection(detection, i)
            anylabeling_json["shapes"].append(shape)
        
        return anylabeling_json
    
    def _create_shape_from_detection(self, detection: Dict[str, Any], index: int) -> Dict[str, Any]:
        """从检测结果创建shape"""
        bbox = detection.get('bbox', [])
        if len(bbox) != 4:
            bbox = [0, 0, 50, 50]  # 默认值
        
        x, y, w, h = bbox
        
        # 使用标准的区域名称和所有者
        region_name = detection.get('region_name', '未知区域')
        group_id = detection.get('group_id', 1)
        owner = self._get_owner_by_group_id(group_id)
        
        shape = {
            "kie_linking": [],
            "region_name": region_name,
            "owner": owner,
            "label": detection.get('label', 'unknown'),
            "score": detection.get('confidence', 0.0),
            "points": [
                [x, y],
                [x + w, y],
                [x + w, y + h],
                [x, y + h]
            ],
            "group_id": group_id,
            "description": "",
            "difficult": False,
            "shape_type": "rectangle",
            "flags": {},
            "attributes": {
                # AnyLabeling兼容属性
                "anylabeling_compatible": True,
                "uses_onnx_model": True,
                "data_cleaning_disabled": True,
                "detection_method": "onnx_no_cleaning",
                "expected_high_recall": True
            }
        }
        
        return shape
    
    def _get_owner_by_group_id(self, group_id: int) -> str:
        """根据group_id确定所有者（基于GAME_RULES_OPTIMIZED.md）"""
        if group_id in [1, 2, 3, 4, 5, 6, 10, 11, 12]:  # 观战方区域
            return "spectator"
        elif group_id in [7, 8, 9, 16]:  # 对战方区域
            return "opponent"
        elif group_id in [13, 14, 15]:  # 结算区域
            if group_id == 14:
                return "winner"
            elif group_id == 15:
                return "loser"
            else:
                return "neutral"
        else:
            return "spectator"
    
    def _generate_report(self) -> Dict[str, Any]:
        """生成统计报告"""
        report = {
            "generation_summary": {
                "total_images": self.statistics['total_images'],
                "processed_images": self.statistics['processed_images'],
                "success_rate": self.statistics['processed_images'] / self.statistics['total_images'] if self.statistics['total_images'] > 0 else 0,
                "processing_time": self.statistics['processing_time'],
                "avg_time_per_image": self.statistics['processing_time'] / self.statistics['processed_images'] if self.statistics['processed_images'] > 0 else 0
            },
            "detection_statistics": {
                "total_detections": self.statistics['total_detections'],
                "avg_detections_per_image": self.statistics['total_detections'] / self.statistics['processed_images'] if self.statistics['processed_images'] > 0 else 0
            },
            "anylabeling_compatibility": self.statistics['anylabeling_compatibility'],
            "output_info": {
                "output_directory": self.output_base_dir,
                "images_directory": self.output_images_dir,
                "labels_directory": self.output_labels_dir,
                "anylabeling_compatible": True
            },
            "quality_assurance": {
                "uses_onnx_model": True,
                "data_cleaning_disabled": True,
                "anylabeling_compatible": True,
                "expected_high_recall": True,
                "recommended_for_production": True
            }
        }
        
        # 保存报告
        report_path = os.path.join(self.output_base_dir, "anylabeling_compatible_report.json")
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        return report


def main():
    """主函数"""
    print("🎯 AnyLabeling兼容标注生成器")
    print("=" * 50)
    
    # 为calibration_gt生成AnyLabeling兼容标注
    print("\n📋 处理calibration_gt数据集...")
    calibration_generator = AnyLabelingCompatibleGenerator(
        dataset_path="legacy_assets/ceshi/calibration_gt"
    )
    
    calibration_report = calibration_generator.generate_anylabeling_compatible_annotations(
        max_images=None,  # 处理全部图像
        copy_images=True
    )
    
    print(f"\n📊 calibration_gt结果:")
    print(f"   处理图像: {calibration_report['generation_summary']['processed_images']}")
    print(f"   总检测数: {calibration_report['detection_statistics']['total_detections']}")
    print(f"   成功率: {calibration_report['generation_summary']['success_rate']:.1%}")
    print(f"   使用ONNX模型: {calibration_report['anylabeling_compatibility']['uses_onnx_model']}")
    print(f"   数据清洗关闭: {calibration_report['anylabeling_compatibility']['data_cleaning_disabled']}")
    print(f"   预期高召回率: {calibration_report['anylabeling_compatibility']['expected_high_recall']}")
    
    print(f"\n📁 输出目录: {calibration_report['output_info']['output_directory']}")
    
    print(f"\n🎯 AnyLabeling兼容性:")
    print(f"   ONNX模型: ✅")
    print(f"   数据清洗关闭: ✅")
    print(f"   极低阈值: ✅")
    print(f"   高召回率: ✅")
    print(f"   推荐生产使用: ✅")


if __name__ == "__main__":
    main()
