#!/usr/bin/env python3
"""
精确ID分配错误分析工具

基于开发过程10的验证报告，深入分析ID分配的具体错误模式
"""

import json
import os
from collections import defaultdict, Counter
from typing import Dict, List, Tuple
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class PreciseIDErrorAnalyzer:
    """精确ID分配错误分析器"""
    
    def __init__(self):
        self.offset_errors = []
        self.card_specific_errors = defaultdict(list)
        self.error_patterns = defaultdict(int)
        
    def analyze_validation_report(self, report_file: str):
        """分析验证报告中的ID错误"""
        logger.info(f"分析验证报告: {report_file}")
        
        try:
            with open(report_file, 'r', encoding='utf-8') as f:
                report_data = json.load(f)
                
            # 分析sample_id_errors
            if 'error_analysis' in report_data and 'sample_id_errors' in report_data['error_analysis']:
                id_errors = report_data['error_analysis']['sample_id_errors']
                logger.info(f"发现 {len(id_errors)} 个ID错误样本")
                
                for error in id_errors:
                    self._analyze_single_error(error)
                    
            # 分析整体统计
            if 'overall_statistics' in report_data:
                stats = report_data['overall_statistics']
                logger.info(f"整体ID准确率: {stats.get('id_accuracy', 0)*100:.1f}%")
                
            return self._generate_analysis_report()
            
        except Exception as e:
            logger.error(f"分析验证报告时出错: {e}")
            return {}
            
    def _analyze_single_error(self, error: Dict):
        """分析单个错误"""
        card_name = error.get('card_name', '')
        expected_id = error.get('expected_id', '')
        actual_id = error.get('actual_id', '')
        frame_idx = error.get('frame_idx', 0)
        
        # 提取ID数字
        expected_num = self._extract_id_number(expected_id)
        actual_num = self._extract_id_number(actual_id)
        
        if expected_num is not None and actual_num is not None:
            offset = actual_num - expected_num
            
            # 记录偏移错误
            self.offset_errors.append({
                'card_name': card_name,
                'expected_num': expected_num,
                'actual_num': actual_num,
                'offset': offset,
                'expected_id': expected_id,
                'actual_id': actual_id,
                'frame_idx': frame_idx
            })
            
            # 记录错误模式
            pattern = f"{expected_id} -> {actual_id}"
            self.error_patterns[pattern] += 1
            
        # 记录卡牌特定错误
        self.card_specific_errors[card_name].append({
            'expected_id': expected_id,
            'actual_id': actual_id,
            'frame_idx': frame_idx,
            'offset': offset if expected_num is not None and actual_num is not None else None
        })
        
    def _extract_id_number(self, id_str: str) -> int:
        """从ID字符串中提取数字"""
        try:
            if '_' in id_str and not id_str.startswith('虚拟_'):
                parts = id_str.split('_')
                return int(parts[0])
            return None
        except:
            return None
            
    def _generate_analysis_report(self) -> Dict:
        """生成分析报告"""
        report = {
            'summary': self._generate_summary(),
            'offset_analysis': self._analyze_offsets(),
            'card_specific_analysis': self._analyze_card_specific_errors(),
            'pattern_analysis': self._analyze_error_patterns(),
            'recommendations': self._generate_recommendations()
        }
        
        return report
        
    def _generate_summary(self) -> Dict:
        """生成总结"""
        total_errors = len(self.offset_errors)
        unique_cards = len(self.card_specific_errors)
        
        return {
            'total_id_errors': total_errors,
            'unique_cards_with_errors': unique_cards,
            'error_rate_analysis': f"共{total_errors}个ID错误，涉及{unique_cards}种不同卡牌"
        }
        
    def _analyze_offsets(self) -> Dict:
        """分析偏移错误"""
        if not self.offset_errors:
            return {'message': '未发现偏移错误'}
            
        # 统计偏移分布
        offset_counter = Counter(error['offset'] for error in self.offset_errors)
        
        # 分析最常见的偏移
        most_common_offset = offset_counter.most_common(1)[0]
        
        # 按卡牌分析偏移
        card_offsets = defaultdict(list)
        for error in self.offset_errors:
            card_offsets[error['card_name']].append(error['offset'])
            
        # 分析偏移的一致性
        consistent_offsets = {}
        for card, offsets in card_offsets.items():
            offset_counts = Counter(offsets)
            if len(offset_counts) == 1:  # 只有一种偏移
                consistent_offsets[card] = list(offset_counts.keys())[0]
                
        return {
            'total_offset_errors': len(self.offset_errors),
            'offset_distribution': dict(offset_counter),
            'most_common_offset': {
                'value': most_common_offset[0],
                'count': most_common_offset[1],
                'percentage': most_common_offset[1] / len(self.offset_errors) * 100
            },
            'consistent_offset_cards': consistent_offsets,
            'systematic_offset_analysis': self._analyze_systematic_offset()
        }
        
    def _analyze_systematic_offset(self) -> Dict:
        """分析系统性偏移"""
        if not self.offset_errors:
            return {}
            
        # 检查是否存在系统性的+1偏移
        plus_one_count = sum(1 for error in self.offset_errors if error['offset'] == 1)
        plus_one_percentage = plus_one_count / len(self.offset_errors) * 100
        
        # 检查是否存在系统性的-1偏移
        minus_one_count = sum(1 for error in self.offset_errors if error['offset'] == -1)
        minus_one_percentage = minus_one_count / len(self.offset_errors) * 100
        
        return {
            'plus_one_errors': {
                'count': plus_one_count,
                'percentage': plus_one_percentage,
                'is_systematic': plus_one_percentage > 50
            },
            'minus_one_errors': {
                'count': minus_one_count,
                'percentage': minus_one_percentage,
                'is_systematic': minus_one_percentage > 50
            }
        }
        
    def _analyze_card_specific_errors(self) -> Dict:
        """分析特定卡牌错误"""
        card_error_counts = {
            card: len(errors) 
            for card, errors in self.card_specific_errors.items()
        }
        
        # 按错误数量排序
        sorted_cards = sorted(card_error_counts.items(), key=lambda x: x[1], reverse=True)
        
        # 分析每种卡牌的错误模式
        card_patterns = {}
        for card, errors in self.card_specific_errors.items():
            offsets = [e['offset'] for e in errors if e['offset'] is not None]
            if offsets:
                offset_counter = Counter(offsets)
                card_patterns[card] = {
                    'total_errors': len(errors),
                    'offset_distribution': dict(offset_counter),
                    'most_common_offset': offset_counter.most_common(1)[0]
                }
                
        return {
            'total_cards_with_errors': len(card_error_counts),
            'top_error_cards': sorted_cards[:10],
            'card_error_patterns': card_patterns,
            'error_distribution': card_error_counts
        }
        
    def _analyze_error_patterns(self) -> Dict:
        """分析错误模式"""
        # 最常见的错误模式
        top_patterns = Counter(self.error_patterns).most_common(10)
        
        return {
            'total_unique_patterns': len(self.error_patterns),
            'top_error_patterns': top_patterns,
            'pattern_analysis': self._categorize_patterns()
        }
        
    def _categorize_patterns(self) -> Dict:
        """分类错误模式"""
        plus_one_patterns = []
        minus_one_patterns = []
        other_patterns = []
        
        for pattern, count in self.error_patterns.items():
            if ' -> ' in pattern:
                expected, actual = pattern.split(' -> ')
                expected_num = self._extract_id_number(expected)
                actual_num = self._extract_id_number(actual)
                
                if expected_num is not None and actual_num is not None:
                    offset = actual_num - expected_num
                    if offset == 1:
                        plus_one_patterns.append((pattern, count))
                    elif offset == -1:
                        minus_one_patterns.append((pattern, count))
                    else:
                        other_patterns.append((pattern, count))
                        
        return {
            'plus_one_patterns': plus_one_patterns,
            'minus_one_patterns': minus_one_patterns,
            'other_patterns': other_patterns
        }
        
    def _generate_recommendations(self) -> List[str]:
        """生成改进建议"""
        recommendations = []
        
        if not self.offset_errors:
            recommendations.append("未发现ID分配错误，系统运行正常")
            return recommendations
            
        # 基于偏移分析的建议
        offset_counter = Counter(error['offset'] for error in self.offset_errors)
        most_common_offset = offset_counter.most_common(1)[0]
        
        if most_common_offset[1] > len(self.offset_errors) * 0.5:  # 超过50%
            if most_common_offset[0] == 1:
                recommendations.append(
                    f"🔴 发现系统性+1偏移问题：{most_common_offset[1]}/{len(self.offset_errors)}个错误({most_common_offset[1]/len(self.offset_errors)*100:.1f}%)，"
                    f"建议检查空间排序算法的起始索引，可能需要从0开始而不是从1开始"
                )
            elif most_common_offset[0] == -1:
                recommendations.append(
                    f"🔴 发现系统性-1偏移问题：{most_common_offset[1]}/{len(self.offset_errors)}个错误({most_common_offset[1]/len(self.offset_errors)*100:.1f}%)，"
                    f"建议检查空间排序算法的起始索引，可能需要从1开始而不是从0开始"
                )
                
        # 基于卡牌特定错误的建议
        if self.card_specific_errors:
            top_error_card = max(self.card_specific_errors.items(), key=lambda x: len(x[1]))
            if len(top_error_card[1]) > 3:  # 错误超过3次
                recommendations.append(
                    f"🎯 卡牌'{top_error_card[0]}'错误率最高({len(top_error_card[1])}次)，"
                    f"建议针对该卡牌优化识别和空间排序逻辑"
                )
                
        # 基于错误数量的建议
        total_errors = len(self.offset_errors)
        if total_errors > 100:
            recommendations.append(
                f"⚠️ ID分配错误数量较多({total_errors}个)，建议优先解决系统性偏移问题"
            )
        elif total_errors > 50:
            recommendations.append(
                f"🔧 ID分配错误数量中等({total_errors}个)，建议进行精细化调优"
            )
        else:
            recommendations.append(
                f"✅ ID分配错误数量较少({total_errors}个)，系统基本正常，可进行优化提升"
            )
            
        return recommendations

def main():
    """主函数"""
    analyzer = PreciseIDErrorAnalyzer()
    
    # 分析最新的验证报告
    report_file = "tests/ground_truth_validation_report_20250717_070024.json"
    
    if not os.path.exists(report_file):
        logger.error(f"验证报告文件不存在: {report_file}")
        return
        
    logger.info("开始精确ID分配错误分析...")
    report = analyzer.analyze_validation_report(report_file)
    
    # 保存分析报告
    output_path = "analysis/precise_id_error_analysis_report.json"
    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    
    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump(report, f, ensure_ascii=False, indent=2)
        
    logger.info(f"分析报告已保存到: {output_path}")
    
    # 打印关键发现
    print("\n🔍 ID分配错误精确分析结果:")
    print("=" * 60)
    
    if 'summary' in report:
        summary = report['summary']
        print(f"📊 总体情况: {summary.get('error_rate_analysis', '')}")
        
    if 'offset_analysis' in report and 'most_common_offset' in report['offset_analysis']:
        offset_info = report['offset_analysis']['most_common_offset']
        print(f"📈 最常见偏移: {offset_info['value']:+d} (出现{offset_info['count']}次，占{offset_info['percentage']:.1f}%)")
        
        # 系统性偏移分析
        if 'systematic_offset_analysis' in report['offset_analysis']:
            sys_analysis = report['offset_analysis']['systematic_offset_analysis']
            if sys_analysis.get('plus_one_errors', {}).get('is_systematic', False):
                print(f"🔴 系统性+1偏移: {sys_analysis['plus_one_errors']['percentage']:.1f}%")
            if sys_analysis.get('minus_one_errors', {}).get('is_systematic', False):
                print(f"🔴 系统性-1偏移: {sys_analysis['minus_one_errors']['percentage']:.1f}%")
                
    if 'card_specific_analysis' in report and 'top_error_cards' in report['card_specific_analysis']:
        top_cards = report['card_specific_analysis']['top_error_cards'][:5]
        print(f"🎯 错误最多的卡牌:")
        for card, count in top_cards:
            print(f"   - {card}: {count}次错误")
            
    print(f"\n💡 改进建议:")
    for i, rec in enumerate(report.get('recommendations', []), 1):
        print(f"   {i}. {rec}")

if __name__ == "__main__":
    main()
