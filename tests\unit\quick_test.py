#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
快速系统验证脚本
检查项目的基础功能是否正常
"""

import os
import sys
import traceback

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)

def test_imports():
    """测试关键模块导入"""
    print("🔍 测试模块导入...")
    
    try:
        from src.main import PaohuziAI
        print("✅ 主程序导入成功")
    except Exception as e:
        print(f"❌ 主程序导入失败: {e}")
        return False
    
    try:
        from src.core.detect import CardDetector
        print("✅ 检测模块导入成功")
    except Exception as e:
        print(f"❌ 检测模块导入失败: {e}")
        return False
    
    try:
        from src.core.decision import DecisionMaker
        print("✅ 决策模块导入成功")
    except Exception as e:
        print(f"❌ 决策模块导入失败: {e}")
        return False
    
    try:
        from src.core.state_builder import StateBuilder
        print("✅ 状态构建模块导入成功")
    except Exception as e:
        print(f"❌ 状态构建模块导入失败: {e}")
        return False
    
    return True

def test_config():
    """测试配置文件"""
    print("\n🔍 测试配置文件...")
    
    config_path = "src/config/config.json"
    if os.path.exists(config_path):
        print("✅ 配置文件存在")
        try:
            import json
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            print("✅ 配置文件格式正确")
            return True
        except Exception as e:
            print(f"❌ 配置文件格式错误: {e}")
            return False
    else:
        print("❌ 配置文件不存在")
        return False

def test_model():
    """测试模型文件"""
    print("\n🔍 测试模型文件...")
    
    model_path = "best.pt"
    if os.path.exists(model_path):
        print("✅ 模型文件存在")
        return True
    else:
        print("❌ 模型文件不存在 (这是正常的，如果您还没有训练模型)")
        return False

def test_basic_functionality():
    """测试基础功能"""
    print("\n🔍 测试基础功能...")
    
    try:
        from src.core.decision import DecisionMaker
        
        # 测试决策模块
        decision_maker = DecisionMaker()
        test_state = {
            'hand': [(1, 0), (2, 0), (3, 0)],
            'legal_actions': ['play', 'pass']
        }
        
        action, confidence = decision_maker.make_decision(test_state)
        win_rate = decision_maker.get_win_rate(test_state)
        
        print(f"✅ 决策模块测试成功 - 动作: {action}, 置信度: {confidence:.2f}, 胜率: {win_rate:.2f}")
        return True
        
    except Exception as e:
        print(f"❌ 基础功能测试失败: {e}")
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🚀 开始快速系统验证...\n")
    
    results = []
    
    # 运行各项测试
    results.append(test_imports())
    results.append(test_config())
    results.append(test_model())
    results.append(test_basic_functionality())
    
    # 总结结果
    print("\n" + "="*50)
    print("📊 验证结果总结:")
    
    passed = sum(results)
    total = len(results)
    
    if passed == total:
        print(f"🎉 所有测试通过! ({passed}/{total})")
        print("\n✅ 系统基础功能正常，可以继续开发!")
    elif passed >= total - 1:  # 允许模型文件不存在
        print(f"⚠️  大部分测试通过 ({passed}/{total})")
        print("\n✅ 系统基本可用，建议先获取模型文件")
    else:
        print(f"❌ 多项测试失败 ({passed}/{total})")
        print("\n🔧 需要修复基础问题后再继续开发")
    
    print("\n📋 下一步建议:")
    if not os.path.exists("best.pt"):
        print("1. 获取或训练YOLO模型文件 (best.pt)")
    print("2. 运行端到端测试: python tests/e2e/test_end_to_end.py")
    print("3. 开始改进决策模块")

if __name__ == "__main__":
    main()
