#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
AnyLabeling格式验证器

验证生成的增强标注文件是否符合AnyLabeling格式要求，
并提供格式兼容性检查和修复建议。
"""

import os
import json
import cv2
from pathlib import Path
from typing import Dict, List, Any, Tuple


class AnyLabelingFormatValidator:
    """AnyLabeling格式验证器"""
    
    def __init__(self, enhanced_dataset_path: str):
        """
        初始化验证器
        
        Args:
            enhanced_dataset_path: 增强数据集路径
        """
        self.dataset_path = enhanced_dataset_path
        self.images_dir = os.path.join(enhanced_dataset_path, "images")
        self.labels_dir = os.path.join(enhanced_dataset_path, "labels")
        
        self.validation_results = {
            'total_files': 0,
            'valid_files': 0,
            'invalid_files': 0,
            'errors': [],
            'warnings': []
        }
        
        print(f"🔍 AnyLabeling格式验证器初始化")
        print(f"   - 数据集路径: {enhanced_dataset_path}")
    
    def validate_all_files(self) -> Dict[str, Any]:
        """验证所有文件"""
        print(f"🚀 开始验证AnyLabeling格式...")
        
        # 获取所有JSON文件
        json_files = [f for f in os.listdir(self.labels_dir) if f.endswith('.json')]
        self.validation_results['total_files'] = len(json_files)
        
        print(f"📊 找到 {len(json_files)} 个JSON文件")
        
        # 验证每个文件
        for i, json_file in enumerate(json_files):
            try:
                self._validate_single_file(json_file)
                self.validation_results['valid_files'] += 1
            except Exception as e:
                self.validation_results['invalid_files'] += 1
                self.validation_results['errors'].append({
                    'file': json_file,
                    'error': str(e)
                })
                print(f"❌ 验证失败: {json_file} - {e}")
            
            # 显示进度
            if (i + 1) % 50 == 0:
                progress = (i + 1) / len(json_files) * 100
                print(f"   进度: {i+1}/{len(json_files)} ({progress:.1f}%)")
        
        # 生成验证报告
        report = self._generate_validation_report()
        
        print(f"✅ 验证完成")
        return report
    
    def _validate_single_file(self, json_file: str) -> None:
        """验证单个JSON文件"""
        json_path = os.path.join(self.labels_dir, json_file)
        
        # 读取JSON文件
        with open(json_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # 验证必需字段
        self._validate_required_fields(data, json_file)
        
        # 验证shapes格式
        self._validate_shapes_format(data.get('shapes', []), json_file)
        
        # 验证图像文件存在性
        self._validate_image_exists(data, json_file)
        
        # 验证坐标合理性
        self._validate_coordinates(data, json_file)
    
    def _validate_required_fields(self, data: Dict[str, Any], filename: str) -> None:
        """验证必需字段"""
        required_fields = ['version', 'flags', 'shapes']
        
        for field in required_fields:
            if field not in data:
                raise ValueError(f"缺少必需字段: {field}")
        
        # 验证version格式
        if not isinstance(data['version'], str):
            raise ValueError("version字段必须是字符串")
        
        # 验证shapes是列表
        if not isinstance(data['shapes'], list):
            raise ValueError("shapes字段必须是列表")
    
    def _validate_shapes_format(self, shapes: List[Dict[str, Any]], filename: str) -> None:
        """验证shapes格式"""
        for i, shape in enumerate(shapes):
            # 验证shape必需字段
            required_shape_fields = [
                'label', 'points', 'group_id', 'shape_type'
            ]
            
            for field in required_shape_fields:
                if field not in shape:
                    raise ValueError(f"Shape {i} 缺少必需字段: {field}")
            
            # 验证points格式
            points = shape.get('points', [])
            if not isinstance(points, list) or len(points) != 4:
                raise ValueError(f"Shape {i} points格式错误，应为4个点的列表")
            
            for j, point in enumerate(points):
                if not isinstance(point, list) or len(point) != 2:
                    raise ValueError(f"Shape {i} point {j} 格式错误，应为[x, y]")
                
                if not all(isinstance(coord, (int, float)) for coord in point):
                    raise ValueError(f"Shape {i} point {j} 坐标必须是数字")
            
            # 验证shape_type
            if shape.get('shape_type') != 'rectangle':
                self.validation_results['warnings'].append({
                    'file': filename,
                    'warning': f"Shape {i} shape_type不是rectangle: {shape.get('shape_type')}"
                })
    
    def _validate_image_exists(self, data: Dict[str, Any], filename: str) -> None:
        """验证对应的图像文件是否存在"""
        image_path_field = data.get('imagePath')
        if image_path_field:
            image_path = os.path.join(self.images_dir, image_path_field)
            if not os.path.exists(image_path):
                raise ValueError(f"对应的图像文件不存在: {image_path_field}")
        else:
            # 根据JSON文件名推断图像文件名
            base_name = Path(filename).stem
            possible_extensions = ['.jpg', '.jpeg', '.png']
            
            image_found = False
            for ext in possible_extensions:
                image_path = os.path.join(self.images_dir, f"{base_name}{ext}")
                if os.path.exists(image_path):
                    image_found = True
                    break
            
            if not image_found:
                raise ValueError(f"找不到对应的图像文件: {base_name}")
    
    def _validate_coordinates(self, data: Dict[str, Any], filename: str) -> None:
        """验证坐标合理性"""
        # 尝试获取图像尺寸
        image_width = data.get('imageWidth')
        image_height = data.get('imageHeight')
        
        if not image_width or not image_height:
            # 从图像文件获取尺寸
            image_path_field = data.get('imagePath')
            if image_path_field:
                image_path = os.path.join(self.images_dir, image_path_field)
            else:
                base_name = Path(filename).stem
                image_path = os.path.join(self.images_dir, f"{base_name}.jpg")
            
            if os.path.exists(image_path):
                image = cv2.imread(image_path)
                if image is not None:
                    image_height, image_width = image.shape[:2]
        
        if image_width and image_height:
            # 验证所有坐标是否在图像范围内
            for i, shape in enumerate(data.get('shapes', [])):
                points = shape.get('points', [])
                for j, point in enumerate(points):
                    x, y = point
                    if x < 0 or x > image_width or y < 0 or y > image_height:
                        self.validation_results['warnings'].append({
                            'file': filename,
                            'warning': f"Shape {i} point {j} 坐标超出图像范围: ({x}, {y})"
                        })
    
    def _generate_validation_report(self) -> Dict[str, Any]:
        """生成验证报告"""
        success_rate = (self.validation_results['valid_files'] / 
                       self.validation_results['total_files'] 
                       if self.validation_results['total_files'] > 0 else 0)
        
        report = {
            'validation_summary': {
                'total_files': self.validation_results['total_files'],
                'valid_files': self.validation_results['valid_files'],
                'invalid_files': self.validation_results['invalid_files'],
                'success_rate': success_rate,
                'anylabeling_compatible': success_rate > 0.95
            },
            'errors': self.validation_results['errors'],
            'warnings': self.validation_results['warnings'],
            'recommendations': self._generate_recommendations()
        }
        
        # 保存验证报告
        report_path = os.path.join(self.dataset_path, "anylabeling_validation_report.json")
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        return report
    
    def _generate_recommendations(self) -> List[str]:
        """生成建议"""
        recommendations = []
        
        if self.validation_results['invalid_files'] > 0:
            recommendations.append("存在格式错误的文件，建议检查并修复")
        
        if len(self.validation_results['warnings']) > 0:
            recommendations.append("存在警告信息，建议检查坐标和字段格式")
        
        if self.validation_results['valid_files'] == self.validation_results['total_files']:
            recommendations.append("所有文件格式正确，可以在AnyLabeling中正常打开")
        
        return recommendations
    
    def fix_common_issues(self) -> Dict[str, Any]:
        """修复常见问题"""
        print(f"🔧 开始修复常见格式问题...")
        
        fixed_files = 0
        json_files = [f for f in os.listdir(self.labels_dir) if f.endswith('.json')]
        
        for json_file in json_files:
            json_path = os.path.join(self.labels_dir, json_file)
            
            try:
                with open(json_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                modified = False
                
                # 修复缺失的imagePath
                if 'imagePath' not in data:
                    base_name = Path(json_file).stem
                    data['imagePath'] = f"{base_name}.jpg"
                    modified = True
                
                # 修复缺失的imageData
                if 'imageData' not in data:
                    data['imageData'] = None
                    modified = True
                
                # 修复缺失的图像尺寸
                if 'imageWidth' not in data or 'imageHeight' not in data:
                    image_path = os.path.join(self.images_dir, data.get('imagePath', f"{Path(json_file).stem}.jpg"))
                    if os.path.exists(image_path):
                        image = cv2.imread(image_path)
                        if image is not None:
                            height, width = image.shape[:2]
                            data['imageHeight'] = height
                            data['imageWidth'] = width
                            modified = True
                
                # 保存修改后的文件
                if modified:
                    with open(json_path, 'w', encoding='utf-8') as f:
                        json.dump(data, f, ensure_ascii=False, indent=2)
                    fixed_files += 1
                
            except Exception as e:
                print(f"❌ 修复文件失败: {json_file} - {e}")
        
        print(f"✅ 修复完成，共修复 {fixed_files} 个文件")
        
        return {
            'fixed_files': fixed_files,
            'total_files': len(json_files)
        }


def main():
    """主函数"""
    print("🔍 AnyLabeling格式验证器")
    print("=" * 50)
    
    # 创建验证器
    validator = AnyLabelingFormatValidator(
        "legacy_assets/ceshi/calibration_gt_enhanced"
    )
    
    # 修复常见问题
    fix_result = validator.fix_common_issues()
    
    # 验证所有文件
    report = validator.validate_all_files()
    
    # 打印结果
    print("\n📊 验证结果汇总:")
    print(f"   总文件数: {report['validation_summary']['total_files']}")
    print(f"   有效文件: {report['validation_summary']['valid_files']}")
    print(f"   无效文件: {report['validation_summary']['invalid_files']}")
    print(f"   成功率: {report['validation_summary']['success_rate']:.1%}")
    print(f"   AnyLabeling兼容: {'✅' if report['validation_summary']['anylabeling_compatible'] else '❌'}")
    
    if report['errors']:
        print(f"\n❌ 错误 ({len(report['errors'])}个):")
        for error in report['errors'][:5]:  # 只显示前5个错误
            print(f"   - {error['file']}: {error['error']}")
        if len(report['errors']) > 5:
            print(f"   ... 还有 {len(report['errors']) - 5} 个错误")
    
    if report['warnings']:
        print(f"\n⚠️ 警告 ({len(report['warnings'])}个):")
        for warning in report['warnings'][:5]:  # 只显示前5个警告
            print(f"   - {warning['file']}: {warning['warning']}")
        if len(report['warnings']) > 5:
            print(f"   ... 还有 {len(report['warnings']) - 5} 个警告")
    
    print(f"\n💡 建议:")
    for rec in report['recommendations']:
        print(f"   - {rec}")
    
    print(f"\n🔧 修复结果:")
    print(f"   修复文件: {fix_result['fixed_files']}/{fix_result['total_files']}")


if __name__ == "__main__":
    main()
