#!/usr/bin/env python3
"""
验证位置匹配修复效果

检查frame_00256→frame_00257的处理结果，验证：
1. 区域9的位置对应关系匹配是否正确
2. 新老卡牌识别是否准确
3. 是否解决了"叁"继承"2五"ID的问题
"""

import json
import os
from pathlib import Path

def load_frame_data(frame_name):
    """加载帧数据"""
    labels_dir = Path("output/calibration_gt_final_with_digital_twin/labels")
    file_path = labels_dir / f"{frame_name}.json"
    
    if not file_path.exists():
        print(f"❌ 文件不存在: {file_path}")
        return None
    
    with open(file_path, 'r', encoding='utf-8') as f:
        return json.load(f)

def extract_region_cards(data, region_id):
    """提取指定区域的卡牌"""
    if not data or 'shapes' not in data:
        return []
    
    region_cards = []
    for shape in data['shapes']:
        if shape.get('group_id') == region_id:
            card_info = {
                'label': shape.get('label', ''),
                'twin_id': shape.get('attributes', {}).get('digital_twin_id', ''),
                'points': shape.get('points', []),
                'x_center': 0,
                'y_center': 0
            }
            
            # 计算中心点
            if card_info['points']:
                x_coords = [p[0] for p in card_info['points']]
                y_coords = [p[1] for p in card_info['points']]
                card_info['x_center'] = sum(x_coords) / len(x_coords)
                card_info['y_center'] = sum(y_coords) / len(y_coords)
            
            region_cards.append(card_info)
    
    return region_cards

def calculate_distance(card1, card2):
    """计算两张卡牌的距离"""
    dx = card1['x_center'] - card2['x_center']
    dy = card1['y_center'] - card2['y_center']
    return (dx * dx + dy * dy) ** 0.5

def analyze_position_matching(prev_cards, curr_cards):
    """分析位置匹配情况"""
    print(f"\n🔍 位置匹配分析:")
    print(f"前一帧: {len(prev_cards)}张卡牌")
    print(f"当前帧: {len(curr_cards)}张卡牌")
    
    if not prev_cards or not curr_cards:
        print("❌ 无法进行位置匹配分析")
        return
    
    # 构建距离矩阵
    print(f"\n📊 距离矩阵:")
    print(f"{'当前帧':<15} {'前一帧':<15} {'距离':<10} {'匹配状态'}")
    print("-" * 60)
    
    POSITION_THRESHOLD = 15.0
    
    # 贪心匹配算法
    all_pairs = []
    for i, curr_card in enumerate(curr_cards):
        for j, prev_card in enumerate(prev_cards):
            distance = calculate_distance(curr_card, prev_card)
            all_pairs.append((distance, i, j, curr_card, prev_card))
    
    # 按距离排序
    all_pairs.sort(key=lambda x: x[0])
    
    matched_current = set()
    matched_previous = set()
    matched_pairs = []
    
    for distance, i, j, curr_card, prev_card in all_pairs:
        if i not in matched_current and j not in matched_previous:
            if distance < POSITION_THRESHOLD:
                matched_pairs.append((curr_card, prev_card, distance))
                matched_current.add(i)
                matched_previous.add(j)
                status = "✅ 位置对应"
            else:
                break
        else:
            status = "⏭️ 已匹配"
        
        print(f"{curr_card['label']:<15} {prev_card['twin_id']:<15} {distance:<10.1f} {status}")
    
    # 分析结果
    print(f"\n📋 匹配结果:")
    print(f"位置对应匹配: {len(matched_pairs)}对")
    print(f"新卡牌: {len(curr_cards) - len(matched_current)}张")
    
    # 显示匹配详情
    print(f"\n✅ 位置对应匹配详情:")
    for curr_card, prev_card, distance in matched_pairs:
        print(f"  {curr_card['label']} ← {prev_card['twin_id']} (距离: {distance:.1f}px)")
    
    # 显示新卡牌
    unmatched_current = [curr_cards[i] for i in range(len(curr_cards)) if i not in matched_current]
    if unmatched_current:
        print(f"\n🆕 新卡牌详情:")
        for card in unmatched_current:
            print(f"  {card['label']} (位置: {card['x_center']:.1f}, {card['y_center']:.1f})")

def main():
    print("🔧 验证位置匹配修复效果")
    print("=" * 50)
    
    # 加载数据
    frame_256 = load_frame_data("frame_00256")
    frame_257 = load_frame_data("frame_00257")
    
    if not frame_256 or not frame_257:
        print("❌ 无法加载帧数据")
        return
    
    # 分析区域9
    print(f"\n📍 区域9（对战方弃牌区）分析:")
    prev_region9 = extract_region_cards(frame_256, 9)
    curr_region9 = extract_region_cards(frame_257, 9)
    
    print(f"\nframe_00256 区域9:")
    for i, card in enumerate(prev_region9):
        print(f"  [{i}] {card['twin_id']} (位置: {card['x_center']:.1f}, {card['y_center']:.1f})")
    
    print(f"\nframe_00257 区域9:")
    for i, card in enumerate(curr_region9):
        print(f"  [{i}] {card['label']} → {card['twin_id']} (位置: {card['x_center']:.1f}, {card['y_center']:.1f})")
    
    # 位置匹配分析
    analyze_position_matching(prev_region9, curr_region9)
    
    # 检查是否有"叁"卡牌的错误继承
    print(f"\n🔍 检查'叁'卡牌错误继承问题:")
    san_cards_in_region9 = [card for card in curr_region9 if '叁' in card['label']]
    wu_cards_with_san_id = [card for card in curr_region9 if '五' in card['twin_id'] and '叁' in card['label']]
    
    if san_cards_in_region9:
        print(f"❌ 发现区域9中有'叁'卡牌: {[card['label'] for card in san_cards_in_region9]}")
    else:
        print(f"✅ 区域9中没有'叁'卡牌")
    
    if wu_cards_with_san_id:
        print(f"❌ 发现'叁'卡牌错误继承'五'ID: {[(card['label'], card['twin_id']) for card in wu_cards_with_san_id]}")
    else:
        print(f"✅ 没有发现'叁'卡牌错误继承'五'ID的问题")
    
    # 分析区域5
    print(f"\n📍 区域5（观战方弃牌区）分析:")
    prev_region5 = extract_region_cards(frame_256, 5)
    curr_region5 = extract_region_cards(frame_257, 5)
    
    print(f"\nframe_00256 区域5: {len(prev_region5)}张卡牌")
    print(f"frame_00257 区域5: {len(curr_region5)}张卡牌")
    
    if prev_region5 and curr_region5:
        analyze_position_matching(prev_region5, curr_region5)
    
    print(f"\n🎉 验证完成!")

if __name__ == "__main__":
    main()
