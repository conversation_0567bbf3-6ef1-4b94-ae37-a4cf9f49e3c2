"""
游戏边界检测器 (GameBoundaryDetector)
专门负责检测单局边界，防止跨局数据污染

设计目标：
1. 精确检测小结算画面（仅限：你赢了、你输了、荒庄）
2. 严格按照设计要求，禁止混合其他策略
3. 与主控器无缝集成
"""

from typing import List, Dict, Any, Optional
from dataclasses import dataclass
from enum import Enum
import logging

logger = logging.getLogger(__name__)

class BoundaryType(Enum):
    """边界类型枚举"""
    SETTLEMENT = "settlement"        # 小结算画面（你赢了、你输了、荒庄）
    NONE = "none"                   # 无边界

@dataclass
class BoundaryDetectionResult:
    """边界检测结果"""
    boundary_type: BoundaryType
    confidence: float
    trigger_labels: List[str]
    should_reset: bool
    detection_method: str
    frame_context: Dict[str, Any]

class GameBoundaryDetector:
    """游戏边界检测器"""
    
    def __init__(self):
        # 🎯 核心小结算画面标签（严格按设计要求）
        self.settlement_labels = {
            '你赢了', '你输了', '荒庄'           # 仅限这三个关键标签
        }

        # 🆕 状态管理：实现"下一帧重置"逻辑
        self.previous_frame_had_settlement = False
        self.should_reset_next_frame = False

        # 检测统计
        self.detection_stats = {
            "total_detections": 0,
            "settlement_detected": 0,
            "false_positives": 0
        }

        logger.info("游戏边界检测器初始化完成 - 仅检测核心小结算标签")
    
    def detect_boundary(self, detections: List[Dict[str, Any]],
                       frame_context: Optional[Dict[str, Any]] = None) -> BoundaryDetectionResult:
        """
        检测游戏边界 - 仅检测核心小结算标签

        Args:
            detections: 检测结果列表
            frame_context: 帧上下文信息（未使用，保持接口兼容）

        Returns:
            边界检测结果
        """
        self.detection_stats["total_detections"] += 1

        # 🔧 步骤1：检查是否应该重置（基于上一帧的小结算检测）
        if self.should_reset_next_frame:
            logger.info("🔄 基于上一帧小结算检测，当前帧执行重置")
            self.should_reset_next_frame = False  # 重置标志
            return BoundaryDetectionResult(
                boundary_type=BoundaryType.SETTLEMENT,
                confidence=1.0,
                trigger_labels=["上一帧小结算触发"],
                should_reset=True,
                detection_method="next_frame_reset",
                frame_context={}
            )

        # 🔧 步骤2：检测当前帧是否有小结算标签
        all_labels = [d.get('label', '') for d in detections]
        current_frame_has_settlement = self._has_settlement_labels(all_labels)

        if current_frame_has_settlement:
            settlement_labels = [label for label in all_labels if any(s in label for s in self.settlement_labels)]
            logger.info(f"🎯 检测到小结算标签: {settlement_labels}，下一帧将重置")
            self.should_reset_next_frame = True  # 标记下一帧需要重置
            self.detection_stats["settlement_detected"] += 1

        # 当前帧不重置，只是标记下一帧重置
        return BoundaryDetectionResult(
            boundary_type=BoundaryType.NONE,
            confidence=0.0,
            trigger_labels=[],
            should_reset=False,
            detection_method="no_boundary_current_frame",
            frame_context=frame_context or {}
        )
    
    def _has_settlement_labels(self, all_labels: List[str]) -> bool:
        """检查是否包含小结算标签"""
        for label in all_labels:
            for settlement_label in self.settlement_labels:
                if settlement_label in label:
                    return True
        return False

    def _detect_settlement_boundary(self, all_labels: List[str]) -> BoundaryDetectionResult:
        """检测小结算边界 - 仅检测核心标签"""

        # 🎯 仅检测核心小结算标签：你赢了、你输了、荒庄
        settlement_triggers = []
        for label in all_labels:
            for settlement_label in self.settlement_labels:
                if settlement_label in label:
                    settlement_triggers.append(label)

        if settlement_triggers:
            return BoundaryDetectionResult(
                boundary_type=BoundaryType.SETTLEMENT,
                confidence=1.0,
                trigger_labels=settlement_triggers,
                should_reset=True,
                detection_method="core_settlement_labels",
                frame_context={}
            )

        # 无小结算标签
        return BoundaryDetectionResult(
            boundary_type=BoundaryType.NONE,
            confidence=0.0,
            trigger_labels=[],
            should_reset=False,
            detection_method="no_settlement",
            frame_context={}
        )



    def should_reset_system(self, boundary_result: BoundaryDetectionResult) -> bool:
        """判断是否应该重置系统"""
        return boundary_result.should_reset

    def get_detection_stats(self) -> Dict[str, Any]:
        """获取检测统计信息"""
        return self.detection_stats.copy()

    def reset_stats(self):
        """重置统计信息"""
        self.detection_stats = {
            "total_detections": 0,
            "settlement_detected": 0,
            "false_positives": 0
        }
        logger.info("边界检测统计信息已重置")

def create_game_boundary_detector() -> GameBoundaryDetector:
    """创建游戏边界检测器实例"""
    return GameBoundaryDetector()
