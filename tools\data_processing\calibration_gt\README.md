# 📊 Calibration GT 数据处理工具

专用于处理calibration_gt数据集的工具集合。

## 🎯 工具概述

### final_processor.py (推荐)
- **功能**: calibration_gt数据的最终版本处理器
- **特点**: 集成所有优化功能，生产就绪
- **输出**: 双格式标注文件 (RLCard + AnyLabeling)
- **成功率**: 91.37% (339/371帧)

### digital_twin_generator.py
- **功能**: 为calibration_gt生成数字孪生ID标注
- **特点**: 完整的数字孪生信息，支持人工审核
- **输出**: 带数字孪生ID的标注文件
- **处理量**: 12,163张卡牌，11,057个数字孪生ID

### dual_format_generator.py
- **功能**: 生成双格式输出的专用工具
- **特点**: 同步生成RLCard和AnyLabeling格式
- **输出**: 100%一致性的双格式文件
- **应用**: 训练集扩展、人工审核

## 🚀 使用指南

### 基本使用
```bash
# 使用最终处理器 (推荐)
python tools/data_processing/calibration_gt/final_processor.py \
  --input_dir data/calibration_gt/images \
  --output_dir output/calibration_gt_processed

# 生成数字孪生ID
python tools/data_processing/calibration_gt/digital_twin_generator.py \
  --input_dir data/calibration_gt \
  --output_dir output/digital_twin_output

# 双格式生成
python tools/data_processing/calibration_gt/dual_format_generator.py \
  --input_file single_image.jpg \
  --output_dir output/dual_format
```

### 高级配置
```bash
# 自定义参数
python tools/data_processing/calibration_gt/final_processor.py \
  --input_dir data/calibration_gt/images \
  --output_dir output/processed \
  --confidence_threshold 0.25 \
  --iou_threshold 0.45 \
  --enable_gpu \
  --batch_size 8
```

## 📊 处理结果

### 性能指标
- **处理成功率**: 91.37%
- **数字孪生ID分配率**: 90.91%
- **双格式一致性**: 100%
- **坐标保留**: 100%原始坐标

### 输出格式
```json
{
  "rlcard_format": {
    "hand": [[2, "二", "1_二", 0.95]],
    "metadata": {...}
  },
  "anylabeling_format": {
    "shapes": [{
      "label": "1二",
      "points": [[x1,y1], [x2,y2], ...],
      "shape_type": "polygon"
    }]
  }
}
```

## 🔧 故障排除

### 常见问题
1. **内存不足**: 减少batch_size或使用CPU模式
2. **GPU不可用**: 添加 `--disable_gpu` 参数
3. **文件权限**: 确保输出目录有写权限
4. **依赖缺失**: 运行 `pip install -r requirements.txt`

### 错误处理
- **处理失败的帧**: 自动保存到failed_frames/目录
- **错误日志**: 详细记录在processing.log中
- **恢复机制**: 支持断点续传处理

## 📋 版本历史

- **final_processor.py**: 最新稳定版本，推荐使用
- **perfect_processor.py**: 坐标修复版本 (已整合)
- **complete_processor.py**: 完整性改进版本 (已整合)
- **simple_processor.py**: 基础版本 (已废弃)

---

**🎯 建议**: 日常使用推荐 `final_processor.py`，它集成了所有优化功能。
