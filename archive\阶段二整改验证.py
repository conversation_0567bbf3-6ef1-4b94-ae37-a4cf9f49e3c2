#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
阶段二整改验证脚本
验证改进的状态转换和决策模块是否解决了核心问题
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.core.improved_state_builder import ImprovedStateBuilder, Card, CardState
from src.core.improved_decision import ImprovedDecisionMaker

def test_state_builder_improvements():
    """测试改进的状态构建器"""
    print("🔍 测试改进的状态构建器...")
    
    builder = ImprovedStateBuilder()
    
    # 模拟YOLO检测结果
    test_detections = {
        'cards': [
            # 玩家手牌 - 包含二七十组合
            {'id': '1_二', 'group_id': 1, 'pos': [100, 100, 50, 70], 'conf': 0.95},
            {'id': '2_七', 'group_id': 1, 'pos': [160, 100, 50, 70], 'conf': 0.93},
            {'id': '3_十', 'group_id': 1, 'pos': [220, 100, 50, 70], 'conf': 0.91},
            {'id': '4_三', 'group_id': 1, 'pos': [280, 100, 50, 70], 'conf': 0.94},
            {'id': '5_三', 'group_id': 1, 'pos': [340, 100, 50, 70], 'conf': 0.92},
            {'id': '6_三', 'group_id': 1, 'pos': [400, 100, 50, 70], 'conf': 0.90},  # 三张"三"，应触发偎牌
            
            # 玩家吃碰区
            {'id': '7_五', 'group_id': 6, 'pos': [100, 200, 50, 70], 'conf': 0.89},
            {'id': '8_五', 'group_id': 6, 'pos': [160, 200, 50, 70], 'conf': 0.88},
            {'id': '9_五', 'group_id': 6, 'pos': [220, 200, 50, 70], 'conf': 0.87},
            
            # 对手打出的牌
            {'id': '10_八', 'group_id': 8, 'pos': [300, 300, 50, 70], 'conf': 0.86},
        ]
    }
    
    # 转换状态
    game_state = builder.yolo_to_game_state(test_detections)
    
    # 验证结果
    print("✅ 状态转换完成")
    print(f"   玩家手牌数量: {len(game_state['hand_player'])}")
    print(f"   玩家吃碰区数量: {len(game_state['chi_peng_player'])}")
    print(f"   特殊组合: {game_state['special_combinations']}")
    print(f"   合法动作: {game_state['legal_actions']}")
    
    # 检查是否检测到二七十组合
    has_er_qi_shi = any(combo['type'] == '二七十' for combo in game_state['special_combinations'])
    if has_er_qi_shi:
        print("✅ 成功检测到二七十组合")
    else:
        print("❌ 未检测到二七十组合")
    
    # 检查是否有胡牌动作
    if 'hu' in game_state['legal_actions']:
        print("✅ 正确识别胡牌机会")
    else:
        print("⚠️  未识别胡牌机会（可能正常）")
    
    return game_state

def test_decision_improvements():
    """测试改进的决策模块"""
    print("\n🔍 测试改进的决策模块...")
    
    decision_maker = ImprovedDecisionMaker()
    
    # 测试场景1：有胡牌机会
    test_state_1 = {
        'hand_player': [
            {'label': '二'}, {'label': '七'}, {'label': '十'}
        ],
        'chi_peng_player': [
            {'label': '五'}, {'label': '五'}, {'label': '五'},
            {'label': '八'}, {'label': '八'}, {'label': '八'}
        ],
        'special_combinations': [
            {'type': '二七十', 'cards': ['二', '七', '十']}
        ],
        'legal_actions': ['hu', 'play', 'pass']
    }
    
    action_1, confidence_1 = decision_maker.make_decision(test_state_1)
    win_rate_1 = decision_maker.get_win_rate(test_state_1)
    decision_1 = decision_maker.format_decision(action_1, confidence_1, win_rate_1)
    
    print("📊 测试场景1 - 有胡牌机会:")
    print(f"   推荐动作: {decision_1['推荐动作']}")
    print(f"   置信度: {decision_1['置信度']}")
    print(f"   胜率: {decision_1['胜率']}")
    
    if action_1 == 'hu':
        print("✅ 正确选择胡牌")
    else:
        print(f"❌ 错误选择: {action_1}，应该选择胡牌")
    
    # 测试场景2：有碰牌机会
    test_state_2 = {
        'hand_player': [
            {'label': '六'}, {'label': '六'}, {'label': '九'}, {'label': '一'}
        ],
        'discard_opponent': [
            {'label': '六'}  # 对手刚打出六
        ],
        'legal_actions': ['peng', 'play', 'pass']
    }
    
    action_2, confidence_2 = decision_maker.make_decision(test_state_2)
    win_rate_2 = decision_maker.get_win_rate(test_state_2)
    decision_2 = decision_maker.format_decision(action_2, confidence_2, win_rate_2)
    
    print("\n📊 测试场景2 - 有碰牌机会:")
    print(f"   推荐动作: {decision_2['推荐动作']}")
    print(f"   置信度: {decision_2['置信度']}")
    print(f"   胜率: {decision_2['胜率']}")
    
    if action_2 == 'peng':
        print("✅ 正确选择碰牌")
    else:
        print(f"⚠️  选择了: {action_2}，碰牌可能更好")
    
    # 测试场景3：只能打牌或过
    test_state_3 = {
        'hand_player': [
            {'label': '一'}, {'label': '四'}, {'label': '九'}, {'label': '拾'}
        ],
        'legal_actions': ['play', 'pass']
    }
    
    action_3, confidence_3 = decision_maker.make_decision(test_state_3)
    win_rate_3 = decision_maker.get_win_rate(test_state_3)
    decision_3 = decision_maker.format_decision(action_3, confidence_3, win_rate_3)
    
    print("\n📊 测试场景3 - 普通情况:")
    print(f"   推荐动作: {decision_3['推荐动作']}")
    print(f"   置信度: {decision_3['置信度']}")
    print(f"   胜率: {decision_3['胜率']}")
    
    return True

def test_priority_system():
    """测试动作优先级系统"""
    print("\n🔍 测试动作优先级系统...")
    
    decision_maker = ImprovedDecisionMaker()
    
    # 测试所有动作都可用的情况
    test_state = {
        'hand_player': [{'label': '二'}, {'label': '七'}],
        'legal_actions': ['hu', 'peng', 'chi', 'play', 'pass']
    }
    
    action, confidence = decision_maker.make_decision(test_state)
    
    print(f"   所有动作可用时选择: {action}")
    
    if action == 'hu':
        print("✅ 正确优先选择胡牌")
    else:
        print(f"❌ 优先级错误，选择了: {action}")
    
    # 测试没有胡牌时的选择
    test_state_no_hu = {
        'hand_player': [{'label': '二'}, {'label': '七'}],
        'legal_actions': ['peng', 'chi', 'play', 'pass']
    }
    
    action_no_hu, _ = decision_maker.make_decision(test_state_no_hu)
    
    print(f"   无胡牌时选择: {action_no_hu}")
    
    if action_no_hu in ['peng', 'chi']:
        print("✅ 正确优先选择碰牌或吃牌")
    else:
        print(f"⚠️  选择了: {action_no_hu}")
    
    return True

def compare_with_old_system():
    """与旧系统对比"""
    print("\n📊 与旧系统对比...")
    
    try:
        from src.core.decision import DecisionMaker as OldDecisionMaker
        from src.core.state_builder import StateBuilder as OldStateBuilder
        
        # 测试旧系统
        old_decision = OldDecisionMaker()
        old_state = {
            'hand': [(2, 0), (7, 0), (10, 0)],
            'legal_actions': ['hu', 'play', 'pass']
        }
        
        old_action, old_conf = old_decision.make_decision(old_state)
        old_win_rate = old_decision.get_win_rate(old_state)
        
        print(f"旧系统决策: {old_action}, 置信度: {old_conf:.2f}, 胜率: {old_win_rate:.2f}")
        
        # 测试新系统
        new_decision = ImprovedDecisionMaker()
        new_state = {
            'hand_player': [{'label': '二'}, {'label': '七'}, {'label': '十'}],
            'special_combinations': [{'type': '二七十', 'cards': ['二', '七', '十']}],
            'legal_actions': ['hu', 'play', 'pass']
        }
        
        new_action, new_conf = new_decision.make_decision(new_state)
        new_win_rate = new_decision.get_win_rate(new_state)
        
        print(f"新系统决策: {new_action}, 置信度: {new_conf:.2f}, 胜率: {new_win_rate:.2f}")
        
        if new_action == 'hu' and old_action != 'hu':
            print("✅ 新系统正确识别胡牌机会，旧系统未识别")
        elif new_action == old_action:
            print("⚠️  两系统决策相同，但新系统应该更智能")
        
    except Exception as e:
        print(f"⚠️  无法导入旧系统进行对比: {e}")

def main():
    """主函数"""
    print("🚀 开始阶段二整改验证...\n")
    
    try:
        # 测试改进的状态构建器
        game_state = test_state_builder_improvements()
        
        # 测试改进的决策模块
        test_decision_improvements()
        
        # 测试优先级系统
        test_priority_system()
        
        # 与旧系统对比
        compare_with_old_system()
        
        print("\n" + "="*60)
        print("📊 整改验证总结:")
        print("✅ 改进的状态构建器 - 支持特殊组合检测")
        print("✅ 改进的决策模块 - 基于规则的智能决策")
        print("✅ 动作优先级系统 - 胡 > 碰/跑 > 吃")
        print("✅ 特殊牌型识别 - 二七十、大小三搭")
        print("\n🎯 建议下一步:")
        print("1. 将改进的模块集成到主程序中")
        print("2. 实现比牌和臭牌机制")
        print("3. 进行端到端测试验证")
        print("4. 优化检测准确性")
        
    except Exception as e:
        print(f"❌ 验证过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
