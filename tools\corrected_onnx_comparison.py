#!/usr/bin/env python3
"""
修正的ONNX对比测试

基于发现的输出格式差异，重新实现正确的ONNX后处理
验证用户假设：老版本ONNX在AnyLabeling中表现更好
"""

import sys
import os
import json
import cv2
import numpy as np
from pathlib import Path
from typing import List, Dict, Any, Tuple
import logging
from datetime import datetime
import time

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class CorrectedONNXComparator:
    """修正的ONNX对比器"""
    
    def __init__(self):
        self.current_onnx_path = "best.onnx"
        self.old_onnx_path = "data/processed/train3.0/weights/best.onnx"
        
        # 基于检查结果的模型信息
        self.model_info = {
            'current': {
                'output_shape': [1, 35, 8400],
                'num_classes': 30,  # 35 - 4(bbox) - 1(conf) = 30
                'class_offset': 5
            },
            'old': {
                'output_shape': [1, 26, 8400],
                'num_classes': 21,  # 26 - 4(bbox) - 1(conf) = 21
                'class_offset': 5
            }
        }
        
    def load_onnx_model(self, onnx_path: str):
        """加载ONNX模型"""
        try:
            import onnxruntime as ort
            
            providers = ['CPUExecutionProvider']
            session = ort.InferenceSession(onnx_path, providers=providers)
            
            input_info = session.get_inputs()[0]
            output_info = session.get_outputs()[0]
            
            logger.info(f"ONNX模型加载成功: {Path(onnx_path).name}")
            logger.info(f"  输出形状: {output_info.shape}")
            
            return session, input_info, output_info
            
        except Exception as e:
            logger.error(f"ONNX模型加载失败: {e}")
            return None, None, None
            
    def preprocess_image(self, image_path: str, target_size: int = 640) -> Tuple[np.ndarray, float, Tuple[int, int]]:
        """预处理图像"""
        image = cv2.imread(image_path)
        if image is None:
            raise ValueError(f"无法读取图像: {image_path}")
            
        h, w = image.shape[:2]
        scale = min(target_size / w, target_size / h)
        new_w, new_h = int(w * scale), int(h * scale)
        
        # 缩放
        resized = cv2.resize(image, (new_w, new_h))
        
        # 填充
        padded = np.full((target_size, target_size, 3), 114, dtype=np.uint8)
        top = (target_size - new_h) // 2
        left = (target_size - new_w) // 2
        padded[top:top+new_h, left:left+new_w] = resized
        
        # 转换为模型输入
        input_tensor = padded.transpose(2, 0, 1).astype(np.float32) / 255.0
        input_tensor = np.expand_dims(input_tensor, axis=0)
        
        return input_tensor, scale, (left, top)
        
    def postprocess_yolo_output(self, output: np.ndarray, model_type: str, scale: float, offset: Tuple[int, int],
                               conf_threshold: float = 0.25, iou_threshold: float = 0.45) -> List[Dict]:
        """正确的YOLO输出后处理"""
        detections = []
        
        # 获取模型信息
        model_info = self.model_info[model_type]
        num_classes = model_info['num_classes']
        
        # 输出格式: [batch, attributes, detections]
        # 需要转置为: [batch, detections, attributes]
        if output.shape[1] != 8400:  # 如果第二维不是检测数
            output = output.transpose(0, 2, 1)
            
        output = output[0]  # 移除batch维度: [detections, attributes]
        
        # 解析输出
        boxes = output[:, :4]  # [x_center, y_center, width, height]
        confidences = output[:, 4]  # 置信度
        class_probs = output[:, 5:5+num_classes]  # 类别概率
        
        # 过滤低置信度
        valid_indices = confidences > conf_threshold
        if not np.any(valid_indices):
            return detections
            
        boxes = boxes[valid_indices]
        confidences = confidences[valid_indices]
        class_probs = class_probs[valid_indices]
        
        # 获取最高概率类别
        class_ids = np.argmax(class_probs, axis=1)
        class_scores = np.max(class_probs, axis=1)
        
        # 最终分数
        final_scores = confidences * class_scores
        
        # 转换边界框格式：center -> corner
        x_center, y_center, width, height = boxes[:, 0], boxes[:, 1], boxes[:, 2], boxes[:, 3]
        x1 = x_center - width / 2
        y1 = y_center - height / 2
        x2 = x_center + width / 2
        y2 = y_center + height / 2
        
        corner_boxes = np.column_stack([x1, y1, x2, y2])
        
        # NMS
        indices = cv2.dnn.NMSBoxes(
            corner_boxes.tolist(),
            final_scores.tolist(),
            conf_threshold,
            iou_threshold
        )
        
        if len(indices) > 0:
            for i in indices.flatten():
                box = corner_boxes[i]
                score = final_scores[i]
                class_id = class_ids[i]
                
                # 转换回原图坐标
                x1 = (box[0] - offset[0]) / scale
                y1 = (box[1] - offset[1]) / scale
                x2 = (box[2] - offset[0]) / scale
                y2 = (box[3] - offset[1]) / scale
                
                detection = {
                    'bbox': [x1, y1, x2, y2],
                    'confidence': float(score),
                    'class_id': int(class_id),
                    'class_name': f'class_{class_id}',
                    'area': (x2 - x1) * (y2 - y1)
                }
                detections.append(detection)
                
        return detections
        
    def run_onnx_inference(self, session, input_info, image_path: str, model_type: str, 
                          conf_threshold: float = 0.25) -> Tuple[List[Dict], float]:
        """运行ONNX推理"""
        start_time = time.time()
        
        try:
            # 预处理
            input_tensor, scale, offset = self.preprocess_image(image_path)
            
            # 推理
            outputs = session.run(None, {input_info.name: input_tensor})
            
            # 后处理
            detections = self.postprocess_yolo_output(
                outputs[0], model_type, scale, offset, conf_threshold
            )
            
            inference_time = time.time() - start_time
            return detections, inference_time
            
        except Exception as e:
            logger.error(f"ONNX推理失败: {e}")
            return [], 0.0
            
    def compare_models_corrected(self, test_images: List[str]) -> Dict:
        """修正的模型对比"""
        logger.info("开始修正的ONNX模型对比...")
        
        # 加载模型
        current_session, current_input, _ = self.load_onnx_model(self.current_onnx_path)
        old_session, old_input, _ = self.load_onnx_model(self.old_onnx_path)
        
        if not current_session or not old_session:
            logger.error("模型加载失败")
            return {}
            
        # 测试配置
        test_configs = [
            {'conf': 0.25, 'name': 'AnyLabeling默认'},
            {'conf': 0.2, 'name': '低置信度'},
            {'conf': 0.3, 'name': '高置信度'},
        ]
        
        comparison_results = {}
        
        for config in test_configs:
            logger.info(f"测试配置: {config['name']} (conf={config['conf']})")
            
            config_results = {
                'config': config,
                'current_results': [],
                'old_results': [],
                'comparison': {}
            }
            
            # 测试图像子集
            test_subset = test_images[:20]
            
            for i, image_path in enumerate(test_subset):
                if i % 5 == 0:
                    logger.info(f"  处理图像 {i+1}/{len(test_subset)}")
                    
                # 当前模型
                current_detections, current_time = self.run_onnx_inference(
                    current_session, current_input, image_path, 'current', config['conf']
                )
                
                # 老版本模型
                old_detections, old_time = self.run_onnx_inference(
                    old_session, old_input, image_path, 'old', config['conf']
                )
                
                config_results['current_results'].append({
                    'detection_count': len(current_detections),
                    'avg_confidence': np.mean([d['confidence'] for d in current_detections]) if current_detections else 0,
                    'inference_time': current_time
                })
                
                config_results['old_results'].append({
                    'detection_count': len(old_detections),
                    'avg_confidence': np.mean([d['confidence'] for d in old_detections]) if old_detections else 0,
                    'inference_time': old_time
                })
                
            # 计算统计
            current_stats = self.calculate_statistics(config_results['current_results'])
            old_stats = self.calculate_statistics(config_results['old_results'])
            
            config_results['comparison'] = {
                'current_model': current_stats,
                'old_model': old_stats,
                'old_detects_more': old_stats['avg_detection_count'] > current_stats['avg_detection_count'],
                'detection_ratio': old_stats['avg_detection_count'] / current_stats['avg_detection_count'] if current_stats['avg_detection_count'] > 0 else 0,
                'confidence_difference': old_stats['avg_confidence'] - current_stats['avg_confidence']
            }
            
            comparison_results[config['name']] = config_results
            
        return comparison_results
        
    def calculate_statistics(self, results: List[Dict]) -> Dict:
        """计算统计信息"""
        if not results:
            return {'avg_detection_count': 0, 'avg_confidence': 0, 'avg_inference_time': 0}
            
        return {
            'avg_detection_count': np.mean([r['detection_count'] for r in results]),
            'avg_confidence': np.mean([r['avg_confidence'] for r in results]),
            'avg_inference_time': np.mean([r['inference_time'] for r in results]),
            'total_detections': sum([r['detection_count'] for r in results])
        }
        
    def collect_test_images(self, max_images: int = 50) -> List[str]:
        """收集测试图像"""
        test_images = []
        
        calibration_path = Path("legacy_assets/ceshi/calibration_gt/images")
        if calibration_path.exists():
            for img_file in calibration_path.glob("*.jpg"):
                test_images.append(str(img_file))
                if len(test_images) >= max_images:
                    break
                    
        return test_images
        
    def run_corrected_comparison(self) -> Dict:
        """运行修正的对比"""
        logger.info("开始修正的ONNX模型对比...")
        
        # 收集测试图像
        test_images = self.collect_test_images()
        logger.info(f"收集到 {len(test_images)} 张测试图像")
        
        if not test_images:
            logger.error("未找到测试图像")
            return {}
            
        # 运行对比
        comparison_results = self.compare_models_corrected(test_images)
        
        # 分析结果
        analysis = self.analyze_corrected_results(comparison_results)
        
        # 保存结果
        self.save_corrected_results(comparison_results, analysis)
        
        return {'comparison_results': comparison_results, 'analysis': analysis}
        
    def analyze_corrected_results(self, comparison_results: Dict) -> Dict:
        """分析修正的结果"""
        analysis = {
            'user_hypothesis_verification': {},
            'key_findings': [],
            'anylabeling_simulation': {}
        }
        
        # 分析AnyLabeling默认配置
        anylabeling_results = comparison_results.get('AnyLabeling默认')
        if anylabeling_results:
            comparison = anylabeling_results['comparison']
            
            old_better = comparison['old_detects_more']
            detection_ratio = comparison['detection_ratio']
            
            analysis['user_hypothesis_verification'] = {
                'old_model_detects_more': old_better,
                'detection_ratio': detection_ratio,
                'hypothesis_supported': old_better and detection_ratio > 1.1
            }
            
            if analysis['user_hypothesis_verification']['hypothesis_supported']:
                analysis['key_findings'].append("✅ 用户假设得到验证：老版本ONNX模型检测能力更强")
                analysis['key_findings'].append(f"老版本检测数量是当前版本的 {detection_ratio:.2f} 倍")
            else:
                analysis['key_findings'].append("❌ 用户假设未得到完全验证")
                
        return analysis
        
    def save_corrected_results(self, comparison_results: Dict, analysis: Dict):
        """保存修正的结果"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 保存JSON
        json_file = f"analysis/corrected_onnx_comparison_{timestamp}.json"
        os.makedirs(os.path.dirname(json_file), exist_ok=True)
        
        results = {
            'comparison_results': comparison_results,
            'analysis': analysis,
            'model_info': self.model_info
        }
        
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2, default=str)
            
        # 生成报告
        report = self.generate_corrected_report(comparison_results, analysis)
        report_file = f"analysis/corrected_onnx_comparison_report_{timestamp}.md"
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report)
            
        logger.info(f"修正的对比结果已保存:")
        logger.info(f"  - 详细数据: {json_file}")
        logger.info(f"  - 对比报告: {report_file}")
        
    def generate_corrected_report(self, comparison_results: Dict, analysis: Dict) -> str:
        """生成修正的报告"""
        report = []
        report.append("# 修正的ONNX模型对比报告\n")
        report.append(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        
        # 模型信息
        report.append("## 📋 模型信息差异")
        report.append("| 模型 | 输出形状 | 类别数 | PyTorch版本 |")
        report.append("|------|----------|--------|-------------|")
        report.append("| 当前模型 | [1, 35, 8400] | 30 | pytorch 2.9.0 |")
        report.append("| 老版本模型 | [1, 26, 8400] | 21 | pytorch 2.8.0 |")
        report.append("")
        
        # 用户假设验证
        if 'user_hypothesis_verification' in analysis:
            hyp = analysis['user_hypothesis_verification']
            status = "✅ 支持" if hyp.get('hypothesis_supported', False) else "❌ 不支持"
            
            report.append("## 🎯 用户假设验证")
            report.append(f"**验证结果**: {status}")
            report.append(f"- 老版本检测数量更多: {hyp.get('old_model_detects_more', False)}")
            report.append(f"- 检测数量比例: {hyp.get('detection_ratio', 0):.3f}")
            report.append("")
            
        # 详细对比
        report.append("## 📊 修正后的性能对比")
        for config_name, config_data in comparison_results.items():
            report.append(f"### {config_name}")
            
            comparison = config_data['comparison']
            current = comparison['current_model']
            old = comparison['old_model']
            
            report.append("| 模型 | 平均检测数 | 平均置信度 | 平均推理时间(s) |")
            report.append("|------|------------|------------|----------------|")
            report.append(f"| 当前模型 | {current['avg_detection_count']:.1f} | {current['avg_confidence']:.3f} | {current['avg_inference_time']:.4f} |")
            report.append(f"| 老版本模型 | {old['avg_detection_count']:.1f} | {old['avg_confidence']:.3f} | {old['avg_inference_time']:.4f} |")
            report.append("")
            
        # 关键发现
        if 'key_findings' in analysis:
            report.append("## 🔍 关键发现")
            for finding in analysis['key_findings']:
                report.append(f"- {finding}")
            report.append("")
            
        return "\n".join(report)

def main():
    """主函数"""
    print("🔧 修正的ONNX模型对比器")
    print("=" * 50)
    
    comparator = CorrectedONNXComparator()
    
    # 运行修正的对比
    results = comparator.run_corrected_comparison()
    
    if results:
        print("\n📊 修正的ONNX对比完成！")
        
        # 显示关键发现
        analysis = results.get('analysis', {})
        if 'key_findings' in analysis:
            for finding in analysis['key_findings']:
                print(f"   {finding}")
                
        print(f"\n✅ 修正的对比报告已生成，请查看analysis目录")
    else:
        print("❌ 修正的对比失败")

if __name__ == "__main__":
    main()
