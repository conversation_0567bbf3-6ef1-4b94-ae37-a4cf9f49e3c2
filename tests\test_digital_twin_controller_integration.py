"""
测试数字孪生主控器与卡牌尺寸启动控制器的集成
验证功能级联控制和接口一致性
"""

import unittest
from pathlib import Path
from unittest.mock import patch, MagicMock

import sys
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.core.digital_twin_controller import (
    DigitalTwinController,
    DigitalTwinConfig,
    ProcessingStrategy,
    ProcessingResult,
    create_digital_twin_controller,
    create_complete_controller,
    create_controller_with_size_control
)

from src.modules.card_size_activation_controller import (
    CardSizeActivationConfig,
    SizeBaseline
)

class TestDigitalTwinControllerIntegration(unittest.TestCase):
    """测试数字孪生主控器集成"""
    
    def setUp(self):
        """测试前准备"""
        # 创建带有尺寸控制的主控器
        self.config = DigitalTwinConfig(
            strategy=ProcessingStrategy.PHASE2_COMPLETE,
            enable_size_activation_control=True,
            size_threshold=0.85,
            qualified_ratio_threshold=0.9,
            min_card_count=20,
            enable_logging=False  # 测试时禁用日志
        )
        
        # Mock处理器初始化，避免依赖真实模块
        with patch('src.core.digital_twin_controller.create_phase1_integrator'), \
             patch('src.core.digital_twin_controller.create_phase2_integrator'):
            self.controller = DigitalTwinController(self.config)
        
        # 设置测试用的尺寸基准
        if self.controller.size_activation_controller:
            self.controller.size_activation_controller.size_baseline = SizeBaseline(
                width_median=45.0,
                height_median=60.0,
                area_median=2700.0,
                width_std=5.0,
                height_std=8.0,
                sample_count=100,
                confidence_level=0.95
            )
    
    def test_size_activation_control_enabled(self):
        """测试尺寸启动控制已启用"""
        self.assertTrue(self.config.enable_size_activation_control)
        self.assertIsNotNone(self.controller.size_activation_controller)
        
        # 验证配置传递
        size_controller = self.controller.size_activation_controller
        self.assertEqual(size_controller.config.size_threshold, 0.85)
        self.assertEqual(size_controller.config.qualified_ratio_threshold, 0.9)
        self.assertEqual(size_controller.config.min_card_count, 20)
    
    def test_process_frame_with_sufficient_good_cards(self):
        """测试处理充足且尺寸良好的卡牌"""
        # 创建20张正常尺寸的观战方手牌
        detections = []
        for i in range(20):
            detections.append({
                "group_id": 1,
                "label": "二",
                "bbox": [100 + i*50, 100, 145 + i*50, 160],  # 45x60正常尺寸
                "confidence": 0.9
            })
        
        # Mock Phase2Integrator的处理结果
        mock_result = ProcessingResult(
            success=True,
            processed_cards=[{"twin_id": f"{i+1}_二", **card} for i, card in enumerate(detections)],
            statistics={"summary": {"inheritance_rate": 0.9}},
            processing_time=0.1,
            strategy_used="phase2_complete"
        )
        
        with patch.object(self.controller.processors[ProcessingStrategy.PHASE2_COMPLETE], 
                         'process_frame', return_value=mock_result):
            result = self.controller.process_frame(detections)
        
        # 应该启动数字孪生处理
        self.assertTrue(result.success)
        self.assertEqual(len(result.processed_cards), 20)
        self.assertIn("twin_id", result.processed_cards[0])
        self.assertEqual(result.strategy_used, "phase2_complete")
        
        # 验证统计信息
        stats = self.controller.get_performance_stats()
        self.assertEqual(stats["activation_decisions"]["activated"], 1)
        self.assertEqual(stats["activation_decisions"]["deactivated"], 0)
    
    def test_process_frame_with_insufficient_cards(self):
        """测试处理卡牌数量不足的情况"""
        # 创建19张卡牌（少于20张）
        detections = []
        for i in range(19):
            detections.append({
                "group_id": 1,
                "label": "二",
                "bbox": [100 + i*50, 100, 145 + i*50, 160],
                "confidence": 0.9
            })
        
        result = self.controller.process_frame(detections)
        
        # 应该返回原始数据保留结果
        self.assertTrue(result.success)
        self.assertEqual(len(result.processed_cards), 19)
        self.assertFalse(result.statistics["digital_twin_enabled"])
        self.assertTrue(result.statistics["data_preservation_mode"])
        self.assertIn("数量不足", result.validation_warnings[0])
        self.assertEqual(result.strategy_used, "phase2_complete_passthrough")
        
        # 验证统计信息
        stats = self.controller.get_performance_stats()
        self.assertEqual(stats["activation_decisions"]["activated"], 0)
        self.assertEqual(stats["activation_decisions"]["deactivated"], 1)
    
    def test_process_frame_with_poor_size_quality(self):
        """测试处理尺寸质量不佳的情况"""
        detections = []
        
        # 创建20张卡牌，但只有一半尺寸正常
        for i in range(10):
            # 正常尺寸
            detections.append({
                "group_id": 1,
                "label": "二",
                "bbox": [100 + i*50, 100, 145 + i*50, 160],  # 45x60
                "confidence": 0.9
            })
            
            # 异常小尺寸
            detections.append({
                "group_id": 1,
                "label": "三",
                "bbox": [100 + i*50, 200, 120 + i*50, 220],  # 20x20
                "confidence": 0.9
            })
        
        result = self.controller.process_frame(detections)
        
        # 应该返回原始数据保留结果（合格率只有50%，低于90%阈值）
        self.assertTrue(result.success)
        self.assertEqual(len(result.processed_cards), 20)
        self.assertFalse(result.statistics["digital_twin_enabled"])
        self.assertTrue(result.statistics["data_preservation_mode"])
        self.assertIn("尺寸合格率", result.validation_warnings[0])
        
        # 验证启动决策信息
        activation_info = result.statistics["activation_decision"]
        self.assertFalse(activation_info["should_activate"])
        self.assertEqual(activation_info["card_count"], 20)
        self.assertLess(activation_info["qualified_ratio"], 0.9)
    
    def test_system_status_includes_size_control(self):
        """测试系统状态包含尺寸控制信息"""
        status = self.controller.get_system_status()
        
        # 验证基本状态
        self.assertIn("size_activation_control", status)
        self.assertTrue(status["size_activation_control"]["enabled"])
        
        # 验证配置信息
        size_control = status["size_activation_control"]
        self.assertEqual(size_control["size_threshold"], 0.85)
        self.assertEqual(size_control["qualified_ratio_threshold"], 0.9)
        self.assertEqual(size_control["min_card_count"], 20)
        
        # 验证统计信息
        self.assertIn("statistics", size_control)
    
    def test_disabled_size_control(self):
        """测试禁用尺寸控制的情况"""
        # 创建禁用尺寸控制的配置
        config = DigitalTwinConfig(
            strategy=ProcessingStrategy.PHASE2_COMPLETE,
            enable_size_activation_control=False
        )
        
        with patch('src.core.digital_twin_controller.create_phase1_integrator'), \
             patch('src.core.digital_twin_controller.create_phase2_integrator'):
            controller = DigitalTwinController(config)
        
        # 验证尺寸控制器未初始化
        self.assertIsNone(controller.size_activation_controller)
        
        # 验证系统状态
        status = controller.get_system_status()
        self.assertFalse(status["size_activation_control"]["enabled"])
        
        # 创建测试数据
        detections = [
            {"group_id": 1, "label": "二", "bbox": [100, 100, 120, 120]}  # 小尺寸
            for _ in range(15)  # 数量不足
        ]
        
        # Mock处理器
        mock_result = ProcessingResult(
            success=True,
            processed_cards=detections,
            statistics={},
            processing_time=0.1,
            strategy_used="phase2_complete"
        )
        
        with patch.object(controller.processors[ProcessingStrategy.PHASE2_COMPLETE], 
                         'process_frame', return_value=mock_result):
            result = controller.process_frame(detections)
        
        # 应该正常处理（不受尺寸控制影响）
        self.assertTrue(result.success)
        self.assertEqual(result.strategy_used, "phase2_complete")
    
    def test_interface_consistency(self):
        """测试接口一致性"""
        # 创建不同配置的控制器
        controllers = [
            create_complete_controller(),  # 默认配置
            create_controller_with_size_control(),  # 带尺寸控制
            create_controller_with_size_control(size_threshold=0.9)  # 自定义阈值
        ]
        
        # 测试数据
        detections = [
            {"group_id": 1, "label": "二", "bbox": [100 + i*50, 100, 145 + i*50, 160]}
            for i in range(20)
        ]
        
        for controller in controllers:
            with patch.object(controller, '_initialize_processors'):
                # 验证接口一致性
                self.assertTrue(hasattr(controller, 'process_frame'))
                self.assertTrue(hasattr(controller, 'get_system_status'))
                self.assertTrue(hasattr(controller, 'get_performance_stats'))
                self.assertTrue(hasattr(controller, 'switch_strategy'))
                
                # 验证返回类型一致性
                status = controller.get_system_status()
                self.assertIsInstance(status, dict)
                self.assertIn("current_strategy", status)
                self.assertIn("config", status)
                
                stats = controller.get_performance_stats()
                self.assertIsInstance(stats, dict)
    
    def test_factory_functions(self):
        """测试工厂函数"""
        # 测试默认控制器
        with patch('src.core.digital_twin_controller.create_phase1_integrator'), \
             patch('src.core.digital_twin_controller.create_phase2_integrator'):
            
            controller1 = create_complete_controller()
            self.assertIsInstance(controller1, DigitalTwinController)
            
            # 测试带尺寸控制的控制器
            controller2 = create_controller_with_size_control()
            self.assertIsInstance(controller2, DigitalTwinController)
            self.assertTrue(controller2.config.enable_size_activation_control)
            self.assertEqual(controller2.config.size_threshold, 0.85)
            
            # 测试自定义参数
            controller3 = create_controller_with_size_control(
                size_threshold=0.9,
                qualified_ratio_threshold=0.95,
                min_card_count=25
            )
            self.assertEqual(controller3.config.size_threshold, 0.9)
            self.assertEqual(controller3.config.qualified_ratio_threshold, 0.95)
            self.assertEqual(controller3.config.min_card_count, 25)
    
    def test_error_handling(self):
        """测试错误处理"""
        # 测试尺寸控制器初始化失败的情况
        with patch('src.core.digital_twin_controller.create_phase1_integrator'), \
             patch('src.core.digital_twin_controller.create_phase2_integrator'), \
             patch('src.core.digital_twin_controller.create_card_size_activation_controller', 
                   side_effect=Exception("初始化失败")):
            
            config = DigitalTwinConfig(enable_size_activation_control=True)
            controller = DigitalTwinController(config)
            
            # 应该自动禁用尺寸控制
            self.assertFalse(controller.config.enable_size_activation_control)
            self.assertIsNone(controller.size_activation_controller)
    
    def test_performance_stats_tracking(self):
        """测试性能统计跟踪"""
        # 执行多次处理
        good_detections = [
            {"group_id": 1, "label": "二", "bbox": [100 + i*50, 100, 145 + i*50, 160]}
            for i in range(20)
        ]
        
        bad_detections = [
            {"group_id": 1, "label": "二", "bbox": [100 + i*50, 100, 120 + i*50, 120]}
            for i in range(15)
        ]
        
        # Mock处理器
        mock_result = ProcessingResult(
            success=True,
            processed_cards=good_detections,
            statistics={},
            processing_time=0.1,
            strategy_used="phase2_complete"
        )
        
        with patch.object(self.controller.processors[ProcessingStrategy.PHASE2_COMPLETE], 
                         'process_frame', return_value=mock_result):
            
            # 执行处理
            self.controller.process_frame(good_detections)  # 应该启动
            self.controller.process_frame(bad_detections)   # 应该不启动
            self.controller.process_frame(good_detections)  # 应该启动
        
        # 验证统计信息
        stats = self.controller.get_performance_stats()
        self.assertEqual(stats["activation_decisions"]["total"], 3)
        self.assertEqual(stats["activation_decisions"]["activated"], 2)
        self.assertEqual(stats["activation_decisions"]["deactivated"], 1)

if __name__ == '__main__':
    # 运行测试
    unittest.main(verbosity=2)
