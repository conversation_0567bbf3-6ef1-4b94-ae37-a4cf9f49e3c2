import os
import sys
import cv2
import numpy as np
import time
import argparse
from pprint import pprint

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from src.core.state_builder import StateBuilder
from src.core.decision import DecisionMaker

def test_image_processing(image_path, config_path='src/config/config.json'):
    """
    测试图像处理
    
    Args:
        image_path (str): 图像路径
        config_path (str): 配置文件路径
    """
    print(f"===== 测试图像处理: {image_path} =====")
    
    # 加载图像
    if not os.path.exists(image_path):
        print(f"图像文件不存在: {image_path}")
        return
    
    image = cv2.imread(image_path)
    if image is None:
        print(f"无法加载图像: {image_path}")
        return
    
    # 调整图像大小
    image = cv2.resize(image, (640, 320))
    
    # 加载YOLO模型
    try:
        from ultralytics import YOLO
        
        # 加载配置
        import json
        config = {}
        if os.path.exists(config_path):
            with open(config_path, 'r', encoding='utf-8') as f:
                if config_path.endswith('.json'):
                    config = json.load(f)
                elif config_path.endswith('.yaml') or config_path.endswith('.yml'):
                    import yaml
                    config = yaml.safe_load(f)
        
        model_path = config.get('model_path', 'best.pt')
        if not os.path.exists(model_path):
            print(f"模型文件不存在: {model_path}")
            return
        
        # 加载模型
        model = YOLO(model_path)
        print(f"成功加载模型: {model_path}")
        
        # 初始化状态转换模块和决策模块
        state_builder = StateBuilder(config_path)
        decision_maker = DecisionMaker(config.get('decision', {}))
        
        # 记录开始时间
        start_time = time.time()
        
        # 执行检测
        results = model(image)
        
        # 计算检测时间
        detection_time = time.time() - start_time
        
        # 解析结果
        cards = []
        for result in results:
            boxes = result.boxes
            for i, box in enumerate(boxes):
                # 获取坐标和置信度
                x1, y1, x2, y2 = box.xyxy[0].tolist()
                conf = box.conf[0].item()
                cls = int(box.cls[0].item())
                
                # 获取类别名称
                cls_name = result.names[cls]
                
                # 解析类别名称中的信息
                if '_' in cls_name:
                    parts = cls_name.split('_')
                    if len(parts) >= 2:
                        # 假设格式为 "group_id:类别名"
                        group_id = int(parts[0])
                        card_name = '_'.join(parts[1:])
                        
                        # 创建卡牌信息
                        card = {
                            'id': f"{i+1}_{card_name}",  # 简单ID生成
                            'group_id': group_id,
                            'pos': [x1, y1, x2-x1, y2-y1],
                            'conf': conf
                        }
                        cards.append(card)
        
        # 创建检测结果
        detections = {'cards': cards}
        
        # 转换为RLCard状态
        rlcard_state = state_builder.yolo_to_rlcard_state(detections)
        
        # 做出决策
        action, confidence = decision_maker.make_decision(rlcard_state)
        
        # 计算胜率
        win_rate = decision_maker.get_win_rate(rlcard_state)
        
        # 格式化决策结果
        decision = decision_maker.format_decision(action, confidence, win_rate)
        
        # 打印结果
        print(f"检测到 {len(cards)} 张卡牌，耗时 {detection_time*1000:.1f}ms")
        print("\n检测结果:")
        for i, card in enumerate(cards[:5]):  # 只显示前5张
            print(f"  卡牌 {i+1}: ID={card['id']}, 组={card['group_id']}, 置信度={card['conf']:.2f}")
        if len(cards) > 5:
            print(f"  ... 还有 {len(cards)-5} 张卡牌")
        
        print("\nRLCard状态:")
        print(f"  手牌数量: {len(rlcard_state.get('hand', []))}")
        print(f"  弃牌数量: {len(rlcard_state.get('discard_pile', []))}")
        print(f"  组合牌数量: {len(rlcard_state.get('combo_cards', []))}")
        print(f"  可选动作: {rlcard_state.get('legal_actions', [])}")
        
        print("\n决策结果:")
        for key, value in decision.items():
            print(f"  {key}: {value}")
        
        # 可视化结果
        vis_image = image.copy()
        
        # 绘制检测结果
        for card in cards:
            x, y, w, h = [int(v) for v in card['pos']]
            conf = card['conf']
            group_id = card.get('group_id', 0)
            card_id = card.get('id', '')
            
            # 根据group_id选择颜色
            colors = {
                1: (0, 255, 0),    # 手牌_观战方: 绿色
                2: (0, 255, 255),  # 调整手牌_观战方: 黄色
                3: (255, 0, 0),    # 抓牌_观战方: 蓝色
                4: (255, 0, 255),  # 打牌_观战方: 紫色
                5: (0, 0, 255),    # 弃牌_观战方: 红色
                6: (255, 255, 0),  # 吃碰区_观战方: 青色
                7: (128, 0, 0),    # 抓牌_对战方: 深蓝色
                8: (0, 128, 0),    # 打牌_对战方: 深绿色
                9: (0, 0, 128),    # 弃牌_对战方: 深红色
                16: (128, 128, 0), # 吃碰区_对战方: 深青色
            }
            color = colors.get(group_id, (255, 255, 255))
            
            # 绘制边界框
            cv2.rectangle(vis_image, (x, y), (x + w, y + h), color, 2)
            
            # 绘制标签
            label = f"{card_id} ({conf:.2f})"
            cv2.putText(vis_image, label, (x, y - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 1)
        
        # 绘制决策结果
        h, w = vis_image.shape[:2]
        cv2.rectangle(vis_image, (w - 200, 0), (w, 100), (0, 0, 0), -1)
        cv2.putText(vis_image, f"推荐: {decision.get('推荐动作', '无')}", (w - 190, 30), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
        cv2.putText(vis_image, f"置信度: {decision.get('置信度', '0%')}", (w - 190, 60), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
        cv2.putText(vis_image, f"胜率: {decision.get('胜率', '0%')}", (w - 190, 90), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
        
        # 显示结果
        cv2.imshow("检测结果", vis_image)
        cv2.waitKey(0)
        cv2.destroyAllWindows()
        
    except ImportError:
        print("未安装ultralytics库，无法加载YOLO模型")
        return
    except Exception as e:
        print(f"发生错误: {e}")
        return

def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description="跑胡子AI集成测试")
    parser.add_argument('--image', type=str, required=True, help='测试图像路径')
    parser.add_argument('--config', type=str, default='src/config.json', help='配置文件路径')
    return parser.parse_args()

if __name__ == "__main__":
    # 解析命令行参数
    args = parse_args()
    
    # 测试图像处理
    test_image_processing(args.image, args.config) 