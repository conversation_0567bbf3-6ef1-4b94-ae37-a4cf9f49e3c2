"""
测试calibration_gt_final_processor.py与第二阶段模块化系统的集成
"""

import sys
import os
sys.path.append('src')

def test_calibration_integration():
    """测试集成情况"""
    print("🧪 测试calibration_gt_final_processor.py集成")
    print("=" * 60)
    
    try:
        # 测试导入
        from calibration_gt_final_processor import CalibrationGTFinalProcessor, FinalProcessorConfig
        print("✅ CalibrationGTFinalProcessor导入成功")
        
        # 创建配置
        config = FinalProcessorConfig()
        print("✅ 配置创建成功")
        
        # 创建处理器
        processor = CalibrationGTFinalProcessor(config)
        print("✅ 处理器创建成功")
        
        # 检查数字孪生系统
        if hasattr(processor.digital_twin_system, 'get_system_status'):
            status = processor.digital_twin_system.get_system_status()
            print("✅ 第二阶段模块化系统集成成功")
            print(f"   系统状态: {status}")
        else:
            print("❌ 数字孪生系统接口不匹配")
            return False
        
        # 测试处理方法是否存在
        if hasattr(processor, 'process_final_dataset'):
            print("✅ 处理方法存在")
        else:
            print("❌ 处理方法缺失")
            return False
        
        print("\n🎉 集成测试成功！")
        print("✅ 第二阶段模块化系统已成功集成到calibration_gt_final_processor.py")
        print("✅ 现在可以启动处理器进行实际测试")
        
        return True
        
    except Exception as e:
        print(f"❌ 集成测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_module_functionality():
    """测试模块功能"""
    print("\n🔧 测试模块功能")
    print("=" * 60)
    
    try:
        from src.modules import create_phase2_integrator
        
        # 创建系统
        system = create_phase2_integrator()
        print("✅ 第二阶段系统创建成功")
        
        # 模拟calibration_gt格式的检测数据
        test_detections = [
            {
                'label': '二',
                'bbox': [100, 100, 150, 150],
                'confidence': 0.9,
                'group_id': 1
            },
            {
                'label': '暗',
                'bbox': [500, 100, 550, 150],
                'confidence': 0.7,
                'group_id': 6
            }
        ]
        
        # 处理测试数据
        result = system.process_frame(test_detections)
        print(f"✅ 处理测试: {'成功' if result.success else '失败'}")
        
        if result.success:
            print(f"   处理卡牌: {len(result.processed_cards)}张")
            for card in result.processed_cards:
                features = []
                if card.get('inherited', False):
                    features.append("继承")
                if card.get('transitioned', False):
                    features.append("流转")
                if card.get('associated', False):
                    features.append("暗牌关联")
                if card.get('is_compensated', False):
                    features.append("补偿")
                
                feature_str = f"({', '.join(features)})" if features else ""
                print(f"     {card['twin_id']} - 区域{card['group_id']}, {card['label']} {feature_str}")
            
            # 显示统计信息
            if 'summary' in result.statistics:
                summary = result.statistics['summary']
                print(f"   关键指标:")
                print(f"     继承率: {summary.get('inheritance_rate', 0):.1%}")
                print(f"     流转率: {summary.get('transition_rate', 0):.1%}")
                print(f"     暗牌成功率: {summary.get('dark_card_success_rate', 0):.1%}")
                print(f"     补偿率: {summary.get('compensation_rate', 0):.1%}")
        
        return True
        
    except Exception as e:
        print(f"❌ 模块功能测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 calibration_gt_final_processor.py集成测试")
    print("=" * 80)
    
    # 测试集成
    integration_success = test_calibration_integration()
    
    # 测试模块功能
    module_success = test_module_functionality()
    
    print("\n" + "=" * 80)
    if integration_success and module_success:
        print("🎉 所有测试通过！")
        print("\n✅ 集成状态:")
        print("  - 第二阶段模块化系统已成功集成")
        print("  - calibration_gt_final_processor.py已更新")
        print("  - 所有核心功能正常工作")
        print("\n🚀 现在可以启动calibration_gt_final_processor.py进行实际测试:")
        print("  python calibration_gt_final_processor.py")
        print("\n🎯 预期改进:")
        print("  - 区域流转问题修复: 1二1 → 1二2 而不是 1二1 → 2二2")
        print("  - 暗牌关联问题修复: 1暗 → 1二暗 而不是 1暗、2暗")
        print("  - 继承机制稳定: 相同区域+标签正确继承ID")
        print("  - 智能遮挡补偿: 避免过度补偿")
    else:
        print("❌ 测试失败，需要修复问题后再试")

if __name__ == "__main__":
    main()
