#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
简单的IoU调试脚本
分析YOLO检测与zhuangtaiquyu标注的具体匹配问题
"""

import os
import sys
import json
import cv2
import numpy as np
from pathlib import Path

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from src.core.detect import CardDetector


def calculate_iou(bbox1, bbox2):
    """计算IoU"""
    x1_1, y1_1, x2_1, y2_1 = bbox1
    x1_2, y1_2, x2_2, y2_2 = bbox2
    
    # 计算交集
    x1_inter = max(x1_1, x1_2)
    y1_inter = max(y1_1, y1_2)
    x2_inter = min(x2_1, x2_2)
    y2_inter = min(y2_1, y2_2)
    
    if x2_inter <= x1_inter or y2_inter <= y1_inter:
        return 0.0
    
    inter_area = (x2_inter - x1_inter) * (y2_inter - y1_inter)
    
    # 计算并集
    area1 = (x2_1 - x1_1) * (y2_1 - y1_1)
    area2 = (x2_2 - x1_2) * (y2_2 - y1_2)
    union_area = area1 + area2 - inter_area
    
    if union_area <= 0:
        return 0.0
    
    return inter_area / union_area


def load_annotation(dataset_id: str, frame_name: str):
    """加载标注文件"""
    base_path = Path("legacy_assets/ceshi/zhuangtaiquyu")
    json_file = base_path / "labels" / "train" / dataset_id / f"{frame_name}.json"
    
    if not json_file.exists():
        return None
    
    with open(json_file, 'r', encoding='utf-8') as f:
        return json.load(f)


def extract_ground_truth_cards(annotation):
    """从标注中提取卡牌信息"""
    cards = []
    
    if 'shapes' not in annotation:
        return cards
    
    for shape in annotation['shapes']:
        if shape.get('shape_type') == 'rectangle':
            points = shape.get('points', [])
            if len(points) >= 4:
                # 4个点的矩形格式
                all_x = [p[0] for p in points]
                all_y = [p[1] for p in points]
                x1, x2 = min(all_x), max(all_x)
                y1, y2 = min(all_y), max(all_y)
                bbox = [x1, y1, x2, y2]
            elif len(points) >= 2:
                # 2个点格式
                bbox = [points[0][0], points[0][1], points[1][0], points[1][1]]
            else:
                continue
                
            card_info = {
                'label': shape.get('label', ''),
                'group_id': shape.get('group_id', 0),
                'bbox': bbox
            }
            cards.append(card_info)
    
    return cards


def debug_specific_match():
    """调试具体的匹配问题"""
    print("🔍 调试具体的IoU匹配问题")
    print("="*50)
    
    # 初始化检测器
    detector = CardDetector("best.pt", enable_validation=False)
    
    # 测试一个样本
    dataset_id = "1"
    frame_name = "frame_00000"
    
    # 加载图片
    base_path = Path("legacy_assets/ceshi/zhuangtaiquyu")
    img_path = base_path / "images" / "train" / dataset_id / f"{frame_name}.jpg"
    
    image = cv2.imread(str(img_path))
    if image is None:
        print(f"❌ 无法读取图片")
        return
    
    print(f"📷 图片尺寸: {image.shape}")
    
    # YOLO检测
    detections = detector.detect_image(image)
    print(f"\n🎯 YOLO检测结果 ({len(detections)}个):")
    
    # 加载标注
    annotation = load_annotation(dataset_id, frame_name)
    if not annotation:
        print(f"❌ 无法加载标注文件")
        return
    
    ground_truth_cards = extract_ground_truth_cards(annotation)
    print(f"\n📋 标注结果 ({len(ground_truth_cards)}个):")
    
    # 详细分析前5个检测结果和前5个标注
    print(f"\n🔍 详细分析前5个检测和标注:")
    
    for i in range(min(5, len(detections), len(ground_truth_cards))):
        det = detections[i]
        gt = ground_truth_cards[i]
        
        print(f"\n--- 对比 {i+1} ---")
        
        # YOLO检测
        bbox = det.get('bbox', [0, 0, 0, 0])
        x, y, w, h = bbox
        det_bbox = [x, y, x + w, y + h]
        
        print(f"YOLO检测:")
        print(f"  标签: {det.get('label', 'unknown')}")
        print(f"  原始bbox: {bbox} (x, y, w, h)")
        print(f"  转换bbox: {det_bbox} (x1, y1, x2, y2)")
        print(f"  中心: ({(det_bbox[0] + det_bbox[2])/2:.1f}, {(det_bbox[1] + det_bbox[3])/2:.1f})")
        
        # 真实标注
        gt_bbox = gt['bbox']
        print(f"真实标注:")
        print(f"  标签: {gt['label']}")
        print(f"  区域ID: {gt['group_id']}")
        print(f"  bbox: {gt_bbox} (x1, y1, x2, y2)")
        print(f"  中心: ({(gt_bbox[0] + gt_bbox[2])/2:.1f}, {(gt_bbox[1] + gt_bbox[3])/2:.1f})")
        
        # 计算IoU
        iou = calculate_iou(det_bbox, gt_bbox)
        print(f"IoU: {iou:.6f}")
        
        # 分析距离
        det_center = [(det_bbox[0] + det_bbox[2])/2, (det_bbox[1] + det_bbox[3])/2]
        gt_center = [(gt_bbox[0] + gt_bbox[2])/2, (gt_bbox[1] + gt_bbox[3])/2]
        distance = ((det_center[0] - gt_center[0])**2 + (det_center[1] - gt_center[1])**2)**0.5
        print(f"中心距离: {distance:.1f}像素")
        
        # 判断是否应该匹配
        if iou > 0.1:
            print(f"✅ 可以匹配 (IoU > 0.1)")
        elif distance < 50:
            print(f"🟡 距离较近但IoU低 (距离 < 50px)")
        else:
            print(f"❌ 无法匹配")
    
    # 尝试找到最佳匹配
    print(f"\n🎯 寻找最佳匹配:")
    
    for i, det in enumerate(detections[:3]):
        bbox = det.get('bbox', [0, 0, 0, 0])
        x, y, w, h = bbox
        det_bbox = [x, y, x + w, y + h]
        
        print(f"\n检测{i+1} ({det.get('label', 'unknown')}) 的最佳匹配:")
        
        best_iou = 0.0
        best_match = None
        best_idx = -1
        
        for j, gt in enumerate(ground_truth_cards):
            gt_bbox = gt['bbox']
            iou = calculate_iou(det_bbox, gt_bbox)
            
            if iou > best_iou:
                best_iou = iou
                best_match = gt
                best_idx = j
            
            if iou > 0.01:  # 显示所有有一点重叠的
                print(f"  与标注{j+1} ({gt['label']}): IoU={iou:.6f}")
        
        if best_match:
            print(f"  最佳匹配: 标注{best_idx+1} ({best_match['label']}) IoU={best_iou:.6f}")
        else:
            print(f"  无匹配 (最高IoU={best_iou:.6f})")


if __name__ == "__main__":
    print("🔍 简单IoU调试分析")
    print("="*60)
    
    try:
        debug_specific_match()
        
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()
