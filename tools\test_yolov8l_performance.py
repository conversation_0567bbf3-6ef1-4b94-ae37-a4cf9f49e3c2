#!/usr/bin/env python3
"""
YOLOv8l模型性能测试

复现开发过程10的测试，验证新导出的YOLOv8l ONNX模型的准确率是否有提高
使用与AnyLabeling一致的参数：conf_threshold=0.25, iou_threshold=0.45
"""

import sys
import os
import json
import cv2
import numpy as np
from pathlib import Path
from typing import List, Dict, Any, Tuple
import logging
from datetime import datetime
import time

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 导入项目模块
from src.core.detect import CardDetector

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class YOLOv8lPerformanceTester:
    """YOLOv8l性能测试器"""
    
    def __init__(self):
        # 加载配置
        self.config = self.load_config()
        
        # 测试数据路径
        self.test_data_paths = {
            'calibration_gt': Path("legacy_assets/ceshi/calibration_gt"),
            'zhuangtaiquyu': Path("legacy_assets/ceshi/zhuangtaiquyu")
        }
        
        # 测试结果
        self.test_results = {
            'model_info': {},
            'performance_metrics': {},
            'detailed_results': [],
            'comparison_with_baseline': {},
            'test_summary': {}
        }

    def load_config(self) -> Dict:
        """加载配置文件"""
        config_path = "src/config/config.json"
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            logger.info(f"配置文件加载成功: {config_path}")
            return config
        except Exception as e:
            logger.warning(f"配置文件加载失败: {e}")
            # 返回默认配置
            return {
                'model_path': 'data/processed/train/weights/best.onnx',
                'confidence_threshold': 0.25,
                'iou_threshold': 0.45
            }

    def initialize_detector(self) -> bool:
        """初始化检测器"""
        try:
            model_path = self.config.get('model_path', 'data/processed/train/weights/best.onnx')
            conf_threshold = self.config.get('confidence_threshold', 0.25)
            iou_threshold = self.config.get('iou_threshold', 0.45)
            
            logger.info(f"初始化YOLOv8l检测器...")
            logger.info(f"模型路径: {model_path}")
            logger.info(f"置信度阈值: {conf_threshold}")
            logger.info(f"IoU阈值: {iou_threshold}")
            
            # 检查模型文件是否存在
            if not os.path.exists(model_path):
                logger.error(f"模型文件不存在: {model_path}")
                return False
                
            # 初始化检测器
            self.detector = CardDetector(
                model_path=model_path,
                conf_threshold=conf_threshold,
                iou_threshold=iou_threshold,
                enable_validation=False  # 先关闭验证，专注测试原始性能
            )
            
            # 记录模型信息
            self.test_results['model_info'] = {
                'model_path': model_path,
                'model_type': 'YOLOv8l ONNX',
                'conf_threshold': conf_threshold,
                'iou_threshold': iou_threshold,
                'anylabeling_compatible': True
            }
            
            logger.info("✅ 检测器初始化成功")
            return True
            
        except Exception as e:
            logger.error(f"检测器初始化失败: {e}")
            return False
            
    def collect_test_images(self, max_images: int = 100) -> List[Tuple[str, str]]:
        """收集测试图像和标注"""
        test_pairs = []
        
        # 从calibration_gt收集高质量数据
        calibration_path = self.test_data_paths['calibration_gt']
        if calibration_path.exists():
            images_dir = calibration_path / "images"
            labels_dir = calibration_path / "labels"
            
            if images_dir.exists() and labels_dir.exists():
                for img_file in images_dir.glob("*.jpg"):
                    json_file = labels_dir / f"{img_file.stem}.json"
                    if json_file.exists():
                        test_pairs.append((str(img_file), str(json_file)))
                        
        logger.info(f"从calibration_gt收集到 {len(test_pairs)} 个高质量样本")
        
        # 从zhuangtaiquyu收集更多数据
        zhuangtaiquyu_path = self.test_data_paths['zhuangtaiquyu']
        if zhuangtaiquyu_path.exists():
            labels_train_path = zhuangtaiquyu_path / "labels" / "train"
            images_train_path = zhuangtaiquyu_path / "images" / "train"
            
            if labels_train_path.exists() and images_train_path.exists():
                zhuangtaiquyu_pairs = []
                for region_dir in labels_train_path.iterdir():
                    if region_dir.is_dir():
                        for json_file in region_dir.glob("*.json"):
                            img_file = images_train_path / region_dir.name / f"{json_file.stem}.jpg"
                            if img_file.exists():
                                zhuangtaiquyu_pairs.append((str(img_file), str(json_file)))
                                
                # 随机选择一部分
                import random
                random.shuffle(zhuangtaiquyu_pairs)
                selected_count = min(max_images - len(test_pairs), len(zhuangtaiquyu_pairs))
                test_pairs.extend(zhuangtaiquyu_pairs[:selected_count])
                
        logger.info(f"总共收集到 {len(test_pairs)} 个测试样本")
        return test_pairs
        
    def load_ground_truth(self, json_file: str) -> List[Dict]:
        """加载真实标注"""
        try:
            with open(json_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
                
            ground_truth = []
            for shape in data.get('shapes', []):
                if len(shape.get('points', [])) >= 4:
                    points = shape['points']
                    x1, y1 = points[0]
                    x2, y2 = points[2]
                    
                    # 确保坐标顺序正确
                    x1, x2 = min(x1, x2), max(x1, x2)
                    y1, y2 = min(y1, y2), max(y1, y2)
                    
                    gt = {
                        'bbox': [x1, y1, x2, y2],
                        'label': shape.get('label', ''),
                        'area': (x2 - x1) * (y2 - y1),
                        'group_id': shape.get('group_id', 1)
                    }
                    ground_truth.append(gt)
                    
            return ground_truth
            
        except Exception as e:
            logger.error(f"加载真实标注失败 {json_file}: {e}")
            return []
            
    def calculate_iou(self, box1: List[float], box2: List[float]) -> float:
        """计算IoU"""
        x1 = max(box1[0], box2[0])
        y1 = max(box1[1], box2[1])
        x2 = min(box1[2], box2[2])
        y2 = min(box1[3], box2[3])
        
        if x2 <= x1 or y2 <= y1:
            return 0.0
            
        inter_area = (x2 - x1) * (y2 - y1)
        area1 = (box1[2] - box1[0]) * (box1[3] - box1[1])
        area2 = (box2[2] - box2[0]) * (box2[3] - box2[1])
        union_area = area1 + area2 - inter_area
        
        return inter_area / union_area if union_area > 0 else 0.0
        
    def evaluate_single_image(self, image_path: str, json_path: str) -> Dict:
        """评估单张图像"""
        try:
            # 加载图像
            image = cv2.imread(image_path)
            if image is None:
                return {'error': f'无法加载图像: {image_path}'}
                
            # 加载真实标注
            ground_truth = self.load_ground_truth(json_path)
            if not ground_truth:
                return {'error': f'无法加载标注: {json_path}'}
                
            # 执行检测
            start_time = time.time()
            detections = self.detector.detect_image(image)
            inference_time = time.time() - start_time
            
            # 计算指标
            metrics = self.calculate_metrics(detections, ground_truth)
            
            result = {
                'image_path': image_path,
                'json_path': json_path,
                'inference_time': inference_time,
                'detection_count': len(detections),
                'ground_truth_count': len(ground_truth),
                'metrics': metrics,
                'detections': detections[:5],  # 只保存前5个检测结果
                'ground_truth': ground_truth[:5]  # 只保存前5个真实标注
            }
            
            return result
            
        except Exception as e:
            logger.error(f"评估图像失败 {image_path}: {e}")
            return {'error': str(e)}
            
    def calculate_metrics(self, detections: List[Dict], ground_truth: List[Dict], iou_threshold: float = 0.5) -> Dict:
        """计算评估指标"""
        if not ground_truth:
            return {'precision': 0, 'recall': 0, 'f1': 0, 'ap': 0}
            
        # 匹配检测结果与真实标注
        matched_detections = []
        matched_gt = set()
        
        for i, detection in enumerate(detections):
            det_bbox = detection.get('bbox', [])
            if len(det_bbox) != 4:
                continue

            # 转换bbox格式：[x, y, w, h] -> [x1, y1, x2, y2]
            x, y, w, h = det_bbox
            det_bbox = [x, y, x + w, y + h]
                
            best_iou = 0
            best_gt_idx = -1
            
            for j, gt in enumerate(ground_truth):
                gt_bbox = gt.get('bbox', [])
                if len(gt_bbox) != 4:
                    continue
                    
                iou = self.calculate_iou(det_bbox, gt_bbox)
                if iou > best_iou and iou > iou_threshold:
                    best_iou = iou
                    best_gt_idx = j
                    
            if best_gt_idx >= 0:
                matched_detections.append({
                    'detection_idx': i,
                    'gt_idx': best_gt_idx,
                    'iou': best_iou,
                    'confidence': detection.get('confidence', 0)
                })
                matched_gt.add(best_gt_idx)
                
        # 计算基础指标
        precision = len(matched_detections) / len(detections) if detections else 0
        recall = len(matched_gt) / len(ground_truth) if ground_truth else 0
        f1 = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0
        
        # 计算AP（简化版本）
        ap = self.calculate_ap(matched_detections, len(ground_truth))
        
        return {
            'precision': precision,
            'recall': recall,
            'f1': f1,
            'ap': ap,
            'matched_count': len(matched_detections),
            'missed_count': len(ground_truth) - len(matched_gt),
            'false_positive_count': len(detections) - len(matched_detections)
        }
        
    def calculate_ap(self, matched_detections: List[Dict], total_gt: int) -> float:
        """计算平均精度（简化版本）"""
        if not matched_detections or total_gt == 0:
            return 0.0
            
        # 按置信度排序
        sorted_matches = sorted(matched_detections, key=lambda x: x['confidence'], reverse=True)
        
        # 计算precision-recall曲线下面积
        precisions = []
        recalls = []
        
        for i in range(len(sorted_matches)):
            tp = i + 1
            fp = 0  # 简化：假设所有匹配都是真正例
            precision = tp / (tp + fp)
            recall = tp / total_gt
            
            precisions.append(precision)
            recalls.append(recall)
            
        # 计算AP
        if len(precisions) > 1:
            ap = np.trapz(precisions, recalls)
        else:
            ap = precisions[0] if precisions else 0.0
            
        return ap
        
    def run_performance_test(self) -> Dict:
        """运行性能测试"""
        logger.info("🚀 开始YOLOv8l性能测试")
        logger.info("=" * 60)
        
        # 1. 初始化检测器
        if not self.initialize_detector():
            return {}
            
        # 2. 收集测试数据
        test_pairs = self.collect_test_images(max_images=100)
        if not test_pairs:
            logger.error("未找到测试数据")
            return {}
            
        # 3. 执行测试
        logger.info(f"开始测试 {len(test_pairs)} 张图像...")
        
        all_results = []
        total_metrics = {
            'precision': [], 'recall': [], 'f1': [], 'ap': [],
            'inference_times': [], 'detection_counts': []
        }
        
        for i, (image_path, json_path) in enumerate(test_pairs):
            if i % 20 == 0:
                logger.info(f"处理进度: {i+1}/{len(test_pairs)}")
                
            result = self.evaluate_single_image(image_path, json_path)
            
            if 'error' not in result:
                all_results.append(result)
                
                # 累积指标
                metrics = result['metrics']
                total_metrics['precision'].append(metrics['precision'])
                total_metrics['recall'].append(metrics['recall'])
                total_metrics['f1'].append(metrics['f1'])
                total_metrics['ap'].append(metrics['ap'])
                total_metrics['inference_times'].append(result['inference_time'])
                total_metrics['detection_counts'].append(result['detection_count'])
                
        # 4. 计算总体指标
        performance_metrics = {
            'avg_precision': np.mean(total_metrics['precision']),
            'avg_recall': np.mean(total_metrics['recall']),
            'avg_f1': np.mean(total_metrics['f1']),
            'avg_ap': np.mean(total_metrics['ap']),
            'avg_inference_time': np.mean(total_metrics['inference_times']),
            'avg_detection_count': np.mean(total_metrics['detection_counts']),
            'total_images_tested': len(all_results),
            'total_detections': sum(total_metrics['detection_counts']),
            'fps': 1.0 / np.mean(total_metrics['inference_times']) if total_metrics['inference_times'] else 0
        }
        
        # 5. 保存结果
        self.test_results.update({
            'performance_metrics': performance_metrics,
            'detailed_results': all_results,
            'test_summary': {
                'test_date': datetime.now().isoformat(),
                'test_images_count': len(test_pairs),
                'successful_tests': len(all_results),
                'failed_tests': len(test_pairs) - len(all_results)
            }
        })
        
        # 6. 生成报告
        self.generate_performance_report()
        
        return self.test_results
        
    def generate_performance_report(self):
        """生成性能测试报告"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 保存详细JSON结果
        json_file = f"analysis/yolov8l_performance_test_{timestamp}.json"
        os.makedirs(os.path.dirname(json_file), exist_ok=True)
        
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(self.test_results, f, ensure_ascii=False, indent=2, default=str)
            
        # 生成Markdown报告
        report = self.create_markdown_report()
        report_file = f"analysis/yolov8l_performance_report_{timestamp}.md"
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report)
            
        logger.info(f"性能测试报告已保存:")
        logger.info(f"  - 详细数据: {json_file}")
        logger.info(f"  - 测试报告: {report_file}")

    def create_markdown_report(self) -> str:
        """创建Markdown格式的测试报告"""
        report = []
        report.append("# YOLOv8l模型性能测试报告\n")
        report.append(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")

        # 模型信息
        model_info = self.test_results.get('model_info', {})
        report.append("## 📋 模型信息")
        report.append(f"- **模型类型**: {model_info.get('model_type', 'N/A')}")
        report.append(f"- **模型路径**: {model_info.get('model_path', 'N/A')}")
        report.append(f"- **置信度阈值**: {model_info.get('conf_threshold', 'N/A')}")
        report.append(f"- **IoU阈值**: {model_info.get('iou_threshold', 'N/A')}")
        report.append(f"- **AnyLabeling兼容**: {model_info.get('anylabeling_compatible', 'N/A')}")
        report.append("")

        # 测试概览
        test_summary = self.test_results.get('test_summary', {})
        report.append("## 📊 测试概览")
        report.append(f"- **测试图像数**: {test_summary.get('test_images_count', 0)}")
        report.append(f"- **成功测试数**: {test_summary.get('successful_tests', 0)}")
        report.append(f"- **失败测试数**: {test_summary.get('failed_tests', 0)}")
        report.append(f"- **测试日期**: {test_summary.get('test_date', 'N/A')}")
        report.append("")

        # 性能指标
        metrics = self.test_results.get('performance_metrics', {})
        report.append("## 🎯 性能指标")
        report.append("| 指标 | 数值 | 说明 |")
        report.append("|------|------|------|")
        report.append(f"| **平均精确率** | {metrics.get('avg_precision', 0):.3f} | 检测准确性 |")
        report.append(f"| **平均召回率** | {metrics.get('avg_recall', 0):.3f} | 检测完整性 |")
        report.append(f"| **平均F1分数** | {metrics.get('avg_f1', 0):.3f} | 综合性能 |")
        report.append(f"| **平均AP** | {metrics.get('avg_ap', 0):.3f} | 平均精度 |")
        report.append(f"| **平均推理时间** | {metrics.get('avg_inference_time', 0):.4f}秒 | 单张图像处理时间 |")
        report.append(f"| **推理速度** | {metrics.get('fps', 0):.1f} FPS | 每秒处理帧数 |")
        report.append(f"| **平均检测数** | {metrics.get('avg_detection_count', 0):.1f} | 每张图像检测目标数 |")
        report.append(f"| **总检测数** | {metrics.get('total_detections', 0)} | 所有图像总检测数 |")
        report.append("")

        # 性能评估
        report.append("## 📈 性能评估")

        avg_precision = metrics.get('avg_precision', 0)
        avg_recall = metrics.get('avg_recall', 0)
        avg_f1 = metrics.get('avg_f1', 0)
        fps = metrics.get('fps', 0)

        # 精确率评估
        if avg_precision >= 0.9:
            precision_status = "🟢 优秀"
        elif avg_precision >= 0.8:
            precision_status = "🟡 良好"
        else:
            precision_status = "🔴 需要改进"

        # 召回率评估
        if avg_recall >= 0.9:
            recall_status = "🟢 优秀"
        elif avg_recall >= 0.8:
            recall_status = "🟡 良好"
        else:
            recall_status = "🔴 需要改进"

        # F1分数评估
        if avg_f1 >= 0.9:
            f1_status = "🟢 优秀"
        elif avg_f1 >= 0.8:
            f1_status = "🟡 良好"
        else:
            f1_status = "🔴 需要改进"

        # 速度评估
        if fps >= 30:
            speed_status = "🟢 实时"
        elif fps >= 10:
            speed_status = "🟡 可用"
        else:
            speed_status = "🔴 较慢"

        report.append(f"- **精确率**: {precision_status} ({avg_precision:.3f})")
        report.append(f"- **召回率**: {recall_status} ({avg_recall:.3f})")
        report.append(f"- **F1分数**: {f1_status} ({avg_f1:.3f})")
        report.append(f"- **推理速度**: {speed_status} ({fps:.1f} FPS)")
        report.append("")

        # 与开发过程10基线对比
        report.append("## 📊 与基线对比")
        report.append("### 开发过程10基线指标")
        report.append("- 基线精确率: 待补充")
        report.append("- 基线召回率: 待补充")
        report.append("- 基线F1分数: 待补充")
        report.append("")
        report.append("### 改进情况")
        report.append("- 精确率改进: 待计算")
        report.append("- 召回率改进: 待计算")
        report.append("- F1分数改进: 待计算")
        report.append("")

        # 结论和建议
        report.append("## 💡 结论和建议")

        if avg_f1 >= 0.85:
            report.append("### ✅ 测试结论")
            report.append("YOLOv8l模型性能表现优秀，达到了预期的性能目标。")
        elif avg_f1 >= 0.75:
            report.append("### 🟡 测试结论")
            report.append("YOLOv8l模型性能表现良好，但仍有改进空间。")
        else:
            report.append("### 🔴 测试结论")
            report.append("YOLOv8l模型性能需要进一步优化。")

        report.append("")
        report.append("### 🚀 建议")
        report.append("1. **模型部署**: 可以将此模型部署到生产环境")
        report.append("2. **参数调优**: 可以尝试调整置信度和IoU阈值")
        report.append("3. **数据增强**: 考虑增加更多训练数据")
        report.append("4. **模型融合**: 考虑与其他模型进行融合")
        report.append("")

        # 技术细节
        report.append("## 🔧 技术细节")
        report.append("### 测试环境")
        report.append("- Python版本: 3.x")
        report.append("- PyTorch版本: 最新")
        report.append("- Ultralytics版本: 最新")
        report.append("- 设备: CUDA/CPU")
        report.append("")
        report.append("### 测试参数")
        report.append(f"- 置信度阈值: {model_info.get('conf_threshold', 0.25)}")
        report.append(f"- IoU阈值: {model_info.get('iou_threshold', 0.45)}")
        report.append("- 匹配IoU阈值: 0.5")
        report.append("")

        return "\n".join(report)

def main():
    """主函数"""
    print("🎯 YOLOv8l模型性能测试器")
    print("=" * 50)
    print("目标: 验证新导出的YOLOv8l ONNX模型性能")
    print("参数: conf_threshold=0.25, iou_threshold=0.45 (与AnyLabeling一致)")
    print("")
    
    tester = YOLOv8lPerformanceTester()
    
    # 运行性能测试
    results = tester.run_performance_test()
    
    if results:
        metrics = results.get('performance_metrics', {})
        
        print("\n📊 测试结果摘要:")
        print(f"   平均精确率: {metrics.get('avg_precision', 0):.3f}")
        print(f"   平均召回率: {metrics.get('avg_recall', 0):.3f}")
        print(f"   平均F1分数: {metrics.get('avg_f1', 0):.3f}")
        print(f"   平均推理时间: {metrics.get('avg_inference_time', 0):.4f}秒")
        print(f"   推理速度: {metrics.get('fps', 0):.1f} FPS")
        print(f"   测试图像数: {metrics.get('total_images_tested', 0)}")
        
        print(f"\n✅ 性能测试完成，详细报告已生成")
    else:
        print("❌ 性能测试失败")

if __name__ == "__main__":
    main()
