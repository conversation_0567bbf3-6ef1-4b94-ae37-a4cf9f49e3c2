#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
最终修复验证器

验证修复后的CardDetector是否彻底解决了类别映射问题。
"""

import os
import json
import numpy as np
from typing import Dict, List, Any, Tuple
from collections import defaultdict, Counter


class FinalFixVerifier:
    """最终修复验证器"""
    
    def __init__(self):
        """初始化验证器"""
        self.original_path = "legacy_assets/ceshi/calibration_gt/labels"
        self.fixed_path = "legacy_assets/ceshi/calibration_gt_fixed_mapping/labels"
        
        # 之前发现的错误映射
        self.known_errors = {
            "二": "三",
            "陆": "柒", 
            "拾": "暗"
        }
        
        print(f"🔍 最终修复验证器初始化")
        print(f"   - 原始标注: {self.original_path}")
        print(f"   - 修复后标注: {self.fixed_path}")
    
    def verify_final_fix(self, sample_size: int = 50) -> Dict[str, Any]:
        """验证最终修复效果"""
        print(f"🚀 开始验证最终修复效果...")
        
        # 获取共同文件
        original_files = set(f for f in os.listdir(self.original_path) if f.endswith('.json'))
        fixed_files = set(f for f in os.listdir(self.fixed_path) if f.endswith('.json'))
        
        common_files = list(original_files & fixed_files)[:sample_size]
        
        print(f"📋 验证 {len(common_files)} 个文件...")
        
        # 收集验证结果
        verification_results = {
            'label_errors': [],
            'position_errors': [],
            'missing_detections': [],
            'false_detections': [],
            'perfect_matches': []
        }
        
        for i, filename in enumerate(common_files):
            try:
                file_result = self._verify_single_file(filename)
                
                verification_results['label_errors'].extend(file_result['label_errors'])
                verification_results['position_errors'].extend(file_result['position_errors'])
                verification_results['missing_detections'].extend(file_result['missing_detections'])
                verification_results['false_detections'].extend(file_result['false_detections'])
                verification_results['perfect_matches'].extend(file_result['perfect_matches'])
                
                if (i + 1) % 10 == 0:
                    progress = (i + 1) / len(common_files) * 100
                    print(f"   进度: {i+1}/{len(common_files)} ({progress:.1f}%)")
                    
            except Exception as e:
                print(f"❌ 验证失败: {filename} - {e}")
        
        # 生成验证报告
        report = self._generate_verification_report(verification_results, len(common_files))
        
        print(f"✅ 最终修复验证完成")
        return report
    
    def _verify_single_file(self, filename: str) -> Dict[str, Any]:
        """验证单个文件"""
        # 读取原始和修复后标注
        original_data = self._load_json(os.path.join(self.original_path, filename))
        fixed_data = self._load_json(os.path.join(self.fixed_path, filename))
        
        # 提取检测结果
        original_detections = self._extract_detections(original_data)
        fixed_detections = self._extract_detections(fixed_data)
        
        # 分析各类错误
        label_errors = self._find_label_errors(original_detections, fixed_detections, filename)
        position_errors = self._find_position_errors(original_detections, fixed_detections, filename)
        missing_detections = self._find_missing_detections(original_detections, fixed_detections, filename)
        false_detections = self._find_false_detections(original_detections, fixed_detections, filename)
        perfect_matches = self._find_perfect_matches(original_detections, fixed_detections, filename)
        
        return {
            'file': filename,
            'label_errors': label_errors,
            'position_errors': position_errors,
            'missing_detections': missing_detections,
            'false_detections': false_detections,
            'perfect_matches': perfect_matches
        }
    
    def _load_json(self, filepath: str) -> Dict[str, Any]:
        """加载JSON文件"""
        with open(filepath, 'r', encoding='utf-8') as f:
            return json.load(f)
    
    def _extract_detections(self, data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """提取检测信息"""
        detections = []
        
        for shape in data.get('shapes', []):
            points = shape.get('points', [])
            if len(points) == 4:
                # 转换为[x, y, w, h]格式
                xs = [p[0] for p in points]
                ys = [p[1] for p in points]
                x, y = min(xs), min(ys)
                w, h = max(xs) - x, max(ys) - y
                
                detection = {
                    'label': shape.get('label', ''),
                    'bbox': [x, y, w, h],
                    'confidence': shape.get('score', 1.0),
                    'center': [x + w/2, y + h/2]
                }
                detections.append(detection)
        
        return detections
    
    def _find_label_errors(self, original_detections: List[Dict], 
                          fixed_detections: List[Dict], filename: str) -> List[Dict]:
        """找出标签错误"""
        errors = []
        used_fixed = set()
        
        for orig in original_detections:
            best_match = None
            best_iou = 0
            best_idx = -1
            
            for i, fixed in enumerate(fixed_detections):
                if i in used_fixed:
                    continue
                
                iou = self._calculate_iou(orig['bbox'], fixed['bbox'])
                if iou > best_iou and iou > 0.3:
                    best_iou = iou
                    best_match = fixed
                    best_idx = i
            
            if best_match and orig['label'] != best_match['label']:
                used_fixed.add(best_idx)
                errors.append({
                    'file': filename,
                    'original_label': orig['label'],
                    'fixed_label': best_match['label'],
                    'iou': best_iou,
                    'position': orig['center'],
                    'confidence': best_match['confidence']
                })
        
        return errors
    
    def _find_position_errors(self, original_detections: List[Dict], 
                            fixed_detections: List[Dict], filename: str) -> List[Dict]:
        """找出位置错误"""
        errors = []
        used_fixed = set()
        
        for orig in original_detections:
            best_match = None
            best_iou = 0
            best_idx = -1
            
            for i, fixed in enumerate(fixed_detections):
                if i in used_fixed:
                    continue
                
                iou = self._calculate_iou(orig['bbox'], fixed['bbox'])
                if iou > best_iou and iou > 0.1:
                    best_iou = iou
                    best_match = fixed
                    best_idx = i
            
            if best_match and best_iou < 0.7:  # 位置误差阈值
                used_fixed.add(best_idx)
                errors.append({
                    'file': filename,
                    'iou': best_iou,
                    'original_bbox': orig['bbox'],
                    'fixed_bbox': best_match['bbox'],
                    'label': orig['label']
                })
        
        return errors
    
    def _find_missing_detections(self, original_detections: List[Dict], 
                               fixed_detections: List[Dict], filename: str) -> List[Dict]:
        """找出漏检"""
        missing = []
        used_fixed = set()
        
        for orig in original_detections:
            found_match = False
            
            for i, fixed in enumerate(fixed_detections):
                if i in used_fixed:
                    continue
                
                iou = self._calculate_iou(orig['bbox'], fixed['bbox'])
                if iou > 0.3:
                    used_fixed.add(i)
                    found_match = True
                    break
            
            if not found_match:
                missing.append({
                    'file': filename,
                    'label': orig['label'],
                    'position': orig['center'],
                    'bbox': orig['bbox']
                })
        
        return missing
    
    def _find_false_detections(self, original_detections: List[Dict], 
                             fixed_detections: List[Dict], filename: str) -> List[Dict]:
        """找出误检"""
        false_detections = []
        used_original = set()
        
        for i, fixed in enumerate(fixed_detections):
            found_match = False
            
            for j, orig in enumerate(original_detections):
                if j in used_original:
                    continue
                
                iou = self._calculate_iou(fixed['bbox'], orig['bbox'])
                if iou > 0.3:
                    used_original.add(j)
                    found_match = True
                    break
            
            if not found_match:
                false_detections.append({
                    'file': filename,
                    'label': fixed['label'],
                    'position': fixed['center'],
                    'confidence': fixed['confidence']
                })
        
        return false_detections
    
    def _find_perfect_matches(self, original_detections: List[Dict], 
                            fixed_detections: List[Dict], filename: str) -> List[Dict]:
        """找出完美匹配"""
        perfect_matches = []
        used_fixed = set()
        
        for orig in original_detections:
            best_match = None
            best_iou = 0
            best_idx = -1
            
            for i, fixed in enumerate(fixed_detections):
                if i in used_fixed:
                    continue
                
                iou = self._calculate_iou(orig['bbox'], fixed['bbox'])
                if iou > best_iou and iou > 0.7:
                    best_iou = iou
                    best_match = fixed
                    best_idx = i
            
            if best_match and orig['label'] == best_match['label'] and best_iou > 0.8:
                used_fixed.add(best_idx)
                perfect_matches.append({
                    'file': filename,
                    'label': orig['label'],
                    'iou': best_iou,
                    'confidence': best_match['confidence']
                })
        
        return perfect_matches
    
    def _calculate_iou(self, bbox1: List[float], bbox2: List[float]) -> float:
        """计算IoU"""
        try:
            x1, y1, w1, h1 = bbox1
            x2, y2, w2, h2 = bbox2
            
            # 转换为 [x1, y1, x2, y2] 格式
            box1 = [x1, y1, x1 + w1, y1 + h1]
            box2 = [x2, y2, x2 + w2, y2 + h2]
            
            # 计算交集
            x_left = max(box1[0], box2[0])
            y_top = max(box1[1], box2[1])
            x_right = min(box1[2], box2[2])
            y_bottom = min(box1[3], box2[3])
            
            if x_right < x_left or y_bottom < y_top:
                return 0.0
            
            intersection = (x_right - x_left) * (y_bottom - y_top)
            
            # 计算并集
            area1 = w1 * h1
            area2 = w2 * h2
            union = area1 + area2 - intersection
            
            return intersection / union if union > 0 else 0.0
        
        except (ValueError, IndexError, ZeroDivisionError):
            return 0.0
    
    def _generate_verification_report(self, verification_results: Dict[str, Any], 
                                    total_files: int) -> Dict[str, Any]:
        """生成验证报告"""
        
        label_errors = verification_results['label_errors']
        position_errors = verification_results['position_errors']
        missing_detections = verification_results['missing_detections']
        false_detections = verification_results['false_detections']
        perfect_matches = verification_results['perfect_matches']
        
        # 统计错误类型
        label_error_types = Counter()
        for error in label_errors:
            mapping = f"{error['original_label']} -> {error['fixed_label']}"
            label_error_types[mapping] += 1
        
        # 分析已知错误状态
        known_error_status = {}
        for orig_label, wrong_label in self.known_errors.items():
            error_count = sum(1 for error in label_errors 
                            if error['original_label'] == orig_label and 
                               error['fixed_label'] == wrong_label)
            
            known_error_status[f"{orig_label} -> {wrong_label}"] = {
                'error_count': error_count,
                'fixed': error_count == 0
            }
        
        # 计算准确率
        total_detections = len(label_errors) + len(perfect_matches)
        accuracy = len(perfect_matches) / total_detections if total_detections > 0 else 0
        
        report = {
            'summary': {
                'total_files_verified': total_files,
                'label_errors': len(label_errors),
                'position_errors': len(position_errors),
                'missing_detections': len(missing_detections),
                'false_detections': len(false_detections),
                'perfect_matches': len(perfect_matches),
                'overall_accuracy': accuracy
            },
            'known_errors_status': known_error_status,
            'label_accuracy': {
                'total_label_errors': len(label_errors),
                'most_common_label_errors': label_error_types.most_common(10),
                'label_error_rate': len(label_errors) / total_files if total_files > 0 else 0
            },
            'fix_effectiveness': {
                'known_errors_fixed': all(status['fixed'] for status in known_error_status.values()),
                'accuracy_improvement': 'Significant' if accuracy > 0.95 else 'Moderate' if accuracy > 0.8 else 'Limited',
                'meets_99_percent_claim': accuracy >= 0.99
            }
        }
        
        # 保存报告
        report_path = "output/final_fix_verification_report.json"
        os.makedirs(os.path.dirname(report_path), exist_ok=True)
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        return report


def main():
    """主函数"""
    print("🔍 最终修复验证器")
    print("=" * 50)
    
    # 创建验证器
    verifier = FinalFixVerifier()
    
    # 验证最终修复效果
    report = verifier.verify_final_fix(sample_size=50)
    
    # 打印结果
    print("\n📊 最终验证结果:")
    print(f"   验证文件数: {report['summary']['total_files_verified']}")
    print(f"   标签错误: {report['summary']['label_errors']}")
    print(f"   位置错误: {report['summary']['position_errors']}")
    print(f"   漏检: {report['summary']['missing_detections']}")
    print(f"   误检: {report['summary']['false_detections']}")
    print(f"   完美匹配: {report['summary']['perfect_matches']}")
    print(f"   总体准确率: {report['summary']['overall_accuracy']:.1%}")
    
    print("\n🎯 已知错误修复状态:")
    for error_type, status in report['known_errors_status'].items():
        if status['fixed']:
            print(f"   {error_type}: ✅ 已修复")
        else:
            error_count = status['error_count']
            print(f"   {error_type}: ❌ 仍有{error_count}个错误")
    
    print("\n🏆 修复效果评估:")
    fix_effectiveness = report['fix_effectiveness']
    print(f"   已知错误全部修复: {'✅' if fix_effectiveness['known_errors_fixed'] else '❌'}")
    print(f"   准确率改善: {fix_effectiveness['accuracy_improvement']}")
    print(f"   达到99%声明: {'✅' if fix_effectiveness['meets_99_percent_claim'] else '❌'}")
    
    if report['label_accuracy']['most_common_label_errors']:
        print("\n❌ 剩余标签错误:")
        for error_type, count in report['label_accuracy']['most_common_label_errors'][:5]:
            print(f"   {error_type}: {count} 次")
    else:
        print("\n✅ 无标签错误！")
    
    print(f"\n📁 详细报告已保存: output/final_fix_verification_report.json")


if __name__ == "__main__":
    main()
