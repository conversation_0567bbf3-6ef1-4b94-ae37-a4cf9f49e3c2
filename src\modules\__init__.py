"""
数字孪生系统模块化架构
第一阶段：基础功能模块
第二阶段：扩展功能模块
"""

# 第一阶段模块
from .data_validator import create_data_validator, DataValidator
from .basic_id_assigner import create_basic_id_assigner, BasicIDAssigner
from .simple_inheritor import create_simple_inheritor, SimpleInheritor
from .phase1_integrator import create_phase1_integrator, Phase1Integrator

# 第二阶段模块
from .region2_processor import create_region2_processor, Region2Processor
from .region_transitioner import create_region_transitioner, RegionTransitioner
from .dark_card_processor import create_dark_card_processor, DarkCardProcessor
from .occlusion_compensator import create_occlusion_compensator, OcclusionCompensator
from .phase2_integrator import create_phase2_integrator, Phase2Integrator

# 卡牌尺寸启动控制模块
from .card_size_activation_controller import (
    create_card_size_activation_controller,
    CardSizeActivationController,
    CardSizeActivationConfig
)

__all__ = [
    # 第一阶段
    'create_data_validator',
    'DataValidator',
    'create_basic_id_assigner',
    'BasicIDAssigner',
    'create_simple_inheritor',
    'SimpleInheritor',
    'create_phase1_integrator',
    'Phase1Integrator',

    # 第二阶段
    'create_region2_processor',
    'Region2Processor',
    'create_region_transitioner',
    'RegionTransitioner',
    'create_dark_card_processor',
    'DarkCardProcessor',
    'create_occlusion_compensator',
    'OcclusionCompensator',
    'create_phase2_integrator',
    'Phase2Integrator',

    # 卡牌尺寸启动控制
    'create_card_size_activation_controller',
    'CardSizeActivationController',
    'CardSizeActivationConfig'
]
