#!/usr/bin/env python3
"""
开发过程14全面验证脚本
使用大量数据集进行双轨机制的全面验证
包括calibration_gt (372张图像) 和 zhuangtaiquyu数据集
"""

import sys
import os
import json
import glob
from datetime import datetime
from pathlib import Path
import shutil

# 添加项目路径
sys.path.insert(0, '.')

def count_files_in_directory(directory):
    """统计目录中的文件数量"""
    if not os.path.exists(directory):
        return 0
    return len([f for f in os.listdir(directory) if os.path.isfile(os.path.join(directory, f))])

def load_json_file(file_path):
    """安全加载JSON文件"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"   ⚠️ 无法加载JSON文件 {file_path}: {e}")
        return None

def simulate_detection_from_json(json_data, image_name):
    """从JSON标注数据模拟检测结果"""
    from src.core.digital_twin_v2 import CardDetection
    
    detections = []
    if not json_data or 'shapes' not in json_data:
        return detections
    
    for i, shape in enumerate(json_data['shapes']):
        try:
            label = shape.get('label', 'unknown')
            points = shape.get('points', [])
            
            if len(points) >= 2:
                # 计算边界框
                x_coords = [p[0] for p in points]
                y_coords = [p[1] for p in points]
                x1, y1 = min(x_coords), min(y_coords)
                x2, y2 = max(x_coords), max(y_coords)
                
                # 模拟置信度和区域分配
                confidence = 0.9 + (i % 10) * 0.01  # 0.9-0.99
                region_id = shape.get('group_id', 1)
                
                detection = CardDetection(
                    label=label,
                    bbox=[x1, y1, x2, y2],
                    confidence=confidence,
                    region_id=region_id,
                    region_name=f'region_{region_id}',
                    player_perspective='spectator'
                )
                detections.append(detection)
        except Exception as e:
            print(f"   ⚠️ 处理标注时出错: {e}")
            continue
    
    return detections

def main():
    print("🚀 开发过程14全面验证 - 大量数据集测试")
    print("=" * 80)
    
    try:
        # 导入模块
        print("1. 导入核心模块...")
        from src.core.digital_twin_v2 import create_digital_twin_system, CardDetection
        print("   ✅ 模块导入成功")
        
        # 创建数字孪生系统
        print("2. 创建数字孪生系统...")
        dt_system = create_digital_twin_system()
        print("   ✅ 数字孪生系统V2.0创建成功")
        
        # 检查双轨输出方法
        if not hasattr(dt_system, 'export_synchronized_dual_format'):
            print("   ❌ 双轨输出方法不存在")
            return False
        print("   ✅ 双轨输出方法存在")
        
        # 数据集统计
        print("\n3. 数据集规模统计...")
        calibration_gt_images = count_files_in_directory("legacy_assets/ceshi/calibration_gt/images")
        calibration_gt_labels = count_files_in_directory("legacy_assets/ceshi/calibration_gt/labels")
        
        print(f"   📊 calibration_gt数据集:")
        print(f"      图像数量: {calibration_gt_images}")
        print(f"      标注数量: {calibration_gt_labels}")
        
        # 统计zhuangtaiquyu数据集
        zhuangtaiquyu_base = "legacy_assets/ceshi/zhuangtaiquyu"
        zhuangtaiquyu_subdirs = []
        total_zhuangtaiquyu_images = 0
        total_zhuangtaiquyu_labels = 0
        
        if os.path.exists(f"{zhuangtaiquyu_base}/images/train"):
            for subdir in os.listdir(f"{zhuangtaiquyu_base}/images/train"):
                subdir_path = f"{zhuangtaiquyu_base}/images/train/{subdir}"
                if os.path.isdir(subdir_path):
                    image_count = count_files_in_directory(subdir_path)
                    label_count = count_files_in_directory(f"{zhuangtaiquyu_base}/labels/train/{subdir}")
                    zhuangtaiquyu_subdirs.append((subdir, image_count, label_count))
                    total_zhuangtaiquyu_images += image_count
                    total_zhuangtaiquyu_labels += label_count
        
        print(f"   📊 zhuangtaiquyu数据集:")
        print(f"      子目录数量: {len(zhuangtaiquyu_subdirs)}")
        print(f"      总图像数量: {total_zhuangtaiquyu_images}")
        print(f"      总标注数量: {total_zhuangtaiquyu_labels}")
        
        # 创建输出目录
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_dir = f"comprehensive_verification_output_{timestamp}"
        os.makedirs(output_dir, exist_ok=True)
        print(f"   📁 输出目录: {output_dir}")
        
        # 验证统计
        total_processed = 0
        total_successful = 0
        consistency_scores = []
        rlcard_outputs = []
        anylabeling_outputs = []
        
        print("\n4. 开始全面验证...")
        
        # 验证calibration_gt数据集 (采样验证，避免过长)
        print("   📋 验证calibration_gt数据集 (采样50张)...")
        calibration_sample_count = 0
        calibration_success_count = 0
        
        for i in range(0, min(calibration_gt_images, 372), 7):  # 每7张采样一次，约50张
            if calibration_sample_count >= 50:
                break
                
            image_name = f"frame_{i:05d}.jpg"
            label_name = f"frame_{i:05d}.json"
            
            image_path = f"legacy_assets/ceshi/calibration_gt/images/{image_name}"
            label_path = f"legacy_assets/ceshi/calibration_gt/labels/{label_name}"
            
            if not os.path.exists(image_path) or not os.path.exists(label_path):
                continue
            
            calibration_sample_count += 1
            total_processed += 1
            
            try:
                # 加载标注数据
                json_data = load_json_file(label_path)
                if not json_data:
                    continue
                
                # 模拟检测结果
                detections = simulate_detection_from_json(json_data, image_name)
                if not detections:
                    continue
                
                # 处理数据
                result = dt_system.process_frame(detections)
                
                # 双轨输出
                dual_result = dt_system.export_synchronized_dual_format(
                    result, 640, 480, image_name
                )
                
                # 记录结果
                consistency = dual_result['consistency_validation']
                score = consistency.get('consistency_score', 0)
                consistency_scores.append(score)
                
                rlcard_outputs.append(dual_result['rlcard_format'])
                anylabeling_outputs.append(dual_result['anylabeling_format'])
                
                calibration_success_count += 1
                total_successful += 1
                
                if calibration_sample_count % 10 == 0:
                    print(f"      处理进度: {calibration_sample_count}/50, 当前一致性: {score:.3f}")
                
            except Exception as e:
                print(f"      ⚠️ 处理 {image_name} 时出错: {e}")
                continue
        
        print(f"   ✅ calibration_gt验证完成: {calibration_success_count}/{calibration_sample_count}")
        
        # 验证zhuangtaiquyu数据集 (采样验证)
        print("   📋 验证zhuangtaiquyu数据集 (采样前3个子目录)...")
        zhuangtaiquyu_sample_count = 0
        zhuangtaiquyu_success_count = 0
        
        for subdir, image_count, label_count in zhuangtaiquyu_subdirs[:3]:  # 只验证前3个子目录
            print(f"      验证子目录 {subdir} (图像: {image_count}, 标注: {label_count})")
            
            subdir_sample_count = 0
            for i in range(0, min(image_count, 100), 5):  # 每5张采样一次
                if subdir_sample_count >= 20:  # 每个子目录最多20张
                    break
                
                image_name = f"frame_{i:05d}.jpg"
                label_name = f"frame_{i:05d}.json"
                
                image_path = f"{zhuangtaiquyu_base}/images/train/{subdir}/{image_name}"
                label_path = f"{zhuangtaiquyu_base}/labels/train/{subdir}/{label_name}"
                
                if not os.path.exists(image_path) or not os.path.exists(label_path):
                    continue
                
                subdir_sample_count += 1
                zhuangtaiquyu_sample_count += 1
                total_processed += 1
                
                try:
                    # 加载标注数据
                    json_data = load_json_file(label_path)
                    if not json_data:
                        continue
                    
                    # 模拟检测结果
                    detections = simulate_detection_from_json(json_data, image_name)
                    if not detections:
                        continue
                    
                    # 处理数据
                    result = dt_system.process_frame(detections)
                    
                    # 双轨输出
                    dual_result = dt_system.export_synchronized_dual_format(
                        result, 640, 480, f"{subdir}_{image_name}"
                    )
                    
                    # 记录结果
                    consistency = dual_result['consistency_validation']
                    score = consistency.get('consistency_score', 0)
                    consistency_scores.append(score)
                    
                    rlcard_outputs.append(dual_result['rlcard_format'])
                    anylabeling_outputs.append(dual_result['anylabeling_format'])
                    
                    zhuangtaiquyu_success_count += 1
                    total_successful += 1
                    
                except Exception as e:
                    print(f"         ⚠️ 处理 {subdir}/{image_name} 时出错: {e}")
                    continue
            
            print(f"         子目录 {subdir} 完成: {subdir_sample_count} 张图像")
        
        print(f"   ✅ zhuangtaiquyu验证完成: {zhuangtaiquyu_success_count}/{zhuangtaiquyu_sample_count}")
        
        # 统计分析
        print("\n5. 验证结果统计分析...")
        if consistency_scores:
            avg_consistency = sum(consistency_scores) / len(consistency_scores)
            min_consistency = min(consistency_scores)
            max_consistency = max(consistency_scores)
            high_quality_count = len([s for s in consistency_scores if s >= 0.95])
            
            print(f"   📊 一致性分析:")
            print(f"      总处理数量: {total_processed}")
            print(f"      成功处理数量: {total_successful}")
            print(f"      成功率: {(total_successful/total_processed)*100:.1f}%")
            print(f"      平均一致性分数: {avg_consistency:.3f}")
            print(f"      最低一致性分数: {min_consistency:.3f}")
            print(f"      最高一致性分数: {max_consistency:.3f}")
            print(f"      高质量输出(≥0.95): {high_quality_count}/{len(consistency_scores)} ({(high_quality_count/len(consistency_scores))*100:.1f}%)")
        
        # 保存验证结果
        print("\n6. 保存验证结果...")
        
        # 保存统计报告
        verification_report = {
            "timestamp": timestamp,
            "datasets": {
                "calibration_gt": {
                    "total_images": calibration_gt_images,
                    "sampled_images": calibration_sample_count,
                    "successful_processing": calibration_success_count
                },
                "zhuangtaiquyu": {
                    "total_subdirs": len(zhuangtaiquyu_subdirs),
                    "total_images": total_zhuangtaiquyu_images,
                    "sampled_images": zhuangtaiquyu_sample_count,
                    "successful_processing": zhuangtaiquyu_success_count
                }
            },
            "verification_results": {
                "total_processed": total_processed,
                "total_successful": total_successful,
                "success_rate": (total_successful/total_processed)*100 if total_processed > 0 else 0,
                "consistency_scores": {
                    "average": avg_consistency if consistency_scores else 0,
                    "minimum": min_consistency if consistency_scores else 0,
                    "maximum": max_consistency if consistency_scores else 0,
                    "high_quality_count": high_quality_count if consistency_scores else 0,
                    "high_quality_rate": (high_quality_count/len(consistency_scores))*100 if consistency_scores else 0
                }
            }
        }
        
        report_file = os.path.join(output_dir, "comprehensive_verification_report.json")
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(verification_report, f, ensure_ascii=False, indent=2)
        
        # 保存样本输出
        if rlcard_outputs:
            sample_rlcard_file = os.path.join(output_dir, "sample_rlcard_outputs.json")
            with open(sample_rlcard_file, 'w', encoding='utf-8') as f:
                json.dump(rlcard_outputs[:10], f, ensure_ascii=False, indent=2)  # 保存前10个样本
        
        if anylabeling_outputs:
            sample_anylabeling_file = os.path.join(output_dir, "sample_anylabeling_outputs.json")
            with open(sample_anylabeling_file, 'w', encoding='utf-8') as f:
                json.dump(anylabeling_outputs[:10], f, ensure_ascii=False, indent=2)  # 保存前10个样本
        
        print(f"   💾 验证报告已保存: {report_file}")
        print(f"   💾 样本输出已保存: {output_dir}/sample_*.json")
        
        # 最终结果
        print("\n🎯 全面验证结果总结")
        print("=" * 80)
        
        if consistency_scores and avg_consistency >= 0.95:
            print("🎉 双轨机制全面验证完全成功！")
            print(f"   ✅ 大量数据验证: {total_successful} 张图像成功处理")
            print(f"   ✅ 平均一致性分数: {avg_consistency:.3f} (目标: ≥0.95)")
            print(f"   ✅ 高质量输出率: {(high_quality_count/len(consistency_scores))*100:.1f}%")
            print(f"   ✅ 数据集覆盖: calibration_gt + zhuangtaiquyu")
            
            # 与开发过程14对比
            improvement = ((avg_consistency - 0.3) / 0.3) * 100
            print(f"\n📈 与开发过程14对比:")
            print(f"   问题: 之前一致性分数仅0.3")
            print(f"   解决: 现在平均一致性分数{avg_consistency:.3f}")
            print(f"   改进: 提升{improvement:.1f}%")
            print(f"   状态: ✅ 彻底解决StateBuilder黑盒问题")
            
            return True
        else:
            print("⚠️ 双轨机制验证存在改进空间")
            if consistency_scores:
                print(f"   📊 平均一致性: {avg_consistency:.3f} (目标: ≥0.95)")
                print(f"   📊 高质量率: {(high_quality_count/len(consistency_scores))*100:.1f}%")
            print(f"   📊 成功处理: {total_successful}/{total_processed}")
            return False
            
    except Exception as e:
        print(f"\n❌ 验证过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    print(f"\n{'='*80}")
    print(f"最终结果: {'🎉 全面验证成功' if success else '❌ 验证需要改进'}")
    sys.exit(0 if success else 1)
