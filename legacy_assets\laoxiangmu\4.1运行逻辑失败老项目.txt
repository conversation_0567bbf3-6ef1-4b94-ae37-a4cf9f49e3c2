这是python架构跑胡子棋牌类AI游戏采集训练决策程序。本程序为本地部署，电脑配置：13900HKES（20线程版禁用了超线程现在14线程）+ddr4-64GB+5060 8G+M.2nvme固态硬盘PCIE4.0 1T（本程序所在盘）+HDD硬盘4TB+USB2.0话筒+2.1音箱+2.0普通摄像头 win11操作系统。
整体程序工作流程（本流程是早其大概流程未及时更新仅做参考，最终目标一致）
graph TD（）
    %% ===== 初始化阶段 =====
    A[系统启动] --> B[安全验证]
    B --> B1[硬件检测]
    B1 --> B2[控制面板启动]  # ★修改★ 原"资源分配"升级为控制面板
    B2 --> C{运行模式选择}
    
    %% ===== 训练模式流程 =====
    C -->|训练模式| D[控制面板指令]
    D --> D1[资源隔离: 12线程+35GB]  # ★修改★ 由控制面板直接调用allocator.py
    D1 --> D2[关闭实战模块]  # ★新增★ 通过process_manager.py终止相关进程
    D2 --> D3[加载规则库]
    D3 --> E{是否增量训练?}
    E -->|是| E1[加载历史模型]
    E -->|否| E2[新建模型架构]
    E1 --> F[注入新训练数据]
    E2 --> F
    F --> G[规则强化训练]
    G --> G1[蒙特卡洛树搜索]
    G1 --> G11[CPU多线程并行]
    G --> G2[神经网络更新]
    G2 --> G21[GPU加速运算]
    G --> H[模型收敛检测]
    H --> H1[胜率验证]
    H1 -->|达标| I[生成新模型]
    H1 -->|未达标| G
    I --> I1[模型版本控制]
    I1 --> I2[保存到M.2固态]
    
    %% ===== 实战决策流程 =====
    C -->|实战模式| J[控制面板指令]  # ★修改★
    J --> J1[资源分配: 2线程+8GB]
    J1 --> J11[终止训练进程]  # ★新增★
    J --> K[输入系统启动]
    K --> K1[视频采集]
    K1 --> K11[屏幕捕获]
    K11 --> K12[牌面识别]
    K --> K2[音频采集]
    K2 --> K21[语音输入处理]
    K12 --> L[实时决策引擎]
    K21 --> L
    L --> L1[加载最新模型]
    L --> L2[特征提取]
    L --> L3[概率计算]
    L --> M[生成决策]
    M --> N[输出系统]
    N --> N1[语音播报]
    N --> N2[可视化反馈]
    N --> N3[文字回退]
    N --> O[执行动作]
    O --> O1[键盘控制]
    O --> O2[机械臂操作]
    
    %% ===== 数据闭环系统 =====
    O --> P[数据采集]
    K1 --> P
    K2 --> P
    P --> P1[关键帧存储]
    P1 --> P11[M.2临时缓存]
    P --> P2[胜负标注]
    P2 --> P21[自动标记系统]
    P --> Q[数据沉淀]
    Q --> Q1[HDD长期存储]
    Q --> Q2[外部素材整合]
    
    %% ===== 控制面板监控循环 =====  # ★新增章节★
    Q --> R{控制面板检测}
    R -->|数据>5GB| S[触发增量训练]
    R -->|定时触发| S
    S --> T[模型版本管理]
    T --> T1[新旧对抗验证]
    T1 --> T11[胜率对比]
    T --> U[最优模型部署]
    U --> J
    
    %% ===== 异常处理流程 =====
    S --> V{训练异常?}
    V -->|是| W[回滚机制]
    W --> W1[加载旧版本]
    W --> W2[记录到logger.py]
    V -->|否| U
    
    %% ===== 安全防护系统 =====
    B --> X[安全子系统]
    X --> X1[文件加密]
    X1 --> X11[AES-256加密]
    X --> X2[网络防护]
    X2 --> X21[防火墙]
    X2 --> X22[进程监控]
    X --> X3[访问控制]
    X3 --> X31[密码认证]
    X3 --> X32[USB防护]
    
    %% ===== 性能监控 =====
    D --> Y[训练监视器]
    Y --> Y1[控制面板资源监控]  # ★修改★ 原"资源占用"改为控制面板主动查询
    Y --> Y2[每10分钟清理]
    J --> Z[实战监控]
    Z --> Z1[延迟检测]
    Z --> Z2[故障转移]
    
    %% ===== 控制面板扩展接口 =====  # ★新增★
    style B2 stroke:#ffa500,stroke-width:3px
    style D stroke:#ffa500,stroke-width:3px
    style J stroke:#ffa500,stroke-width:3px
    style R stroke:#ffa500,stroke-width:3px
    
    %% ===== 未来扩展 =====
    N1 --> AA[声纹识别]
    K21 --> AA
    AA --> AB[语音控制]
    N1 --> AC[情感合成]
   

控制层：control_center.py + ai1.3.py

核心系统层：decision_engine/ + training/ + battle/

数据层：data/ + assets/

硬件抽象层：hardware/

配置中心：global_configs/公共配置文件 + core_configs/训练与实战相关私有配置文件 开发主要以硬编码路径，太复杂看不懂，绝对路径我可以修改

以下是参考结构目录，必须带main.py入口文件，英文文件名加中文注释，包括目录注释）最后更新时间20250705 2140
D:\project_root（跑胡子AI核心系统） 类型：混合分层架构（Layered Architecture） + 核心子系统模式
├── 【主程序】                   
├──         .github/
│                   └── workflows/
│                               └── ci.yml
│                                                                                      
├── ai1.3.py（原main.py） → 系统总开关（硬件检测/模式切换/安全启动）
├──    【ui】    →    主控制面板
│                     ├──  resources
│                     │           ├── 图片
│                     │           ├── 图片
│                     │
│                     └──  temporary
│                                 ├── main_window.py
│
├──  trainer.py
│      
├── 【核心系统】（完全保留原有结构）
│                ├──  core/ 核心模块region_state_assigner.py
│                     │           ├── control_center.py → 主控面板（模式切换/资源调度）
│                     │           └── path_resolver.py →  路径解析系统不会用，只能用绝对路径自已修改
│                     │
│                     ├── training（训练系统）按首写字母排序 
│                     │           ├── base_trainer.py → 基础训练支持类，用于模型保存、日志记录、ONNX导出等通用功能 为trainer.py扩展模块 相当于副控
│                     │           ├── trainer.py → 高层训练控制器（整合 UI、规则、版本控制）card_flow_analyzer.py
│                     │           ├── data_processing/（数据处理相关模块）⭐：已集成  ❌：取消集成  ⚠️：独立模块
│                     │           │        ⚠️ ├── action_labeler.py →根据规则、标注或推理结果，为每步/每帧自动生成动作标签（如"最佳动作/实际动作"）。输出：为每个样本生成 action 字段，支持多种动作类型（如出牌、碰、吃、胡等）。
│                     │           │        ⚠️ ├── annotation_parser.py →原始标注解析器 - 从AnyLabeling格式JSON提取cards信息支持group_id状态解析和卡牌类型识别
│                     │           │        ⚠️ ├── area_region_loader.py → 通用的底层工具类/数据加载类，不是特征融合的“业务模块”。被多个业务模块调用。从一个集中配置文件（如 paohuzi_labels.json）读取布局信息，并提供接口来查询区域坐标、校验点的位置。支持一个区域映射到多个group_id。
│                     │           │        ⭐ ├── card_tracker.py → 跨视频帧追踪卡牌实例 (Card Instance Tracker)。为首次出现的卡牌分配一个全局唯一的 ID (instance_id) 在后续的视频帧中，通过位置关联（IoU）来维持这个ID，即使卡牌移动 为系统提供"我是谁"的身份信息，是实现高级业务逻辑（如偎牌、跑牌）的基石
│                     │           │        ⭐ ├── card_feature_extractor.py  →  #特征提取器：将卡牌状态编码为模型 花色、点数、明暗、组合等细粒度特征提取 可用的特征向量 专注于特征工程不依赖任何训练好的模型 
│                     │           │        ⭐ ├── card_flow_analyzer.py  →  分析卡牌ID在整局/分局中的流动轨迹，输出每张牌的完整流动日志。已集成至dataset_builder.py 该模块的分析结果不会写入数据库，仅用于日志输出和后续分析，需要扩展该模块
│                     │           │            ├── dataset_builder.py → 单帧游戏画面表示包含图像路径、识别到的卡牌、HUD信息、当前状态等大融合最终构建构建 Dataset db文件
│                     │           │        ⚠️ ├── database_explorer.py → 工具将帮助"看到"数据库内容，解决"黑盒子"问题 迁移至tools目录内
│                     │           │        ⚠️ ├── dataset_validator.py → 数据集校验器：自动校验所有样本字段的完整性、一致性和分布合理 支持COCO格式、自定义JSON格式等多种数据集格式
│                     │           │        ⭐ ├── enhanced_feature_extractor.py → 高级规则特征：提取可胡、可碰、可吃、胡息、红牌等特征。
│                     │           │        ⚠️ ├── enhanced_dataset_builder.py → 数据集构建器分器 - 集成原始标注解析和卡牌识别支持从原始标注补充cards字段                  
│                     │           │        ❌ ├── hud_extractor.py → HUD信息提取器 用于从游戏画面中提取文字信息（玩家名、分数、倒计时等）融合至dataset_builder.py
│                     │           │        ⚠️ ├── image_augmenter.py → 图像增强器 使用 PyTorch 实现 GPU 加速增强 支持：翻转、旋转、亮度调整等 与data_augmenter.py类似功能，须确认是否重复功能 已确认data_augmenter.py已淘汰
│                     │           │        ⭐ ├── operation_history_builder.py →根据每帧/每步的状态、动作、牌面等信息，自动生成操作历史序列 输出为每个样本生成 history 字段，格式为动作序列或结构化历史记录。融合至dataset_builder.py
│                     │           │        ⭐ ├── region_state_assigner.py → 区域分配器：根据卡牌bbox中心点，调用AreaRegionLoader进行group_id和区域名称分配。至dataset_builder.py 20250704 新建拆分出的模块，此模块有模块间互相调用，需改为独立
│                     │           │        ⭐ ├── simplified_hud_extractor.py  → HUD信息提取器(简化版) 用于从游戏画面中提取文字、数字信息（结算、局数、胡息）融合至dataset_builder.py
│                     │           │        ⭐ ├── smart_video_processor.py → 视频抽成图片 功能去黑边、去重、预处理统一输出640 320分辨率图片
│                     │           │        ⭐ ├── spectator_assigner.py → 根据牌局结果（输/赢）来分配结算画面的归属（主角/对手）。结算专属画面中，该模块对 group_id, region_name, owner 的最终决定权。如结算画面将确定用1-12 16状态或13 14 15状态
│                     │           │        ⚠️ └── video_segmenter.py → 视频切割机，多整局单视频分割成单整局单视频，双轨交叉扫描,yolo8X模型类别判断，4阶段智能处理，确保准确率100%，此模块未集成主流程为独立脚本，需独立环境
│                     │           ├── tools/（独立工作的脚本）
│                     │           │            ├── yolo_retraining/（yolo专项脚本）
│                     │           │            │             ├── retrain_yolo_zengjia_tianjialeibie.py → 训练卡牌识别yolo模型添加新类别专用脚本 三级
│                     │           │            │             ├── retrain_yolo_zengjia.py  → 训练卡牌识别yolo模型添加新类别专用脚本 二级
│                     │           │            │             └── retrain_yolo.py  → 训练卡牌识别yolo模型添加新类别专用脚本 一级 完全整训练集训练 已产出6.0模型，现构建数据集流程在用5.0模型
│                     │           │            └── label_visualizer.py 定性：独立的、非常有价值的调试和可视化工具，强烈建议保留。
│                     │           ├── rule_engine/（规则引擎）此文件夹内只有precompiled_rules.py为最新yolo版，文件夹内容已合并至core/rule_engine/此文件夹将废弃10天内予以删除 20250704
│                     │           │            ├── regional_rules/# 训练数据（实战视频处理后）
│                     │           │            │             ├── loudi.yaml        # 娄底玩法 
│                     │           │            │             ├── changde.yaml      # 常德玩法 
│                     │           │            │             └── chenzhou.yaml     # 郴州玩法 
│                     │           │            ├── dynamic_loader.py  → # 新增：动态规则加载  
│                     │           │            ├── mcts_enhancer.py  →  # 蒙特卡洛优化 MCTS搜索树的基本节点结构 用于封装游戏状态和搜索信息
│                     │           │            ├── precompiled_rules.py →  功能：加载和管理基础规则库 支持跑胡子和麻将(预留)两种游戏类型 内置规则验证机制 dynamic_loader和rule_merger模块深度集成 支持2-4人游戏规则适配 完整安全检查和性能优化
│                     │           │            ├── precompiled_rules.yaml  →  此文件未完善，需改路径，放这里不行，专注于数据挖掘，与更新/合并逻辑解耦 输出 YAML 规则文件
│                     │           │            ├── rule_merger.py → 规则合并引擎 - 负责多地区跑胡子规则的智能合并与冲突解决 功能：支持基础规则与地区规则的深度合并 智能冲突检测与解决机制 版本控制与语义化版本管理 为麻将等扩展规则预留接口 支持2-4人游戏规则适配
│                     │           │            └── rule_miner.py → 跑胡子规则挖掘引擎 - V4.0完整版 功能：从实战数据中自动提取规则特征 支持多地区(郴州/常德/娄底)规则差异化学习 自动生成可读性强的YAML规则文件 支持2-4人玩法规则自适应 厂商UI布局自适应识别 与rule_manager和dynamic_loader的完整集成
│                     │           │
│                     │           ├── self_play/（自我博弈）
│                     │           │            ├── arena.py → 模型对抗竞技场
│                     │           │            └── elo_rater.py → 模型评分系统（ELO算法）
│                     │           │ 
│                     │           └── model_manager/（模型管理）
│                     │                        ├── version_control.py → 版本控制（语义化版本号）
│                     │                        ├── self_play_optimizer.py →  对抗结果、动作序列mcts_config.yaml
│                     │                        ├── strategy_injector.py → 规则库 + 高频动作mcts_enhancer.py 中
│                     │                        └── hot_swap.py → 热更新模块（不停机替换模型）
│                     │  
│                     ├── battle（实战系统）
│                     │           ├── decision_flow.py →实战主控模块
│                     │           └── realtime/（实时处理）
│                     │                        ├── card_detector.py  ← 卡牌检测核心 已加载由retrain_yolo.py产出yolov8x模型当前识别32个类别 5.0版本 20250630 0920
│                     │                        ├── enhanced_card_detector.py 增强版卡牌检测器 - 集成状态判断和位置跟踪 yolo新版本 20250630 0915 此脚本可能未导入，需确认20250630 0921
│                     │                        ├── frame_analyzer.py —— 提取游戏状态信息（手牌、出牌堆等）模版识别历史冗余20250630 0911
│                     │                        ├── manufacturer_adapter.py —— 厂商UI适配器模块 - 专为跑胡子游戏优化 模版识别历史冗余20250630 0912
│                     │                        ├── multi_state_detector.py —— 多帧状态检测器 模版识别历史冗余20250630 0911
│                     │                        ├── position_tracker.py —— 卡牌位置追踪器  模版识别历史冗余20250630 0911
│                     │                        ├── probability_calc.py —— 胜率与听牌计算器 模版识别历史冗余20250630 0913
│                     │                        └── realtime_processor.py  实时主控制器  模版识别历史冗余20250630 0911
│                     │
│                     ├── pipeline （场景系统）
│                     │           ├── batch_video_processor.py 功能：对导入视频素材进行全流程处理（训练用）
│                     │           │                               此模块应属trainer.py子模块提供场景支持
│                     │           │                                 要求可单独运行（或许UI要增加专属接口）
│                     │           ├── resource_manager.py 功能：为batch_video_processor.py与子模块提供GPU资源调配与配置文件加载等，主控装不下装这里
│                     │           ├── dataset_building_stage.py 负责数据集生成的关键阶段 为batch_video_processor.py提供支持
│                     │           ├── long_video_processor.py 功能：配合smart_video_processor.py处理超长视频模块不应出现在这个文件夹，未来可能删除，因为不会再有长视频，实现牌局切割，此脚本已淘汰
│                     │           ├── pipeline_stage.py 是整个管道架构的基石，为batch_video_processor.py提供管道支持
│                     │           ├── cache_manager.py 处理过程中的各步骤输出将缓存至内存中 优先在缓存中传递 避免重复的硬盘读取 由resource_manager.py调用 20250704 0011
│                     │           └── realtime_controller.py （未开发）功能：从屏幕捕获→识别→决策→执行动作
│                     │                                           此模块应属decision_flow.py子模块提供场景支持
│                     │                                           无需单独启动，直接对接decision_flow.py
│                     │                                         （之前大部分子模块已与decision_flow.py对接需修改或补充）
│                     │
│                     ├── data （）待补全此文件夹功能
│                     │                        ├── database_manager.py → 提供数据库的底层操作接口 数据库连接管理（SQLite）表结构定义（Videos、Frames、Detections 三张表） 由dataset_builder.py调用
│                     ├── rule_engine 此目录与D:\project_root\core\training\rule_engine 已合并20250704 0718 迁移对应文件后需要重新排序当前太乱
│                     │                        ├── backup/# 合并可能重复的文件
│                     │                        │             ├── rule_miner_old.py - 较老版本的规则自动提取模块
│                     │                        ├── regional_rules/# 训练数据（实战视频处理后）
│                     │                        │             ├── hu_patterns.yaml - 胡牌名堂与胡息计算 未完成
│                     │                        │             ├── changde.yaml      # 常德玩法 
│                     │                        │             ├── chenzhou.yaml     # 郴州玩法 
│                     │                        │             ├── loudi.yaml        # 娄底玩法 
│                     │                        │             ├── precompiled_rules.yaml - 规则映射表（供 AI 理解使用）可能与上层目录的文件相同
│                     │                        │             ├── rule_mapping.yaml - 牌型 → 动作 → 分数 映射关系
│                     │                        │             └── scoring_rules.yaml - 结算与打鸟规则 未完成 - 胡牌名堂与胡息计算 未完成
│                     │                        ├── dynamic_loader.py  → # 新增：动态规则加载 
│                     │                        ├── mcts_enhancer.py  →  # 蒙特卡洛优化 MCTS搜索树的基本节点结构 用于封装游戏状态和搜索信息
│                     │                        ├── precompiled_rules.py →  功能：加载和管理基础规则库 支持跑胡子和麻将(预留)两种游戏类型 内置规则验证机制 dynamic_loader和rule_merger模块深度集成 支持2-4人游戏规则适配 完整安全检查和性能优化
│                     │                        ├── precompiled_rules.yaml  →  此文件未完善，需改路径，放这里不行，专注于数据挖掘，与更新/合并逻辑解耦 输出 YAML 规则文件
│                     │                        ├── rule_merger.py → 规则合并引擎 - 负责多地区跑胡子规则的智能合并与冲突解决 功能：支持基础规则与地区规则的深度合并 智能冲突检测与解决机制 版本控制与语义化版本管理 为麻将等扩展规则预留接口 支持2-4人游戏规则适配
│                     │                        ├── rule_miner.py → 跑胡子规则挖掘引擎 - V4.0完整版 功能：从实战数据中自动提取规则特征 支持多地区(郴州/常德/娄底)规则差异化学习 自动生成可读性强的YAML规则文件 支持2-4人玩法规则自适应 厂商UI布局自适应识别 与rule_manager和dynamic_loader的完整集成
│                     │                        ├── hu_pattern_analyzer.py → 此模块内显示# rule_miner.py - 规则挖掘与胡息计算模块 V4.0 需要确认功能
│                     │                        ├── manual_annotations.yaml      →  未知用途，可能为历史冗余
│                     │                        ├── ocr_config.yaml → 未知用途，可能为历史冗余                   
│                     │                        ├── rule_loader.py → 新版本，加载所有规则文件到一个统一命名空间中 应为新版本？最后修改为20250625
│                     │                        └── video_miner.py → 视频规则挖掘模块 - 从视频中自动提取高频牌型组合 
│                     ├── interfaces/（输入输出）（此文件夹内模块为根目录移动至core文件夹）
│                     │                        ├── screen_recorder.py → 主入口
│                     │                        ├── input_handler.py → 麦克风输入（语音指令）
│                     │                        ├── output_engine.py → 语音输出（2.1声道）
│                     │                        ├── voice_synth.py → 语音合成（标准普通话）
│                     │                        ├── video.py → （屏幕录制）
│                     │                        ├── recording_controller.py → （屏幕控制面板）
│                     │                        ├── visual_feedback.py → 可视化反馈（牌桌高亮）
│                     │                        └── text_fallback.py → 文字回退（音频故障时启用）
│                     │  
│                     ├── hardware/    硬件控制（此文件夹内模块为根目录移动至core文件夹）
│                     │            ├── resources/（资源）
│                     │            │          ├── allocator.py → CPU/GPU分配器
│                     │            │          └── memory_lock.py → 内存锁定（训练专用）
│                     │            │           
│                     │            ├── monitors/（监控）
│                     │            │          ├── training_watcher.py → 训练监控（资源占用）
│                     │            │          ├── system_watcher.py → 系统监控（新增）
│                     │            │          └── battle_monitor.py → 实战监控（延迟检测）
│                     │            │
│                     │            └── devices/（设备）
│                     │                        ├── gpu_controller.py → GPU控制（CUDA加速）
│                     │                        ├── camera_adapter.py → 摄像头适配器模块
│                     │                        └── storage_rw.py → 存储优化（自动切换M.2/HDD）
│                     │ 
│                     └── core_configs/ （模块配置）（此配置文件专配置实战与训练core目录下模块）
│                                   ├── hardware_settings/（硬件配置）
│                                   │            ├── monitoring.yaml → 系统监控配置
│                                   │            ├── training_monitor.yaml → 训练监控配置
│                                   │            ├── gpu_config.yaml    gpu配置
│                                   │            ├── storage_config.yaml → 存储配置
│                                   │            ├── base_config.yaml → 基础配置
│                                   │            ├── training_mode.yaml → 训练模式（12线程+35GB
│                                   │            └── battle_mode.yaml → 实战模式（2线程+8GB）
│                                   ├── model_params
│                                   │            ├──  mcts_config.yaml     蒙特卡洛树搜索增强器配置文件
│                                   │            ├──  data.yaml    生成器支持自动读取类别
│                                   │            └── version_policy.yaml      模型版本控制配置文件
│                                   ├── runtime
│                                   │            ├── arena_config.yaml     模型对抗竞技场配置文件
│                                   │            ├── elo_config.yaml         模型评分系统配置文件
│                                   │            ├── prob_calc.yaml           胜率计算配置文
│                                   │            ├── data_loop.yaml            视频处理配置文件
│                                   │            ├── rule_update_policy.yaml  规则自动更新配置文件
│                                   │            ├── regional_rules.yaml     地区规则映射配置文件
│                                   │            └── hunan_2p.yaml         视频处理配置文件
│                                   └── 预留
│
├──  data/ ← 保存中间临时数据
│                      ├── processed/   ← 输出目录 此目录更新调整中 20250629 1652
│                      │                ├── project_data.db    ← 当前存放数据库文件位置，后期需要改路径 20250630 0855 数据集构建最终输出文件
│                      │                ├── detections/       ← 卡牌识别模块card_detector输出目录 此目录需要改位置，改后再进行更新 20250629 1712
│                      │                │              ├── 视频文件名1.json/     ← 存放卡牌识别模块card_detector输出json文件
│                      │                │              ├── 视频文件名2.json/     ← 存放卡牌识别模块card_detector输出json文件
│                      │                ├── augmented/     ← 存放增强后图像   
│                      │                │              ├── 根据视频名自动创建的文件夹/   ← 存放data_augmenter模块处理frame_utils预处理模块后图片（编号连续）
│                      │                │                          ├── 000000.jpg/  ← 存放data_augmenter模块处理frame_utils预处理模块后图片（编号连续）
│                      │                │                          ├── 000001.jpg/  ← 存放data_augmenter模块处理frame_utils预处理模块后图片（编号连续）
│                      │                │
│                      │                │
│                      │                └── xunlianshuchu/       ← yolo卡牌识别训练输出目录 20250630 0858
│                      │                               ├── images/
│                      │                               ├── labels/
│                      ├── biaozhuwenjian_yuanshi/     AnyLabeling人工标注文件共计1200套 以训练yolo模型6.0版，现用5.0版
│                      │              ├── images/          图片统一640 320
│                      │              │         └── train 共40个文件夹 前24个文件夹图片为人工标注含原始group_id状态 24个文件夹共约900套
│                      │              │                 ├── 1
│                      │              │                 │      ├── frame_00601.jpg
│                      │              │                 │      ├── frame_00651.jpg
│                      │              │                 │      ├── frame_00681.jpg
│                      │              │                 ├── 2
│                      │              │                 │      ├── frame_00601.jpg
│                      │              │                 │      ├── frame_00651.jpg
│                      │              │                 │      ├── frame_00681.jpg
│                      │              │                 ├── 24
│                      │              │                 └── 40
│                      │              └── labels/            标注文件  共计20个 文件名都是原图配套
│                      │                        └── train 共40个文件夹 前24个文件夹json文件为人工标注含原始group_id状态 24个文件夹共约900套
│                      │                                ├── 1
│                      │                                │      ├── frame_00601.json
│                      │                                │      ├── frame_00651.json
│                      │                                │      ├── frame_00681.json
│                      │                                ├── 2
│                      │                                │      ├── frame_00601.json
│                      │                                │      ├── frame_00651.json
│                      │                                │      ├── frame_00681.json
│                      │                                ├── 24
│                      │                                └── 40
│                      │                
│                      ├── 1_segmented_videos/        video_segmenter.py → 将D:\project_root\assets\videos\ram内多整局单视频分割成单整局单视频 据原视频名+模块缩写+单整局编号创建视屏名
│                      │              ├── segmentation_summary.log  →视频切割中实时产生的日志报告，当程序启动时会先扫描记录，避免重复处理同一视频
│                      │              ├── 20250624-10_seg_002.mp4
│                      │              ├── 20250625-1_seg_007.mp4
│                      │              ├── 20250624-9_seg_003.mp4
│                      ├── 2_sampled_frames/          smart_video_processor.py → 将同级目录内1_segmented_videos内视频抽成图片 输出目录，根据原视频名+模块缩写创建文件夹名字，图片按原视频顺序
│                      │              ├── 20250624-10_seg_002
│                      │              │         ├── frame_0000.jpg
│                      │              │         ├── frame_0001.jpg
│                      │              ├── 20250625-1_seg_007
│                      │              ├── 20250624-9_seg_003
│                      │
│
├──  【全程序配置】（此目录只配置文件夹全局）
│               ├── global_configs/
│                      ├── paths.yaml → 路径配置（含新增测试路径）
│                      ├── labels →
│                      │            └── paohuzi_labels.json  视频类别状态等映射文件
│                      │
│                      ├── runtime
│                      │            ├── data_loop.yaml   硬盘归档缓存器配置文件
│                      │            ├── input.yaml    可视化反馈配置
│                      │            ├── output.yaml  语音文字配置
│                      │            └── video.yaml   视频预处理配置文件
│                      ├── video.yaml 视频预处理配置文件batch_video_processor以下全部模块所有配置参数都由本文件进行调节
│                      ├── control_center.yam
│                      ├── data_loop.yaml                         （可能要改,原目录变化）
│                      ├── logging.yaml
│                      ├── mode_settings.yaml
│                      ├── network_config.yaml
│                      ├── system.yaml
│                      ├── system_config.yaml
│                      │ 
│                      └── model_params/（模型参数）
│                                    ├── mcts_config.yaml → 规则模型参数
│                                    ├── rule_based.yaml → 规则模型参数补充     
│                                    ├── label_rules.json  →  视频数据增强配置文件
│                                    └── nn_architecture.yaml → 神经网络结构
│                      
│   
├──  【素材储存】
│              ├──  assets/
│                     ├── juece_models/ # 模型库（训练产出）
│                     │            ├── current/           # 当前模型（M.2固态加速）
│                     │            ├── archive/           # 原始模型备份（HDD归档）
│                     │            ├── checkpoints/         ← 模型断点
│                     │            └── onnx/              ← ONNX 模型导出
│                     ├── kaipai_shibie_models/ # 模型库（训练产出）此路径未启用yolo模型在D:\project_root\data\processed\xunlianshuchu\train5.0\weights
│                     │            ├── current/           # 当前模型（M.2固态加速）
│                     │            ├── archive/           # 原始模型备份（HDD归档）
│                     │            ├── checkpoints/         ← 模型断点
│                     │            └── onnx/              ← ONNX 模型导出
│                     │
│                     │
│                     ├── training_data/# 训练数据（实战视频处理后）此文件夹是原来冗余文件夹后期内部需调整20250629 1655
│                     │
│                     ├── videos/# 视频资源（）
│                     │            ├── raw/               # 屏幕捕获的原始视频
│                     │            ├── archive/
│                     │            └── test/              # 测试用牌局录像
│                     │
│                     ├── sounds/# 音效资源  （暂不开发）
│                     │            ├── feedback/          # 系统反馈音效（success/error）（暂不开发）
│                     │            └── voices/            # 自定义语音包（暂不开发）
│                     │
│                     └── external/# 外部素材整合 此文件夹路径没有正确使用现在外部导入路径用的D:\project_root\assets\videos\ram目录 20250629 1658
│                                  └── imported/         # 多台电脑采集的外部数据
│    
│    
├── 【安全系统】（暂不深度开发,开发阶段用开发者模式）
│              ├── security/
│                    ├──  network_monitor.py → 网络状态检测（离线自动解锁）
│                    ├── crypto/（加密）
│                    │            ├── aes_engine.py → AES-256文件加密
│                    │            ├── crypto_config.yaml → 配置文件?
│                    │            ├── hw_key.bin → 密钥管理（绑定硬件指纹）系统生成
│                    │            └── memory_encryptor.py → 内存加密
│                    │
│                    ├── network_lock/（网络防护）           开发阶段取消此设定
│                    │            ├── firewall.py → 防火墙（白名单机制）    开发阶段取消
│                    │            ├── process_monitor.py → 进程监控（黑名单）       开发阶段取消
│                    │
│                    └── access_control/（访问控制）
│                                  ├── auth.py → 密码认证（PBKDF2加密）
│                                  ├── secure_boot.py → 启动验证（校验系统完整性）
│                                  └── usb_guard.py → USB管控（禁止外部设备）
│ 
├──  【外部设备】（暂不开发）预留
│                    └── external/  # 外部设备接口
│                                  ├── camera_adapter/        # 多品牌摄像头适配
│                                  └── arm_controller/        # 机械臂控制协议    
│   
└──  【工具模块】 
               ├── utilities/     utilities/目录，禁止新建utils/
                      ├── logger.py → 日志系统（分级记录）
                      ├── exception_handlers/（异常处理）
                      │             ├── training_errors.py → 训练异常（内存不足处理）
                      │             ├── circuit_breaker.py → 熔断?（备用决策流程）
                      │             └── battle_fallback.py → 实战回退（备用决策流程）
                      │                                                                                                 
                      └── debug_tools/（调试）
                                     ├── performance_profiler.py → 性能分析（火焰图生成）
                                     └── decision_visualizer.py → 决策可视化（牌局回放）


文件在电脑中的位置,新开发文件脚本与配置文件,必须带上文件位置

跑胡子AI系统核心需求文档

安全防护要求

全文件AES-256加密

网络连接时自动禁止所有文件访问

记录所有试图访问程序的进程

联网前需密码验证，离线状态自动解除锁定

性能隔离要求

实时决策时禁用训练模块

输入输出延迟需0.5秒内

录屏仅作实战数据采集用途

支持多台电脑录屏素材整合

核心闭环逻辑

实战数据采集 → 模型训练 → 决策优化 → 新数据采集
模块优先级

核心级：训练系统、决策系统

辅助级：视频/音频输入输出

训练模式规范

运行时仅保留：

基础硬件控制

性能监测（每10分钟强制内存清理）

强制占用：12CPU线程+35GB内存

实战交互要求

必须模块：

音频输入（麦克风）

音频输出（2.1声道）

视频输入（屏幕捕获+摄像头备用）

容灾方案：

输出内容：标准普通话，无需情感语调

核心规范三原则
代码风格

PEP 8基础 + Google风格docstring

类型注解全覆盖（Python 3.10+语法）

私有变量前缀_，常量全大写

模块通信

进程间消息采用JSON Schema验证（见global_configs/comm_protocol.yaml）

关键约束
性能红线：决策延迟<500ms（见tests/benchmark/decision_latency.py）

资源隔离：训练模式强制占用12线程+35GB（configs/runtime/training_prod.yaml）

安全基线：所有文件AES-256加密（security/crypto/aes_engine.py）
核心目标：70+模块全贯通

环境
| NVIDIA-SMI 576.52                 Driver Version: 576.52         CUDA Version: 12.9     |
|-----------------------------------------+------------------------+----------------------+
环境没问题| GPU  Name                  Driver-Model | Bus-Id          Disp.A | Volatile Uncorr. ECC |
| Fan  Temp   Perf          Pwr:Usage/Cap |           Memory-Usage | GPU-Util  Compute M. |
|                                         |                        |               MIG M. |
|=========================================+========================+======================|
|   0  NVIDIA GeForce RTX 5060      WDDM  |   00000000:01:00.0  On |                  N/A |
|  0%   49C    P0             26W /  142W |    1021MiB /   8151MiB |      0%      Default |
|                                         |                        |                  N/A |
+-----------------------------------------+------------------------+----------------------+
(venv) D:\project_root>python
Python 3.10.11 (tags/v3.10.11:7d4cc5a, Apr  5 2023, 00:38:17) [MSC v.1929 64 bit (AMD64)] on win32
Type "help", "copyright", "credits" or "license" for more information.
>>> import torch
>>> print(f"PyTorch版本: {torch.__version__}")
PyTorch版本: 2.8.0.dev20250525+cu128
>>> print(f"CUDA可用: {torch.cuda.is_available()}")
CUDA可用: True
>>> print(f"设备能力: {torch.cuda.get_device_capability(0)}")  # 应显示(12,0)
设备能力: (12, 0)
架构
Blackwell (GB206-250-A1)
CUDA 核心数
3840 个
Tensor Core
第五代（210 个）
RT Core
第四代（30 个）
显存容量
8GB GDDR7
显存带宽
448 GB/s
FP32 性能
15.11~19.18 TFLOPS
PCIe 接口
PCIe 5.0 x8
TDP
145W ~ 150W
编码器
第九代 NVENC + 第六代 NVDEC
DLSS 支持
DLSS 4 / Frame Generation
已进行10个小时训练，环境完美融合，显卡最高占用到40%已经达到pci4.0固态硬盘瓶颈，说明显卡与环境正常
目标
从任意视频源读取帧抽取 → 自动校正方向 → 裁剪黑边 → 缩放到指定尺寸640 320 → 识别卡牌 → 输出结果供训练模块使用
