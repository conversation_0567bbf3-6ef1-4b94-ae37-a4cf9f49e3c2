#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
zhuangtaiquyu数据集专项全面测试脚本

功能：
1. 区域状态分配验证 - 验证group_id分配的准确性
2. 数字孪生系统测试 - 验证物理卡牌ID追踪的稳定性  
3. 状态转换正确性验证 - 验证YOLO检测→RLCard状态的转换
4. 跨帧一致性测试 - 验证卡牌追踪的连续性

数据说明：
- zhuangtaiquyu数据集包含手工标注的状态区域（99%准确）和物理卡牌ID（90%准确）
- 与calibration_gt数据集无关联，独立测试
- 包含多个训练集子目录，每个包含图片和对应的JSON标注文件
"""

import os
import sys
import json
import cv2
import numpy as np
from pathlib import Path
from collections import defaultdict, Counter
from typing import Dict, List, Tuple, Any, Optional
import time
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from src.core.detect import CardDetector
from src.core.state_builder import StateBuilder
from src.core.data_validator import DataValidationPipeline


class ZhuangtaiquyuComprehensiveTest:
    """zhuangtaiquyu数据集专项全面测试类"""
    
    def __init__(self):
        """初始化"""
        self.base_path = Path("legacy_assets/ceshi/zhuangtaiquyu")
        self.detector = None
        self.state_builder = None
        self.validator = None
        
        # 测试结果存储
        self.test_results = {
            'region_assignment': {},  # 区域分配测试结果
            'digital_twin': {},       # 数字孪生测试结果
            'state_conversion': {},   # 状态转换测试结果
            'cross_frame': {},        # 跨帧一致性测试结果
            'statistics': {}          # 统计信息
        }
        
        # 统计信息
        self.statistics = {
            'total_datasets': 0,
            'total_frames': 0,
            'total_detections': 0,
            'total_ground_truth': 0,
            'region_accuracy': {},
            'digital_twin_accuracy': {},
            'state_conversion_accuracy': {},
            'processing_time': {}
        }
        
        # 区域ID映射（基于GAME_RULES_OPTIMIZED.md）
        self.region_mapping = {
            1: "手牌_观战方",
            2: "调整手牌_观战方", 
            3: "抓牌_观战方",
            4: "打牌_观战方",
            5: "弃牌_观战方",
            6: "吃碰区_观战方",
            7: "抓牌_对战方",
            8: "打牌_对战方", 
            9: "弃牌_对战方",
            10: "弹窗提示_观战方",
            11: "透明提示_观战方",
            12: "听牌区_观战方",
            13: "底牌区域",
            14: "赢方区域",
            15: "输方区域", 
            16: "吃碰区_对战方"
        }
        
    def setup(self):
        """设置测试环境"""
        print("🔧 设置zhuangtaiquyu专项测试环境...")
        
        # 初始化检测器
        model_path = "best.pt"
        if os.path.exists(model_path):
            self.detector = CardDetector(model_path, enable_validation=False)  # 禁用验证层获取原始结果
            print("✅ 检测器初始化成功")
            
            # 模型预热
            dummy_image = np.zeros((320, 640, 3), dtype=np.uint8)
            _ = self.detector.detect_image(dummy_image)
            print("✅ 模型预热完成")
        else:
            raise FileNotFoundError(f"模型文件不存在: {model_path}")
        
        # 初始化状态构建器
        self.state_builder = StateBuilder()
        print("✅ 状态构建器初始化成功")
        
        # 初始化验证器
        self.validator = DataValidationPipeline()
        print("✅ 验证器初始化成功")
        
        # 检查数据集
        self.check_dataset_structure()
        
    def check_dataset_structure(self):
        """检查数据集结构"""
        print("\n📁 检查zhuangtaiquyu数据集结构...")
        
        images_path = self.base_path / "images" / "train"
        labels_path = self.base_path / "labels" / "train"
        
        if not images_path.exists():
            raise FileNotFoundError(f"图片目录不存在: {images_path}")
        if not labels_path.exists():
            raise FileNotFoundError(f"标注目录不存在: {labels_path}")
        
        # 统计数据集
        dataset_dirs = [d for d in images_path.iterdir() if d.is_dir()]
        self.statistics['total_datasets'] = len(dataset_dirs)
        
        total_frames = 0
        for dataset_dir in dataset_dirs:
            frames = list(dataset_dir.glob("*.jpg"))
            total_frames += len(frames)
            print(f"   数据集 {dataset_dir.name}: {len(frames)} 帧")
        
        self.statistics['total_frames'] = total_frames
        print(f"✅ 数据集检查完成: {len(dataset_dirs)} 个数据集, 共 {total_frames} 帧")
        
    def load_ground_truth_annotation(self, dataset_id: str, frame_name: str) -> Optional[Dict]:
        """加载真实标注数据"""
        json_file = self.base_path / "labels" / "train" / dataset_id / f"{frame_name}.json"
        
        if not json_file.exists():
            return None
        
        try:
            with open(json_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            return data
        except Exception as e:
            print(f"   ⚠️ 加载标注失败 {json_file}: {e}")
            return None
            
    def extract_ground_truth_cards(self, annotation: Dict) -> List[Dict]:
        """从标注中提取卡牌信息"""
        cards = []
        
        if 'shapes' not in annotation:
            return cards
        
        for shape in annotation['shapes']:
            if shape.get('shape_type') == 'rectangle':
                card_info = {
                    'label': shape.get('label', ''),
                    'group_id': shape.get('group_id', 0),
                    'bbox': shape.get('points', []),
                    'region_name': self.region_mapping.get(shape.get('group_id', 0), f"未知区域_{shape.get('group_id', 0)}")
                }
                cards.append(card_info)
        
        return cards
        
    def calculate_bbox_overlap(self, bbox1: List, bbox2: List) -> float:
        """计算两个边界框的重叠度（IoU）"""
        try:
            # bbox格式: [[x1, y1], [x2, y2]] 或 [x1, y1, x2, y2]
            if isinstance(bbox1[0], list):
                x1_1, y1_1 = bbox1[0]
                x2_1, y2_1 = bbox1[1]
            else:
                x1_1, y1_1, x2_1, y2_1 = bbox1
                
            if isinstance(bbox2[0], list):
                x1_2, y1_2 = bbox2[0]
                x2_2, y2_2 = bbox2[1]
            else:
                x1_2, y1_2, x2_2, y2_2 = bbox2
            
            # 计算交集
            x1_inter = max(x1_1, x1_2)
            y1_inter = max(y1_1, y1_2)
            x2_inter = min(x2_1, x2_2)
            y2_inter = min(y2_1, y2_2)
            
            if x2_inter <= x1_inter or y2_inter <= y1_inter:
                return 0.0
            
            inter_area = (x2_inter - x1_inter) * (y2_inter - y1_inter)
            
            # 计算并集
            area1 = (x2_1 - x1_1) * (y2_1 - y1_1)
            area2 = (x2_2 - x1_2) * (y2_2 - y1_2)
            union_area = area1 + area2 - inter_area
            
            if union_area <= 0:
                return 0.0
            
            return inter_area / union_area
            
        except Exception as e:
            print(f"   ⚠️ 计算IoU失败: {e}")
            return 0.0
            
    def match_detections_with_ground_truth(self, detections: List[Dict], ground_truth: List[Dict], iou_threshold: float = 0.5) -> Tuple[List, List, List]:
        """匹配检测结果与真实标注"""
        matched_pairs = []
        unmatched_detections = list(detections)
        unmatched_ground_truth = list(ground_truth)
        
        # 为每个检测结果寻找最佳匹配
        for det in detections:
            best_match = None
            best_iou = 0.0
            best_gt_idx = -1
            
            det_bbox = [det.get('x1', 0), det.get('y1', 0), det.get('x2', 0), det.get('y2', 0)]
            
            for gt_idx, gt in enumerate(unmatched_ground_truth):
                gt_bbox = gt['bbox']
                iou = self.calculate_bbox_overlap(det_bbox, gt_bbox)
                
                if iou > best_iou and iou >= iou_threshold:
                    best_iou = iou
                    best_match = gt
                    best_gt_idx = gt_idx
            
            if best_match:
                matched_pairs.append({
                    'detection': det,
                    'ground_truth': best_match,
                    'iou': best_iou
                })
                unmatched_ground_truth.pop(best_gt_idx)
                if det in unmatched_detections:
                    unmatched_detections.remove(det)
        
        return matched_pairs, unmatched_detections, unmatched_ground_truth

    def test_region_assignment_accuracy(self, dataset_id: str, max_frames: int = 50) -> Dict:
        """测试区域分配准确性"""
        print(f"\n🎯 测试数据集 {dataset_id} 的区域分配准确性...")

        images_path = self.base_path / "images" / "train" / dataset_id
        if not images_path.exists():
            print(f"   ❌ 数据集不存在: {dataset_id}")
            return {}

        frame_files = sorted(list(images_path.glob("*.jpg")))[:max_frames]

        region_stats = defaultdict(lambda: {'correct': 0, 'total': 0, 'errors': []})
        overall_stats = {'correct_assignments': 0, 'total_assignments': 0, 'accuracy': 0.0}

        for frame_file in frame_files:
            frame_name = frame_file.stem
            print(f"   🔍 处理帧: {frame_name}")

            # 加载图片
            image = cv2.imread(str(frame_file))
            if image is None:
                print(f"      ❌ 无法读取图片: {frame_file}")
                continue

            # YOLO检测
            start_time = time.time()
            detections = self.detector.detect_image(image)
            detection_time = time.time() - start_time

            # 加载真实标注
            annotation = self.load_ground_truth_annotation(dataset_id, frame_name)
            if not annotation:
                print(f"      ⚠️ 无标注文件: {frame_name}")
                continue

            ground_truth_cards = self.extract_ground_truth_cards(annotation)

            # 匹配检测结果与真实标注
            matched_pairs, unmatched_detections, unmatched_ground_truth = self.match_detections_with_ground_truth(
                detections, ground_truth_cards, iou_threshold=0.3
            )

            # 分析区域分配准确性
            for pair in matched_pairs:
                det = pair['detection']
                gt = pair['ground_truth']

                # 使用state_builder进行区域分配
                det_with_region = self.assign_region_to_detection(det, image.shape)
                predicted_region = det_with_region.get('group_id', 0)
                actual_region = gt['group_id']

                region_name = self.region_mapping.get(actual_region, f"未知区域_{actual_region}")
                region_stats[region_name]['total'] += 1
                overall_stats['total_assignments'] += 1

                if predicted_region == actual_region:
                    region_stats[region_name]['correct'] += 1
                    overall_stats['correct_assignments'] += 1
                else:
                    error_info = {
                        'frame': frame_name,
                        'card_label': det.get('label', 'unknown'),
                        'predicted_region': predicted_region,
                        'actual_region': actual_region,
                        'predicted_name': self.region_mapping.get(predicted_region, f"未知区域_{predicted_region}"),
                        'actual_name': region_name
                    }
                    region_stats[region_name]['errors'].append(error_info)

            print(f"      ✅ 检测: {len(detections)}, 标注: {len(ground_truth_cards)}, 匹配: {len(matched_pairs)}, 用时: {detection_time:.3f}s")

        # 计算准确率
        if overall_stats['total_assignments'] > 0:
            overall_stats['accuracy'] = overall_stats['correct_assignments'] / overall_stats['total_assignments']

        for region_name, stats in region_stats.items():
            if stats['total'] > 0:
                stats['accuracy'] = stats['correct'] / stats['total']
            else:
                stats['accuracy'] = 0.0

        result = {
            'dataset_id': dataset_id,
            'overall_stats': overall_stats,
            'region_stats': dict(region_stats),
            'frames_tested': len(frame_files)
        }

        self.test_results['region_assignment'][dataset_id] = result
        return result

    def assign_region_to_detection(self, detection: Dict, image_shape: Tuple) -> Dict:
        """为检测结果分配区域ID"""
        # 这里使用简化的区域分配逻辑，基于位置
        # 实际项目中应该使用state_builder的逻辑

        x1 = detection.get('x1', 0)
        y1 = detection.get('y1', 0)
        x2 = detection.get('x2', 0)
        y2 = detection.get('y2', 0)

        center_x = (x1 + x2) / 2
        center_y = (y1 + y2) / 2

        height, width = image_shape[:2]

        # 简化的区域分配规则（基于位置）
        detection_with_region = detection.copy()

        # 根据Y坐标分配区域
        if center_y < height * 0.2:
            # 上方区域 - 对战方
            if center_x < width * 0.3:
                detection_with_region['group_id'] = 16  # 吃碰区_对战方
            else:
                detection_with_region['group_id'] = 9   # 弃牌_对战方
        elif center_y > height * 0.7:
            # 下方区域 - 观战方
            if center_x < width * 0.8:
                detection_with_region['group_id'] = 1   # 手牌_观战方
            else:
                detection_with_region['group_id'] = 5   # 弃牌_观战方
        else:
            # 中间区域
            if center_x < width * 0.3:
                detection_with_region['group_id'] = 6   # 吃碰区_观战方
            else:
                detection_with_region['group_id'] = 4   # 打牌_观战方

        return detection_with_region

    def test_digital_twin_consistency(self, dataset_id: str, max_frames: int = 30) -> Dict:
        """测试数字孪生系统的一致性"""
        print(f"\n🔗 测试数据集 {dataset_id} 的数字孪生一致性...")

        images_path = self.base_path / "images" / "train" / dataset_id
        if not images_path.exists():
            print(f"   ❌ 数据集不存在: {dataset_id}")
            return {}

        frame_files = sorted(list(images_path.glob("*.jpg")))[:max_frames]

        # 跟踪卡牌的物理ID一致性
        card_tracking = defaultdict(list)  # physical_id -> [frame_detections]
        consistency_stats = {'consistent_cards': 0, 'inconsistent_cards': 0, 'total_cards': 0}

        for frame_file in frame_files:
            frame_name = frame_file.stem
            print(f"   🔍 处理帧: {frame_name}")

            # 加载真实标注
            annotation = self.load_ground_truth_annotation(dataset_id, frame_name)
            if not annotation:
                continue

            ground_truth_cards = self.extract_ground_truth_cards(annotation)

            # 分析物理ID一致性
            for card in ground_truth_cards:
                # 从标签中提取物理ID（假设格式为 "数字+卡牌名称"，如 "1壹", "2三"）
                label = card['label']
                if label and len(label) > 1 and label[0].isdigit():
                    physical_id = label[0]
                    card_name = label[1:]

                    card_tracking[physical_id].append({
                        'frame': frame_name,
                        'card_name': card_name,
                        'region': card['group_id'],
                        'bbox': card['bbox']
                    })

        # 分析一致性
        for physical_id, detections in card_tracking.items():
            consistency_stats['total_cards'] += 1

            # 检查同一物理ID的卡牌名称是否一致
            card_names = [det['card_name'] for det in detections]
            unique_names = set(card_names)

            if len(unique_names) == 1:
                consistency_stats['consistent_cards'] += 1
            else:
                consistency_stats['inconsistent_cards'] += 1
                print(f"      ⚠️ 物理ID {physical_id} 不一致: {unique_names}")

        # 计算一致性率
        if consistency_stats['total_cards'] > 0:
            consistency_rate = consistency_stats['consistent_cards'] / consistency_stats['total_cards']
        else:
            consistency_rate = 0.0

        result = {
            'dataset_id': dataset_id,
            'consistency_stats': consistency_stats,
            'consistency_rate': consistency_rate,
            'card_tracking': dict(card_tracking),
            'frames_tested': len(frame_files)
        }

        self.test_results['digital_twin'][dataset_id] = result
        return result

    def test_state_conversion_accuracy(self, dataset_id: str, max_frames: int = 20) -> Dict:
        """测试状态转换准确性"""
        print(f"\n🔄 测试数据集 {dataset_id} 的状态转换准确性...")

        images_path = self.base_path / "images" / "train" / dataset_id
        if not images_path.exists():
            print(f"   ❌ 数据集不存在: {dataset_id}")
            return {}

        frame_files = sorted(list(images_path.glob("*.jpg")))[:max_frames]

        conversion_stats = {
            'successful_conversions': 0,
            'failed_conversions': 0,
            'total_frames': 0,
            'state_quality_scores': []
        }

        for frame_file in frame_files:
            frame_name = frame_file.stem
            print(f"   🔍 处理帧: {frame_name}")

            # 加载图片
            image = cv2.imread(str(frame_file))
            if image is None:
                continue

            try:
                # YOLO检测
                detections = self.detector.detect_image(image)

                # 状态转换
                game_state = self.state_builder.yolo_to_game_state(detections)

                # 评估状态质量
                quality_score = self.evaluate_state_quality(game_state, detections)
                conversion_stats['state_quality_scores'].append(quality_score)

                if quality_score > 0.5:  # 阈值可调
                    conversion_stats['successful_conversions'] += 1
                else:
                    conversion_stats['failed_conversions'] += 1

                conversion_stats['total_frames'] += 1

                print(f"      ✅ 检测: {len(detections)}, 状态质量: {quality_score:.3f}")

            except Exception as e:
                print(f"      ❌ 状态转换失败: {e}")
                conversion_stats['failed_conversions'] += 1
                conversion_stats['total_frames'] += 1

        # 计算平均质量分数
        if conversion_stats['state_quality_scores']:
            avg_quality = sum(conversion_stats['state_quality_scores']) / len(conversion_stats['state_quality_scores'])
        else:
            avg_quality = 0.0

        # 计算成功率
        if conversion_stats['total_frames'] > 0:
            success_rate = conversion_stats['successful_conversions'] / conversion_stats['total_frames']
        else:
            success_rate = 0.0

        result = {
            'dataset_id': dataset_id,
            'conversion_stats': conversion_stats,
            'average_quality': avg_quality,
            'success_rate': success_rate,
            'frames_tested': len(frame_files)
        }

        self.test_results['state_conversion'][dataset_id] = result
        return result

    def evaluate_state_quality(self, game_state: Dict, detections: List[Dict]) -> float:
        """评估游戏状态质量"""
        if not game_state or not detections:
            return 0.0

        quality_factors = []

        # 1. 检测数量合理性（跑胡子通常有20-30张牌）
        detection_count = len(detections)
        if 10 <= detection_count <= 40:
            quality_factors.append(1.0)
        elif 5 <= detection_count <= 50:
            quality_factors.append(0.7)
        else:
            quality_factors.append(0.3)

        # 2. 状态结构完整性
        required_keys = ['hand', 'public', 'actions']
        structure_score = sum(1 for key in required_keys if key in game_state) / len(required_keys)
        quality_factors.append(structure_score)

        # 3. 卡牌标签有效性
        valid_labels = 0
        total_labels = 0
        for det in detections:
            label = det.get('label', '')
            total_labels += 1
            if label and label != 'unknown' and len(label) > 0:
                valid_labels += 1

        if total_labels > 0:
            label_validity = valid_labels / total_labels
        else:
            label_validity = 0.0
        quality_factors.append(label_validity)

        # 计算综合质量分数
        if quality_factors:
            return sum(quality_factors) / len(quality_factors)
        else:
            return 0.0

    def run_comprehensive_test(self, max_datasets: int = 3, max_frames_per_test: int = 30):
        """运行全面测试"""
        print("\n🚀 开始zhuangtaiquyu数据集全面测试")
        print("="*60)

        start_time = time.time()

        # 获取所有数据集
        images_path = self.base_path / "images" / "train"
        dataset_dirs = [d.name for d in images_path.iterdir() if d.is_dir()][:max_datasets]

        print(f"📊 将测试 {len(dataset_dirs)} 个数据集: {dataset_dirs}")

        # 对每个数据集进行测试
        for dataset_id in dataset_dirs:
            print(f"\n" + "="*40)
            print(f"📁 测试数据集: {dataset_id}")
            print("="*40)

            # 1. 区域分配测试
            region_result = self.test_region_assignment_accuracy(dataset_id, max_frames_per_test)

            # 2. 数字孪生测试
            twin_result = self.test_digital_twin_consistency(dataset_id, max_frames_per_test)

            # 3. 状态转换测试
            state_result = self.test_state_conversion_accuracy(dataset_id, max_frames_per_test // 2)

            # 汇总结果
            print(f"\n📊 数据集 {dataset_id} 测试结果:")
            if region_result:
                print(f"   区域分配准确率: {region_result['overall_stats']['accuracy']:.1%}")
            if twin_result:
                print(f"   数字孪生一致性: {twin_result['consistency_rate']:.1%}")
            if state_result:
                print(f"   状态转换成功率: {state_result['success_rate']:.1%}")

        total_time = time.time() - start_time
        self.statistics['processing_time']['total'] = total_time

        # 生成综合报告
        self.generate_comprehensive_report()

        print(f"\n🎉 全面测试完成！总用时: {total_time:.1f}秒")

    def generate_comprehensive_report(self):
        """生成综合测试报告"""
        print("\n📋 生成综合测试报告...")

        report = {
            'test_timestamp': datetime.now().isoformat(),
            'test_summary': {
                'total_datasets_tested': len(self.test_results['region_assignment']),
                'total_processing_time': self.statistics['processing_time'].get('total', 0)
            },
            'region_assignment_results': self.test_results['region_assignment'],
            'digital_twin_results': self.test_results['digital_twin'],
            'state_conversion_results': self.test_results['state_conversion'],
            'overall_statistics': self.statistics
        }

        # 计算总体指标
        region_accuracies = []
        twin_consistencies = []
        state_success_rates = []

        for dataset_id, result in self.test_results['region_assignment'].items():
            if 'overall_stats' in result:
                region_accuracies.append(result['overall_stats']['accuracy'])

        for dataset_id, result in self.test_results['digital_twin'].items():
            twin_consistencies.append(result['consistency_rate'])

        for dataset_id, result in self.test_results['state_conversion'].items():
            state_success_rates.append(result['success_rate'])

        # 添加总体统计
        if region_accuracies:
            report['test_summary']['average_region_accuracy'] = sum(region_accuracies) / len(region_accuracies)
        if twin_consistencies:
            report['test_summary']['average_twin_consistency'] = sum(twin_consistencies) / len(twin_consistencies)
        if state_success_rates:
            report['test_summary']['average_state_success'] = sum(state_success_rates) / len(state_success_rates)

        # 保存报告
        report_file = f"zhuangtaiquyu_comprehensive_test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)

        print(f"✅ 测试报告已保存: {report_file}")

        # 打印关键指标
        print(f"\n📊 关键测试指标:")
        if region_accuracies:
            print(f"   平均区域分配准确率: {report['test_summary']['average_region_accuracy']:.1%}")
        if twin_consistencies:
            print(f"   平均数字孪生一致性: {report['test_summary']['average_twin_consistency']:.1%}")
        if state_success_rates:
            print(f"   平均状态转换成功率: {report['test_summary']['average_state_success']:.1%}")


if __name__ == "__main__":
    print("🚀 zhuangtaiquyu数据集专项全面测试")
    print("="*60)

    try:
        # 创建测试实例
        test = ZhuangtaiquyuComprehensiveTest()

        # 设置环境
        test.setup()

        # 运行全面测试
        test.run_comprehensive_test(max_datasets=2, max_frames_per_test=20)

    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
