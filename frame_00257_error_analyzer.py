#!/usr/bin/env python3
"""
Frame_00257.jpg 错误分析脚本

专门用于分析frame_00257.jpg中"2叁"→"2伍"错误的根本原因
验证之前分析的几个可能原因：
1. 缺少专门的7→9流转处理机制
2. 继承机制中的标签匹配问题  
3. 模块间协调问题
4. ID分配机制中的类别混乱
5. 帧间状态管理缺陷
"""

import os
import sys
import json
import re
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
import logging

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 导入项目核心模块
from src.modules.phase2_integrator import Phase2Integrator

# 配置日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('frame_00257_analysis.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class Frame00257ErrorAnalyzer:
    """Frame_00257.jpg错误分析器"""
    
    def __init__(self):
        self.source_dir = "legacy_assets/ceshi/calibration_gt"
        self.output_dir = "output/calibration_gt_final_with_digital_twin"
        self.analysis_results = {}
        
    def analyze_error(self):
        """执行完整的错误分析"""
        logger.info("🔍 开始Frame_00257.jpg错误分析...")
        
        # 1. 分析现有日志输出
        self._analyze_existing_logs()
        
        # 2. 分析原始数据
        self._analyze_original_data()
        
        # 3. 重新处理frame_00256和frame_00257
        self._reprocess_target_frames()
        
        # 4. 验证具体错误原因
        self._verify_error_causes()
        
        # 5. 生成分析报告
        self._generate_analysis_report()
        
    def _analyze_existing_logs(self):
        """分析现有的处理日志"""
        logger.info("📋 分析现有处理日志...")
        
        # 查找可能的日志文件
        log_patterns = [
            "*.log",
            "calibration_gt_final_processor.log",
            "phase2_integrator.log"
        ]
        
        log_files = []
        for pattern in log_patterns:
            log_files.extend(Path(".").glob(pattern))
            
        if not log_files:
            logger.warning("⚠️ 未找到现有日志文件")
            return
            
        # 分析日志中关于frame_00256和frame_00257的信息
        frame_logs = {"frame_00256": [], "frame_00257": []}
        
        for log_file in log_files:
            try:
                with open(log_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                    
                # 提取相关帧的日志
                for frame_name in frame_logs.keys():
                    pattern = rf'.*{frame_name}.*'
                    matches = re.findall(pattern, content, re.MULTILINE | re.IGNORECASE)
                    frame_logs[frame_name].extend(matches)
                    
            except Exception as e:
                logger.warning(f"读取日志文件失败 {log_file}: {e}")
                
        # 记录分析结果
        self.analysis_results["existing_logs"] = frame_logs
        
        for frame_name, logs in frame_logs.items():
            if logs:
                logger.info(f"📋 {frame_name} 相关日志: {len(logs)} 条")
                for log in logs[:5]:  # 只显示前5条
                    logger.info(f"  {log}")
            else:
                logger.warning(f"⚠️ 未找到 {frame_name} 相关日志")
                
    def _analyze_original_data(self):
        """分析原始标注数据"""
        logger.info("📊 分析原始标注数据...")
        
        frames_to_analyze = ["frame_00256", "frame_00257"]
        original_data = {}
        
        for frame_name in frames_to_analyze:
            label_file = Path(self.source_dir) / "labels" / f"{frame_name}.json"
            
            if label_file.exists():
                try:
                    with open(label_file, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                        original_data[frame_name] = data
                        
                    # 分析卡牌分布
                    shapes = data.get("shapes", [])
                    card_analysis = self._analyze_frame_cards(frame_name, shapes)
                    
                    logger.info(f"📊 {frame_name} 原始数据分析:")
                    logger.info(f"  总shapes: {len(shapes)}")
                    logger.info(f"  卡牌分布: {card_analysis}")
                    
                except Exception as e:
                    logger.error(f"读取原始数据失败 {frame_name}: {e}")
            else:
                logger.warning(f"⚠️ 原始标注文件不存在: {label_file}")
                
        self.analysis_results["original_data"] = original_data
        
    def _analyze_frame_cards(self, frame_name: str, shapes: List[Dict]) -> Dict[str, Any]:
        """分析单帧的卡牌分布"""
        analysis = {
            "total_shapes": len(shapes),
            "cards_by_region": {},
            "labels_found": [],
            "target_cards": []  # 重点关注的卡牌
        }
        
        for shape in shapes:
            label = shape.get("label", "")
            group_id = shape.get("group_id", 0)
            
            # 统计各区域卡牌
            if group_id not in analysis["cards_by_region"]:
                analysis["cards_by_region"][group_id] = []
            analysis["cards_by_region"][group_id].append(label)
            
            # 收集所有标签
            if label:
                analysis["labels_found"].append(label)
                
            # 重点关注"叁"和"伍"相关的卡牌
            if "叁" in label or "伍" in label or "三" in label or "五" in label:
                analysis["target_cards"].append({
                    "label": label,
                    "group_id": group_id,
                    "bbox": shape.get("points", [])
                })
                
        return analysis
        
    def _reprocess_target_frames(self):
        """重新处理目标帧以获取详细信息"""
        logger.info("🔄 重新处理frame_00256和frame_00257...")
        
        # 创建Phase2Integrator实例
        integrator = Phase2Integrator()
        
        frames_to_process = ["frame_00256", "frame_00257"]
        processing_results = {}
        
        for frame_name in frames_to_process:
            logger.info(f"🔄 处理 {frame_name}...")
            
            # 读取原始数据
            label_file = Path(self.source_dir) / "labels" / f"{frame_name}.json"
            if not label_file.exists():
                logger.warning(f"⚠️ 跳过不存在的文件: {label_file}")
                continue
                
            try:
                with open(label_file, 'r', encoding='utf-8') as f:
                    original_data = json.load(f)
                    
                # 转换为检测格式
                detections = self._convert_to_detections(original_data)
                
                # 处理
                result = integrator.process_detections(detections, frame_name)
                processing_results[frame_name] = result
                
                # 详细分析处理结果
                self._analyze_processing_result(frame_name, result)
                
            except Exception as e:
                logger.error(f"处理 {frame_name} 失败: {e}")
                
        self.analysis_results["processing_results"] = processing_results
        
    def _convert_to_detections(self, original_data: Dict) -> List[Dict]:
        """将原始标注转换为检测格式"""
        detections = []
        
        for shape in original_data.get("shapes", []):
            label = shape.get("label", "")
            group_id = shape.get("group_id", 0)
            points = shape.get("points", [])
            
            if len(points) == 4:
                # 计算bbox
                x_coords = [p[0] for p in points]
                y_coords = [p[1] for p in points]
                bbox = [min(x_coords), min(y_coords), max(x_coords), max(y_coords)]
                
                detection = {
                    "label": label,
                    "bbox": bbox,
                    "confidence": 1.0,
                    "group_id": group_id
                }
                detections.append(detection)
                
        return detections
        
    def _analyze_processing_result(self, frame_name: str, result: Any):
        """分析处理结果的详细信息"""
        logger.info(f"📈 分析 {frame_name} 处理结果...")
        
        if hasattr(result, 'digital_twin_cards'):
            cards = result.digital_twin_cards
            logger.info(f"  数字孪生卡牌: {len(cards)} 张")
            
            # 重点分析"叁"和"伍"相关的卡牌
            target_cards = []
            for card in cards:
                label = getattr(card, 'label', '') if hasattr(card, 'label') else card.get('label', '')
                twin_id = getattr(card, 'twin_id', '') if hasattr(card, 'twin_id') else card.get('twin_id', '')
                group_id = getattr(card, 'group_id', 0) if hasattr(card, 'group_id') else card.get('group_id', 0)
                
                if "叁" in label or "伍" in label or "三" in label or "五" in label:
                    target_cards.append({
                        "label": label,
                        "twin_id": twin_id,
                        "group_id": group_id
                    })
                    
            if target_cards:
                logger.info(f"  目标卡牌分析:")
                for card in target_cards:
                    logger.info(f"    {card}")
            else:
                logger.warning(f"  ⚠️ 未找到目标卡牌（叁/伍）")
                
        # 分析统计信息
        if hasattr(result, 'statistics'):
            stats = result.statistics
            logger.info(f"  处理统计: {stats}")
            
    def _verify_error_causes(self):
        """验证具体的错误原因"""
        logger.info("🔍 验证错误原因...")
        
        verification_results = {
            "missing_7_to_9_handler": self._check_missing_7_to_9_handler(),
            "inheritance_matching_issue": self._check_inheritance_matching(),
            "module_coordination_issue": self._check_module_coordination(),
            "id_assignment_confusion": self._check_id_assignment_confusion(),
            "frame_state_management": self._check_frame_state_management()
        }
        
        self.analysis_results["verification_results"] = verification_results
        
        # 输出验证结果
        for cause, result in verification_results.items():
            status = "✅ 确认" if result["confirmed"] else "❌ 排除"
            logger.info(f"{status} {cause}: {result['description']}")
            
    def _check_missing_7_to_9_handler(self) -> Dict[str, Any]:
        """检查是否缺少7→9流转处理机制"""
        # 检查RegionTransitioner中是否有专门的7→9处理方法
        try:
            from src.modules.region_transitioner import RegionTransitioner
            transitioner = RegionTransitioner()
            
            # 检查是否有_handle_special_7_to_9_transition方法
            has_special_handler = hasattr(transitioner, '_handle_special_7_to_9_transition')
            
            return {
                "confirmed": not has_special_handler,
                "description": f"7→9专门处理方法存在: {has_special_handler}",
                "details": {
                    "has_special_handler": has_special_handler,
                    "available_methods": [m for m in dir(transitioner) if m.startswith('_handle_special')]
                }
            }
        except Exception as e:
            return {
                "confirmed": False,
                "description": f"检查失败: {e}",
                "details": {}
            }
            
    def _check_inheritance_matching(self) -> Dict[str, Any]:
        """检查继承机制的标签匹配问题"""
        try:
            from src.modules.simple_inheritor import SimpleInheritor

            # 检查跨区域继承规则
            inheritor = SimpleInheritor()

            # 模拟检查9区域的继承规则
            cross_region_rules = {
                9: [7, 8],  # 弃牌区可以从抓牌区、打牌区继承
            }

            details = {
                "cross_region_rules_for_9": cross_region_rules.get(9, []),
                "has_cross_region_method": hasattr(inheritor, '_try_cross_region_inheritance'),
                "has_extract_label_method": hasattr(inheritor, '_extract_original_label')
            }

            # 检查是否存在标签提取问题
            confirmed = False
            description = "继承机制检查完成"

            if not details["has_cross_region_method"]:
                confirmed = True
                description = "缺少跨区域继承方法"
            elif not details["has_extract_label_method"]:
                confirmed = True
                description = "缺少标签提取方法"

            return {
                "confirmed": confirmed,
                "description": description,
                "details": details
            }
        except Exception as e:
            return {
                "confirmed": False,
                "description": f"检查失败: {e}",
                "details": {}
            }

    def _check_module_coordination(self) -> Dict[str, Any]:
        """检查模块间协调问题"""
        try:
            from src.modules.phase2_integrator import Phase2Integrator

            integrator = Phase2Integrator()

            # 检查模块处理顺序
            processing_modules = []
            if hasattr(integrator, 'inheritor'):
                processing_modules.append("SimpleInheritor")
            if hasattr(integrator, 'transitioner'):
                processing_modules.append("RegionTransitioner")
            if hasattr(integrator, 'id_assigner'):
                processing_modules.append("BasicIdAssigner")

            details = {
                "available_modules": processing_modules,
                "has_process_detections": hasattr(integrator, 'process_detections'),
                "module_count": len(processing_modules)
            }

            confirmed = len(processing_modules) < 3  # 应该至少有3个核心模块
            description = f"模块协调检查: 发现{len(processing_modules)}个模块"

            return {
                "confirmed": confirmed,
                "description": description,
                "details": details
            }
        except Exception as e:
            return {
                "confirmed": False,
                "description": f"检查失败: {e}",
                "details": {}
            }

    def _check_id_assignment_confusion(self) -> Dict[str, Any]:
        """检查ID分配机制中的类别混乱"""
        try:
            from src.modules.basic_id_assigner import BasicIdAssigner

            assigner = BasicIdAssigner()

            # 检查ID分配相关方法
            details = {
                "has_assign_method": hasattr(assigner, 'assign_ids'),
                "has_available_ids_method": hasattr(assigner, '_get_available_ids'),
                "has_label_mapping": hasattr(assigner, 'label_to_chinese_mapping')
            }

            # 检查是否可能存在"叁"和"伍"的混淆
            confirmed = False
            description = "ID分配机制检查完成"

            if hasattr(assigner, 'label_to_chinese_mapping'):
                mapping = getattr(assigner, 'label_to_chinese_mapping', {})
                if '三' in mapping and '五' in mapping:
                    details["san_wu_mapping"] = {
                        "三": mapping.get('三'),
                        "五": mapping.get('五')
                    }

            return {
                "confirmed": confirmed,
                "description": description,
                "details": details
            }
        except Exception as e:
            return {
                "confirmed": False,
                "description": f"检查失败: {e}",
                "details": {}
            }

    def _check_frame_state_management(self) -> Dict[str, Any]:
        """检查帧间状态管理缺陷"""
        try:
            from src.modules.simple_inheritor import SimpleInheritor

            inheritor = SimpleInheritor()

            # 检查帧间状态管理
            details = {
                "has_previous_frame_mapping": hasattr(inheritor, 'previous_frame_mapping'),
                "has_update_mapping_method": hasattr(inheritor, '_update_previous_frame_mapping'),
                "mapping_type": type(getattr(inheritor, 'previous_frame_mapping', None)).__name__
            }

            confirmed = not details["has_previous_frame_mapping"]
            description = f"帧间状态管理检查: mapping存在={details['has_previous_frame_mapping']}"

            return {
                "confirmed": confirmed,
                "description": description,
                "details": details
            }
        except Exception as e:
            return {
                "confirmed": False,
                "description": f"检查失败: {e}",
                "details": {}
            }
        
    def _generate_analysis_report(self):
        """生成分析报告"""
        logger.info("📋 生成分析报告...")
        
        report = {
            "analysis_time": datetime.now().isoformat(),
            "target_frames": ["frame_00256", "frame_00257"],
            "error_description": "frame_00257.jpg中9区域显示'2伍'而不是预期的'2叁'",
            "analysis_results": self.analysis_results,
            "conclusions": self._draw_conclusions(),
            "recommended_fixes": self._recommend_fixes()
        }
        
        # 保存报告
        report_file = "frame_00257_error_analysis_report.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
            
        logger.info(f"📋 分析报告已保存: {report_file}")
        
        # 输出关键结论
        logger.info("🎯 关键结论:")
        for conclusion in report["conclusions"]:
            logger.info(f"  • {conclusion}")
            
        logger.info("🔧 推荐修复方案:")
        for fix in report["recommended_fixes"]:
            logger.info(f"  • {fix}")
            
    def _draw_conclusions(self) -> List[str]:
        """得出分析结论"""
        conclusions = []
        
        # 基于验证结果得出结论
        verification = self.analysis_results.get("verification_results", {})
        
        for cause, result in verification.items():
            if result.get("confirmed", False):
                conclusions.append(f"确认问题: {cause} - {result['description']}")
                
        if not conclusions:
            conclusions.append("需要更深入的分析来确定根本原因")
            
        return conclusions
        
    def _recommend_fixes(self) -> List[str]:
        """推荐修复方案"""
        fixes = [
            "添加专门的7→9流转处理方法",
            "增强继承机制的标签匹配逻辑",
            "改进帧间状态管理机制",
            "添加更详细的调试日志",
            "实施端到端的流转测试"
        ]
        return fixes

def main():
    """主函数"""
    print("🔍 Frame_00257.jpg 错误分析工具")
    print("=" * 50)
    
    analyzer = Frame00257ErrorAnalyzer()
    
    try:
        analyzer.analyze_error()
        print("\n✅ 分析完成！请查看生成的报告文件。")
        
    except Exception as e:
        logger.error(f"分析过程中发生错误: {e}")
        logger.exception("详细错误信息:")

if __name__ == "__main__":
    main()
