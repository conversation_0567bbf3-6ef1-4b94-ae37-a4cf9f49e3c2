"""
修正calibration_gt_enhanced数据集的区域分配逻辑

问题：
1. 给非卡牌类别（如"打鸟选择"、"已准备"、"吃"、"碰"、"胡"等）错误分配了区域
2. 只应该给21个有效卡牌类别分配区域：一、二、三、四、五、六、七、八、九、十 + 暗

修正策略：
1. 定义21个有效卡牌类别
2. 只为这些类别且缺少区域信息的卡牌分配区域
3. 非卡牌类别保持group_id为null
"""

import json
import os
from pathlib import Path
from typing import Dict, List, Any, Set
from collections import Counter

class CalibrationGTFixer:
    """calibration_gt增强数据集修正器"""
    
    def __init__(self):
        self.enhanced_path = Path("legacy_assets/ceshi/calibration_gt_enhanced/labels")
        self.original_path = Path("legacy_assets/ceshi/calibration_gt/labels")
        
        # 定义21个有效卡牌类别
        self.valid_card_categories = {
            "一", "二", "三", "四", "五", "六", "七", "八", "九", "十", "暗",
            "壹", "贰", "叁", "肆", "伍", "陆", "柒", "捌", "玖", "拾"  # 繁体版本
        }
        
        # 非卡牌类别（不应该分配区域）
        self.non_card_categories = {
            "打鸟选择", "已准备", "吃", "碰", "胡", "过", "听", "杠", "摸", "出",
            "选择", "确定", "取消", "等待", "思考", "计时", "分数", "结算"
        }
        
    def is_valid_card_label(self, label: str) -> bool:
        """判断是否是有效的卡牌标签"""
        if not label:
            return False
        
        # 处理带数字前缀的标签（如"1八"、"2四"）
        import re
        match = re.match(r'^(\d+)(.+)$', label)
        if match:
            card_name = match.group(2)
        else:
            card_name = label
        
        # 检查是否在有效卡牌类别中
        return card_name in self.valid_card_categories
    
    def analyze_current_issues(self) -> Dict[str, Any]:
        """分析当前增强数据集的问题"""
        print("🔍 分析当前增强数据集的问题...")
        
        issues = {
            "non_card_with_regions": [],  # 非卡牌类别被分配了区域
            "cards_without_regions": [],  # 卡牌类别缺少区域
            "inconsistent_assignments": [],  # 不一致的分配
            "statistics": {}
        }
        
        json_files = list(self.enhanced_path.glob("*.json"))
        
        total_shapes = 0
        non_card_with_region_count = 0
        card_without_region_count = 0
        
        for json_file in json_files[:20]:  # 分析前20个文件
            try:
                with open(json_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                for shape in data.get("shapes", []):
                    total_shapes += 1
                    label = shape.get("label", "")
                    group_id = shape.get("group_id")
                    
                    is_valid_card = self.is_valid_card_label(label)
                    
                    if not is_valid_card and group_id is not None and group_id != 0:
                        # 非卡牌类别被分配了区域
                        non_card_with_region_count += 1
                        issues["non_card_with_regions"].append({
                            "file": json_file.name,
                            "label": label,
                            "group_id": group_id
                        })
                    
                    elif is_valid_card and (group_id is None or group_id == 0):
                        # 卡牌类别缺少区域
                        card_without_region_count += 1
                        issues["cards_without_regions"].append({
                            "file": json_file.name,
                            "label": label,
                            "group_id": group_id
                        })
                        
            except Exception as e:
                print(f"    ❌ 处理文件{json_file.name}时出错: {e}")
        
        issues["statistics"] = {
            "total_shapes": total_shapes,
            "non_card_with_region_count": non_card_with_region_count,
            "card_without_region_count": card_without_region_count
        }
        
        print(f"📊 问题统计:")
        print(f"  总标注数: {total_shapes}")
        print(f"  非卡牌被分配区域: {non_card_with_region_count}")
        print(f"  卡牌缺少区域: {card_without_region_count}")
        
        if issues["non_card_with_regions"]:
            print(f"\n❌ 错误分配区域的非卡牌类别:")
            label_counts = Counter(item["label"] for item in issues["non_card_with_regions"])
            for label, count in label_counts.most_common(10):
                print(f"    {label}: {count}次")
        
        return issues
    
    def fix_enhanced_dataset(self) -> bool:
        """修正增强数据集"""
        print("\n🔧 修正增强数据集...")
        
        # 导入多算法融合分类器
        try:
            import sys
            sys.path.append(str(Path(__file__).parent.parent))
            from src.core.multi_algorithm_region_classifier import MultiAlgorithmRegionClassifier
            
            # 创建分类器并训练
            classifier = MultiAlgorithmRegionClassifier()
            
            # 从原始数据收集有效卡牌的训练数据
            print("📊 收集有效卡牌训练数据...")
            training_count = self._collect_valid_card_training_data(classifier)
            
            if training_count > 0:
                classifier.train_ml_classifier()
                print(f"✅ 分类器训练完成，使用{training_count}个有效卡牌样本")
            else:
                print("⚠️ 无有效训练数据，使用默认分类器")
            
        except Exception as e:
            print(f"❌ 分类器初始化失败: {e}")
            return False
        
        # 修正所有文件
        json_files = list(self.enhanced_path.glob("*.json"))
        fixed_files = 0
        
        for json_file in json_files:
            try:
                # 读取增强文件
                with open(json_file, 'r', encoding='utf-8') as f:
                    enhanced_data = json.load(f)
                
                # 读取原始文件进行对比
                original_file = self.original_path / json_file.name
                original_data = {}
                if original_file.exists():
                    with open(original_file, 'r', encoding='utf-8') as f:
                        original_data = json.load(f)
                
                # 修正每个shape
                modified = False
                for i, shape in enumerate(enhanced_data.get("shapes", [])):
                    label = shape.get("label", "")
                    current_group_id = shape.get("group_id")
                    
                    # 获取原始的group_id
                    original_group_id = None
                    if i < len(original_data.get("shapes", [])):
                        original_shape = original_data["shapes"][i]
                        if original_shape.get("label") == label:
                            original_group_id = original_shape.get("group_id")
                    
                    is_valid_card = self.is_valid_card_label(label)
                    
                    if not is_valid_card:
                        # 非卡牌类别：恢复为原始状态（通常是null）
                        if current_group_id != original_group_id:
                            shape["group_id"] = original_group_id
                            if "region_name" in shape and original_group_id is None:
                                del shape["region_name"]
                            modified = True
                    
                    elif is_valid_card and (original_group_id is None or original_group_id == 0):
                        # 有效卡牌且原始缺少区域：使用分类器分配
                        if len(shape.get("points", [])) >= 4:
                            points = shape["points"]
                            x1, y1 = points[0]
                            x2, y2 = points[2]
                            bbox = [x1, y1, x2, y2]
                            
                            predicted_region, confidence = classifier.classify_region(bbox)
                            
                            if confidence > 0.5:  # 只有置信度足够高才分配
                                shape["group_id"] = predicted_region
                                shape["region_name"] = f"region_{predicted_region}"
                                modified = True
                
                # 保存修正后的文件
                if modified:
                    with open(json_file, 'w', encoding='utf-8') as f:
                        json.dump(enhanced_data, f, ensure_ascii=False, indent=2)
                    fixed_files += 1
                    
            except Exception as e:
                print(f"    ❌ 修正文件{json_file.name}时出错: {e}")
        
        print(f"✅ 修正完成，共修正{fixed_files}个文件")
        return True
    
    def _collect_valid_card_training_data(self, classifier) -> int:
        """收集有效卡牌的训练数据"""
        training_count = 0
        
        # 从原始calibration_gt收集训练数据
        original_files = list(self.original_path.glob("*.json"))
        
        for json_file in original_files[:100]:  # 使用前100个文件
            try:
                with open(json_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                image_width = data.get("imageWidth", 640)
                image_height = data.get("imageHeight", 320)
                
                for shape in data.get("shapes", []):
                    label = shape.get("label", "")
                    group_id = shape.get("group_id")
                    
                    # 只收集有效卡牌且有区域信息的数据
                    if (self.is_valid_card_label(label) and 
                        group_id is not None and group_id != 0 and
                        len(shape.get("points", [])) >= 4):
                        
                        points = shape["points"]
                        x1, y1 = points[0]
                        x2, y2 = points[2]
                        bbox = [x1, y1, x2, y2]
                        
                        classifier.add_training_data(bbox, group_id, image_width, image_height)
                        training_count += 1
                        
            except Exception as e:
                print(f"    ❌ 收集训练数据时处理文件{json_file.name}出错: {e}")
        
        return training_count
    
    def validate_fixes(self) -> Dict[str, Any]:
        """验证修正结果"""
        print("\n✅ 验证修正结果...")
        
        validation_results = {
            "total_files": 0,
            "valid_card_with_regions": 0,
            "valid_card_without_regions": 0,
            "non_card_with_regions": 0,
            "non_card_without_regions": 0
        }
        
        json_files = list(self.enhanced_path.glob("*.json"))
        validation_results["total_files"] = len(json_files)
        
        for json_file in json_files[:20]:  # 验证前20个文件
            try:
                with open(json_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                for shape in data.get("shapes", []):
                    label = shape.get("label", "")
                    group_id = shape.get("group_id")
                    
                    is_valid_card = self.is_valid_card_label(label)
                    has_region = group_id is not None and group_id != 0
                    
                    if is_valid_card and has_region:
                        validation_results["valid_card_with_regions"] += 1
                    elif is_valid_card and not has_region:
                        validation_results["valid_card_without_regions"] += 1
                    elif not is_valid_card and has_region:
                        validation_results["non_card_with_regions"] += 1
                    elif not is_valid_card and not has_region:
                        validation_results["non_card_without_regions"] += 1
                        
            except Exception as e:
                print(f"    ❌ 验证文件{json_file.name}时出错: {e}")
        
        print(f"📊 验证结果:")
        print(f"  有效卡牌有区域: {validation_results['valid_card_with_regions']}")
        print(f"  有效卡牌无区域: {validation_results['valid_card_without_regions']}")
        print(f"  非卡牌有区域: {validation_results['non_card_with_regions']} ❌")
        print(f"  非卡牌无区域: {validation_results['non_card_without_regions']} ✅")
        
        return validation_results
    
    def run_complete_fix(self) -> Dict[str, Any]:
        """运行完整的修正流程"""
        print("🚀 修正calibration_gt_enhanced数据集")
        print("=" * 50)
        
        # 1. 分析问题
        issues = self.analyze_current_issues()
        
        # 2. 修正数据集
        fix_success = self.fix_enhanced_dataset()
        
        if not fix_success:
            return {"error": "fix_failed"}
        
        # 3. 验证修正结果
        validation_results = self.validate_fixes()
        
        # 保存结果
        results = {
            "fix_timestamp": "2025-01-17 09:00:00",
            "issues_found": issues,
            "fix_success": fix_success,
            "validation_results": validation_results,
            "recommendations": [
                "✅ 修正完成，非卡牌类别不再被分配区域",
                "🔍 建议使用AnyLabeling验证修正结果",
                "📊 重新训练分类器以获得更好性能",
                "🎯 专注于21个有效卡牌类别的区域分配"
            ]
        }
        
        output_path = "analysis/calibration_gt_fix_results.json"
        os.makedirs("analysis", exist_ok=True)
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        
        print(f"\n💾 修正结果已保存: {output_path}")
        
        return results

def main():
    """主修正函数"""
    fixer = CalibrationGTFixer()
    results = fixer.run_complete_fix()
    
    if "error" in results:
        print(f"❌ 修正失败: {results['error']}")
        return False
    
    # 检查修正效果
    validation = results["validation_results"]
    non_card_with_regions = validation.get("non_card_with_regions", 0)
    
    if non_card_with_regions == 0:
        print("\n🎉 修正成功！非卡牌类别不再被分配区域")
        return True
    else:
        print(f"\n⚠️ 仍有{non_card_with_regions}个非卡牌被分配区域，需要进一步检查")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
