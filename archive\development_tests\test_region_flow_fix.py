"""
测试修复后的区域流转逻辑
验证：1二1 → 1二2 → 1二4 → 1二5 （正确的区域流转）
而不是：1二1 → 2二2 → 3二4 → 4二5 （错误的递增）
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.core.digital_twin_v3 import DigitalTwinSystemV3

def test_region_flow_fix():
    """测试区域流转修复"""
    print("🧪 测试区域流转修复")
    print("期望：1二1 → 1二2 → 1二4 → 1二5")
    print("=" * 50)
    
    system = DigitalTwinSystemV3()
    
    # 第一帧：手牌区域1
    print("第一帧：手牌区域1")
    detections_frame1 = [
        {"label": "二", "bbox": [100, 100, 150, 150], "confidence": 0.9, "group_id": 1, "region_name": "手牌_观战方", "owner": "观战方"},
    ]
    
    result1 = system.process_frame(detections_frame1, frame_id=0)
    cards1 = result1["digital_twin_cards"]
    
    for card in cards1:
        print(f"  分配ID: {card.twin_id} (标签: {card.label}, 区域: {card.group_id})")
    
    # 第二帧：调整手牌区域2
    print("\n第二帧：调整手牌区域2")
    detections_frame2 = [
        {"label": "二", "bbox": [200, 200, 250, 250], "confidence": 0.9, "group_id": 2, "region_name": "调整手牌_观战方", "owner": "观战方"},
    ]
    
    result2 = system.process_frame(detections_frame2, frame_id=1)
    cards2 = result2["digital_twin_cards"]
    
    for card in cards2:
        print(f"  流转ID: {card.twin_id} (标签: {card.label}, 区域: {card.group_id}, 虚拟: {card.is_virtual})")
    
    # 第三帧：打牌区域4
    print("\n第三帧：打牌区域4")
    detections_frame3 = [
        {"label": "二", "bbox": [300, 300, 350, 350], "confidence": 0.9, "group_id": 4, "region_name": "打牌_观战方", "owner": "观战方"},
    ]
    
    result3 = system.process_frame(detections_frame3, frame_id=2)
    cards3 = result3["digital_twin_cards"]
    
    for card in cards3:
        print(f"  流转ID: {card.twin_id} (标签: {card.label}, 区域: {card.group_id}, 虚拟: {card.is_virtual})")
    
    # 第四帧：弃牌区域5
    print("\n第四帧：弃牌区域5")
    detections_frame4 = [
        {"label": "二", "bbox": [400, 400, 450, 450], "confidence": 0.9, "group_id": 5, "region_name": "弃牌_观战方", "owner": "观战方"},
    ]
    
    result4 = system.process_frame(detections_frame4, frame_id=3)
    cards4 = result4["digital_twin_cards"]
    
    for card in cards4:
        print(f"  流转ID: {card.twin_id} (标签: {card.label}, 区域: {card.group_id}, 虚拟: {card.is_virtual})")
    
    # 验证结果
    print("\n" + "=" * 50)
    print("验证结果:")
    
    expected_sequence = ["1二1", "1二2", "1二4", "1二5"]
    actual_sequence = []
    
    for i, result in enumerate([result1, result2, result3, result4]):
        physical_cards = [c for c in result["digital_twin_cards"] if not c.is_virtual]
        if physical_cards:
            actual_sequence.append(physical_cards[0].twin_id)
    
    print(f"期望序列: {expected_sequence}")
    print(f"实际序列: {actual_sequence}")
    
    if actual_sequence == expected_sequence:
        print("✅ 区域流转修复成功！")
        print("✅ 同一张物理牌正确保持相同的卡牌序号")
    else:
        print("❌ 区域流转修复失败")
        print("❌ 卡牌序号仍在错误递增")
    
    # 检查序号计数器
    print(f"\n序号计数器状态: {system.card_sequence_counters}")
    print(f"序号映射: {system.sequence_to_base_id}")
    
    return system

def test_multiple_cards_flow():
    """测试多张卡牌的区域流转"""
    print("\n🧪 测试多张卡牌的区域流转")
    print("=" * 50)
    
    system = DigitalTwinSystemV3()
    
    # 第一帧：两张不同的牌
    print("第一帧：两张不同的牌在手牌区域1")
    detections_frame1 = [
        {"label": "二", "bbox": [100, 100, 150, 150], "confidence": 0.9, "group_id": 1, "region_name": "手牌_观战方", "owner": "观战方"},
        {"label": "三", "bbox": [160, 100, 210, 150], "confidence": 0.9, "group_id": 1, "region_name": "手牌_观战方", "owner": "观战方"},
    ]
    
    result1 = system.process_frame(detections_frame1, frame_id=0)
    cards1 = result1["digital_twin_cards"]
    
    for card in cards1:
        print(f"  分配ID: {card.twin_id} (标签: {card.label})")
    
    # 第二帧：一张流转到区域2，一张留在区域1
    print("\n第二帧：二流转到调整区域2，三留在手牌区域1")
    detections_frame2 = [
        {"label": "二", "bbox": [200, 200, 250, 250], "confidence": 0.9, "group_id": 2, "region_name": "调整手牌_观战方", "owner": "观战方"},
        {"label": "三", "bbox": [160, 100, 210, 150], "confidence": 0.9, "group_id": 1, "region_name": "手牌_观战方", "owner": "观战方"},
    ]
    
    result2 = system.process_frame(detections_frame2, frame_id=1)
    cards2 = result2["digital_twin_cards"]
    
    for card in cards2:
        print(f"  ID: {card.twin_id} (标签: {card.label}, 区域: {card.group_id}, 虚拟: {card.is_virtual})")
    
    # 验证
    expected_ids = ["1二2", "2三1"]  # 二流转到区域2，三保持在区域1
    actual_ids = sorted([c.twin_id for c in cards2 if not c.is_virtual])
    
    print(f"\n期望ID: {sorted(expected_ids)}")
    print(f"实际ID: {actual_ids}")
    
    if actual_ids == sorted(expected_ids):
        print("✅ 多张卡牌区域流转测试通过！")
    else:
        print("❌ 多张卡牌区域流转测试失败")

def test_dark_card_flow():
    """测试暗牌的区域流转"""
    print("\n🧪 测试暗牌的区域流转")
    print("=" * 50)
    
    system = DigitalTwinSystemV3()
    
    # 第一帧：偎牌场景 - 1明2暗在吃碰区域6
    print("第一帧：偎牌场景在吃碰区域6")
    detections_frame1 = [
        {"label": "二", "bbox": [100, 100, 150, 150], "confidence": 0.9, "group_id": 6, "region_name": "吃碰区_观战方", "owner": "观战方"},
        {"label": "暗", "bbox": [160, 100, 210, 150], "confidence": 0.8, "group_id": 6, "region_name": "吃碰区_观战方", "owner": "观战方"},
        {"label": "暗", "bbox": [220, 100, 270, 150], "confidence": 0.8, "group_id": 6, "region_name": "吃碰区_观战方", "owner": "观战方"},
    ]
    
    result1 = system.process_frame(detections_frame1, frame_id=0)
    cards1 = result1["digital_twin_cards"]
    
    for card in cards1:
        print(f"  分配ID: {card.twin_id} (标签: {card.label}, 暗牌: {card.is_dark})")
    
    # 第二帧：整组流转到赢方区域14
    print("\n第二帧：整组流转到赢方区域14")
    detections_frame2 = [
        {"label": "二", "bbox": [300, 300, 350, 350], "confidence": 0.9, "group_id": 14, "region_name": "赢方区域", "owner": "观战方"},
        {"label": "暗", "bbox": [360, 300, 410, 350], "confidence": 0.8, "group_id": 14, "region_name": "赢方区域", "owner": "观战方"},
        {"label": "暗", "bbox": [420, 300, 470, 350], "confidence": 0.8, "group_id": 14, "region_name": "赢方区域", "owner": "观战方"},
    ]
    
    result2 = system.process_frame(detections_frame2, frame_id=1)
    cards2 = result2["digital_twin_cards"]
    
    for card in cards2:
        print(f"  流转ID: {card.twin_id} (标签: {card.label}, 区域: {card.group_id}, 暗牌: {card.is_dark})")
    
    # 验证暗牌流转
    expected_pattern = ["1二14", "2二暗14", "3二暗14"]  # 区域6 → 区域14
    actual_ids = sorted([c.twin_id for c in cards2 if not c.is_virtual])
    
    print(f"\n期望模式: {expected_pattern}")
    print(f"实际结果: {actual_ids}")
    
    if actual_ids == expected_pattern:
        print("✅ 暗牌区域流转测试通过！")
    else:
        print("❌ 暗牌区域流转测试失败")

def main():
    """运行所有测试"""
    print("🚀 区域流转修复测试")
    print("=" * 60)
    
    try:
        test_region_flow_fix()
        test_multiple_cards_flow()
        test_dark_card_flow()
        
        print("\n" + "=" * 60)
        print("🎉 所有区域流转测试完成！")
        
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
