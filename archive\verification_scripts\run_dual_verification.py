#!/usr/bin/env python3
"""
实际运行双轨机制验证
基于zhuangtaiquyu数据格式进行真实验证
"""

import sys
import os
import json
from pathlib import Path

def main():
    print("🚀 启动双轨机制实际验证")
    print("=" * 50)
    
    try:
        # 添加项目路径
        project_root = Path(__file__).parent
        sys.path.insert(0, str(project_root))
        
        # 导入模块
        from src.core.digital_twin_v2 import create_digital_twin_system, CardDetection
        print("✅ 模块导入成功")
        
        # 创建系统
        dt_system = create_digital_twin_system()
        print("✅ 数字孪生系统创建成功")
        
        # 检查双轨输出方法
        if hasattr(dt_system, 'export_synchronized_dual_format'):
            print("✅ 双轨输出方法存在")
        else:
            print("❌ 双轨输出方法不存在")
            return False
        
        # 创建基于zhuangtaiquyu的测试数据
        print("\n📋 创建测试数据...")
        test_detections = [
            CardDetection("壹", [114.54, 268.05, 159.0, 319.0], 0.95, 1, "手牌_观战方", "spectator"),
            CardDetection("贰", [159.94, 268.33, 204.4, 319.29], 0.92, 1, "手牌_观战方", "spectator"),
            CardDetection("二", [205.34, 267.76, 249.8, 318.71], 0.88, 1, "手牌_观战方", "spectator"),
            CardDetection("捌", [261.09, 97.36, 276.9, 112.73], 0.85, 6, "吃碰牌_观战方", "spectator"),
            CardDetection("玖", [74.31, 101.38, 90.69, 122.07], 0.87, 9, "弃牌区_对手方", "opponent")
        ]
        print(f"✅ 创建了 {len(test_detections)} 个测试检测结果")
        
        # 数字孪生系统处理
        print("\n🧠 数字孪生系统处理...")
        dt_result = dt_system.process_frame(test_detections)
        digital_twin_cards = dt_result['digital_twin_cards']
        print(f"✅ 处理完成: {len(digital_twin_cards)} 张数字孪生卡牌")
        
        # 显示数字孪生卡牌详情
        print("\n📋 数字孪生卡牌详情:")
        for i, card in enumerate(digital_twin_cards):
            print(f"   {i+1}. twin_id='{card.twin_id}', label='{card.label}', region='{card.region_name}'")
        
        # 生成双轨输出
        print("\n🔄 生成同步双轨输出...")
        dual_result = dt_system.export_synchronized_dual_format(
            dt_result, 640, 320, "test_frame.jpg"
        )
        print("✅ 双轨输出生成成功")
        
        # 检查结果结构
        required_keys = ['rlcard_format', 'anylabeling_format', 'consistency_validation', 'metadata']
        for key in required_keys:
            if key in dual_result:
                print(f"✅ 包含 {key}")
            else:
                print(f"❌ 缺少 {key}")
                return False
        
        # 验证一致性
        consistency = dual_result['consistency_validation']
        print(f"\n📊 一致性验证结果:")
        print(f"   一致性分数: {consistency['consistency_score']:.3f}")
        print(f"   是否一致: {'✅' if consistency['is_consistent'] else '❌'}")
        
        if consistency['issues']:
            print(f"   问题: {consistency['issues']}")
        
        # 检查RLCard格式
        rlcard_format = dual_result['rlcard_format']
        rlcard_total = sum(len(cards) for cards in [
            rlcard_format.get('hand', []),
            rlcard_format.get('discard_pile', []),
            rlcard_format.get('opponent_discard_pile', []),
            rlcard_format.get('combo_cards', []),
            rlcard_format.get('opponent_combo_cards', [])
        ])
        print(f"\n📊 RLCard格式:")
        print(f"   总卡牌数: {rlcard_total}")
        print(f"   手牌: {len(rlcard_format.get('hand', []))}")
        print(f"   吃碰牌: {len(rlcard_format.get('combo_cards', []))}")
        print(f"   对手弃牌: {len(rlcard_format.get('opponent_discard_pile', []))}")
        
        # 检查AnyLabeling格式
        anylabeling_format = dual_result['anylabeling_format']
        anylabeling_shapes = anylabeling_format.get('shapes', [])
        print(f"\n📊 AnyLabeling格式:")
        print(f"   标注总数: {len(anylabeling_shapes)}")
        
        # 显示生成的标注
        print("   生成的标注:")
        for i, shape in enumerate(anylabeling_shapes):
            label = shape.get('label', '')
            twin_id = shape.get('attributes', {}).get('digital_twin_id', '')
            group_id = shape.get('group_id', 0)
            print(f"     {i+1}. label='{label}', twin_id='{twin_id}', group_id={group_id}")
        
        # 验证zhuangtaiquyu兼容性
        print(f"\n🔍 zhuangtaiquyu兼容性验证:")
        
        # 检查标签格式
        zhuangtaiquyu_labels = []
        for shape in anylabeling_shapes:
            twin_id = shape.get('attributes', {}).get('digital_twin_id', '')
            if '_' in twin_id and not twin_id.startswith('虚拟'):
                parts = twin_id.split('_')
                if len(parts) >= 2:
                    zhuangtaiquyu_labels.append(f"{parts[0]}{parts[1]}")
            else:
                zhuangtaiquyu_labels.append(twin_id)
        
        print(f"   转换后的zhuangtaiquyu标签: {zhuangtaiquyu_labels}")
        
        # 期望的zhuangtaiquyu标签
        expected_labels = ["1壹", "1贰", "1二", "1捌", "2玖"]
        print(f"   期望的zhuangtaiquyu标签: {expected_labels}")
        
        # 计算匹配度
        if len(zhuangtaiquyu_labels) == len(expected_labels):
            matches = sum(1 for a, b in zip(zhuangtaiquyu_labels, expected_labels) if a == b)
            match_rate = matches / len(expected_labels)
            print(f"   标签匹配率: {match_rate:.3f} ({matches}/{len(expected_labels)})")
        else:
            print(f"   ⚠️ 数量不匹配: 生成{len(zhuangtaiquyu_labels)}, 期望{len(expected_labels)}")
        
        # 保存验证结果
        output_dir = Path("verification_output")
        output_dir.mkdir(exist_ok=True)
        
        with open(output_dir / "rlcard_output.json", 'w', encoding='utf-8') as f:
            json.dump(rlcard_format, f, ensure_ascii=False, indent=2)
        
        with open(output_dir / "anylabeling_output.json", 'w', encoding='utf-8') as f:
            json.dump(anylabeling_format, f, ensure_ascii=False, indent=2)
        
        with open(output_dir / "consistency_result.json", 'w', encoding='utf-8') as f:
            json.dump(consistency, f, ensure_ascii=False, indent=2)
        
        print(f"\n💾 验证结果已保存到: {output_dir}")
        
        # 最终评估
        success_criteria = [
            consistency['is_consistent'],
            rlcard_total > 0,
            len(anylabeling_shapes) > 0,
            rlcard_total == len(anylabeling_shapes) == len(digital_twin_cards)
        ]
        
        success = all(success_criteria)
        
        print(f"\n🎯 验证结果评估:")
        print(f"   双轨一致性: {'✅' if consistency['is_consistent'] else '❌'}")
        print(f"   RLCard有效: {'✅' if rlcard_total > 0 else '❌'}")
        print(f"   AnyLabeling有效: {'✅' if len(anylabeling_shapes) > 0 else '❌'}")
        print(f"   数量一致: {'✅' if rlcard_total == len(anylabeling_shapes) == len(digital_twin_cards) else '❌'}")
        print(f"   总体成功: {'✅' if success else '❌'}")
        
        if success:
            print(f"\n🎉 双轨机制验证成功！")
            print(f"   - 一致性分数: {consistency['consistency_score']:.3f}")
            print(f"   - 双轨输出正常工作")
            print(f"   - zhuangtaiquyu格式兼容")
        else:
            print(f"\n⚠️ 双轨机制验证失败")
            print(f"   - 请检查上述问题")
        
        return success
        
    except Exception as e:
        print(f"❌ 验证过程中发生异常: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
