# 🔧 生产脚本

本目录包含生产环境使用的脚本和工具。

## 📁 目录结构

### production/
生产就绪的稳定脚本
- `calibration_gt_processor.py` - 生产级calibration_gt处理器
- `dual_format_generator.py` - 生产级双格式生成器
- `batch_processor.py` - 生产级批量处理器

### maintenance/
系统维护脚本
- `batch_verification.py` - 批量验证脚本
- `data_validation.py` - 数据验证脚本
- `system_health_check.py` - 系统健康检查
- `performance_monitor.py` - 性能监控脚本

### utilities/
实用工具脚本
- `quick_test.py` - 快速测试工具
- `format_converter.py` - 格式转换工具
- `config_manager.py` - 配置管理工具
- `log_analyzer.py` - 日志分析工具

## 🚀 使用指南

### 生产环境部署
```bash
# 运行生产级处理器
python scripts/production/calibration_gt_processor.py \
  --config production_config.json \
  --input_dir /data/input \
  --output_dir /data/output

# 批量处理
python scripts/production/batch_processor.py \
  --batch_size 32 \
  --parallel_workers 4
```

### 系统维护
```bash
# 系统健康检查
python scripts/maintenance/system_health_check.py

# 数据验证
python scripts/maintenance/data_validation.py \
  --data_dir /data/production \
  --report_dir /reports

# 性能监控
python scripts/maintenance/performance_monitor.py \
  --duration 3600 \
  --interval 60
```

### 实用工具
```bash
# 快速测试
python scripts/utilities/quick_test.py \
  --test_type detection \
  --sample_size 10

# 格式转换
python scripts/utilities/format_converter.py \
  --input_format rlcard \
  --output_format anylabeling \
  --input_file data.json
```

## 📋 脚本特点

### 生产级质量
- **稳定性**: 经过充分测试，适合生产环境
- **性能**: 优化的性能，支持大规模数据处理
- **错误处理**: 完善的错误处理和恢复机制
- **监控**: 内置监控和日志记录功能

### 配置管理
- **配置文件**: 支持JSON/YAML配置文件
- **环境变量**: 支持环境变量配置
- **命令行参数**: 灵活的命令行参数
- **默认值**: 合理的默认配置

### 安全性
- **输入验证**: 严格的输入参数验证
- **权限检查**: 文件和目录权限检查
- **资源限制**: 内存和CPU使用限制
- **日志安全**: 敏感信息不记录在日志中

## ⚠️ 使用注意事项

### 环境要求
- **Python版本**: Python 3.10+
- **依赖安装**: 确保所有生产依赖已安装
- **硬件要求**: 根据脚本需求配置合适的硬件
- **权限设置**: 确保脚本有必要的执行权限

### 数据安全
- **备份策略**: 处理前备份重要数据
- **访问控制**: 限制对敏感数据的访问
- **传输安全**: 使用安全的数据传输方式
- **存储加密**: 敏感数据加密存储

### 监控和维护
- **日志监控**: 定期检查脚本执行日志
- **性能监控**: 监控脚本性能和资源使用
- **错误告警**: 设置错误告警机制
- **定期更新**: 定期更新脚本和依赖

## 📊 性能基准

### 处理能力
- **calibration_gt处理**: 372张图像/小时
- **批量处理**: 1000张图像/小时 (GPU)
- **格式转换**: 10000个文件/小时
- **数据验证**: 5000个文件/小时

### 资源使用
- **内存使用**: 通常<4GB
- **GPU显存**: <6GB (使用GPU时)
- **磁盘IO**: 优化的批量读写
- **网络带宽**: 最小化网络使用

## 🔧 配置示例

### 生产配置 (production_config.json)
```json
{
  "processing": {
    "batch_size": 32,
    "parallel_workers": 4,
    "gpu_enabled": true,
    "memory_limit": "8GB"
  },
  "output": {
    "format": "dual",
    "compression": true,
    "backup": true
  },
  "monitoring": {
    "log_level": "INFO",
    "metrics_enabled": true,
    "alert_threshold": 0.95
  }
}
```

### 维护配置 (maintenance_config.json)
```json
{
  "health_check": {
    "check_interval": 300,
    "alert_email": "<EMAIL>",
    "critical_threshold": 0.9
  },
  "data_validation": {
    "validation_rules": "strict",
    "report_format": "detailed",
    "auto_fix": false
  }
}
```

## 🆘 故障排除

### 常见问题
1. **权限错误**: 检查文件和目录权限
2. **内存不足**: 减少batch_size或增加内存
3. **GPU不可用**: 检查CUDA环境和驱动
4. **配置错误**: 验证配置文件格式和内容

### 紧急处理
- **脚本中断**: 检查日志确定中断原因
- **数据损坏**: 从备份恢复数据
- **性能下降**: 检查系统资源使用情况
- **错误率高**: 检查输入数据质量

---

**🎯 重要**: 生产环境使用前请充分测试，确保脚本稳定可靠。
